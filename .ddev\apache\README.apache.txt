#ddev-generated
The .ddev/apache directory contains a generated apache-site.conf file
possibly specially adapted for the specific project type chosen in .ddev/config.yaml.
and it handles most projects on ddev, including those with multiple
hostnames, etc.

However, if you have very specific needs for configuration, you can edit
the apache-site.conf file and remove the #ddev-generated line in it and change
as you see fit. Use `ddev start` to restart.

You can also add more configurations, for example with separate configurations
for each site, as demonstrated by the second_docroot.conf.example, which shows how to have apache serve completely different configurations for a named site that is different from the default.

The files will be copied into /etc/apache2/sites-enabled directory.
