# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-05-08T10:20:52+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: pt_PT\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: src/Site_Health/Site_Health.php:517
msgid "Number of Field Groups with Blocks and Other Locations"
msgstr ""

#: src/Site_Health/Site_Health.php:512
msgid "Number of Field Groups with Multiple Block Locations"
msgstr ""

#: src/Site_Health/Site_Health.php:507
msgid "Number of Field Groups with a Single Block Location"
msgstr ""

#: src/Site_Health/Site_Health.php:476
msgid "All Location Rules"
msgstr ""

#: includes/validation.php:144
msgid "Learn more"
msgstr ""

#: includes/validation.php:133
msgid ""
"ACF was unable to perform validation because the provided nonce failed "
"verification."
msgstr ""

#: includes/validation.php:131
msgid ""
"ACF was unable to perform validation because no nonce was received by the "
"server."
msgstr ""

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:324
msgid "are developed and maintained by"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:850
#: includes/admin/views/acf-taxonomy/advanced-settings.php:810
msgid "By default only admin users can edit this setting."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:848
#: includes/admin/views/acf-taxonomy/advanced-settings.php:808
msgid "By default only super admin users can edit this setting."
msgstr ""

#: includes/admin/views/acf-field-group/field.php:322
msgid "Close and Add Field"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:804
msgid ""
"A PHP function name to be called to handle the content of a meta box on your "
"taxonomy. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:842
msgid ""
"A PHP function name to be called when setting up the meta boxes for the edit "
"screen. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""

#: includes/fields/class-acf-field.php:359
msgid "Allow Access to Value in Editor UI"
msgstr ""

#: includes/fields/class-acf-field.php:341
msgid "Learn more."
msgstr ""

#. translators: %s A "Learn More" link to documentation explaining the setting
#. further.
#: includes/fields/class-acf-field.php:340
msgid ""
"Allow content editors to access and display the field value in the editor UI "
"using Block Bindings or the ACF Shortcode. %s"
msgstr ""

#: src/Blocks/Bindings.php:67
msgid ""
"The requested ACF field type does not support output in Block Bindings or "
"the ACF shortcode."
msgstr ""

#: includes/api/api-template.php:1085 src/Blocks/Bindings.php:75
msgid ""
"The requested ACF field is not allowed to be output in bindings or the ACF "
"Shortcode."
msgstr ""

#: includes/api/api-template.php:1077
msgid ""
"The requested ACF field type does not support output in bindings or the ACF "
"Shortcode."
msgstr ""

#: includes/api/api-template.php:1054
msgid "[The ACF shortcode cannot display fields from non-public posts]"
msgstr ""

#: includes/api/api-template.php:1011
msgid "[The ACF shortcode is disabled on this site]"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:451
msgid "Businessman Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:443
msgid "Forums Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:722
msgid "YouTube Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:721
msgid "Yes (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:719
msgid "Xing Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:718
msgid "WordPress (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:716
msgid "WhatsApp Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:715
msgid "Write Blog Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:714
msgid "Widgets Menus Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:713
msgid "View Site Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:712
msgid "Learn More Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:710
msgid "Add Page Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:707
msgid "Video (alt3) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:706
msgid "Video (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:705
msgid "Video (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:702
msgid "Update (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:699
msgid "Universal Access (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:696
msgid "Twitter (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:694
msgid "Twitch Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:691
msgid "Tide Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:690
msgid "Tickets (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:686
msgid "Text Page Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:680
msgid "Table Row Delete Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:679
msgid "Table Row Before Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:678
msgid "Table Row After Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:677
msgid "Table Col Delete Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:676
msgid "Table Col Before Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:675
msgid "Table Col After Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:674
msgid "Superhero (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:673
msgid "Superhero Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:667
msgid "Spotify Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:661
msgid "Shortcode Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:660
msgid "Shield (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:658
msgid "Share (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:657
msgid "Share (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:652
msgid "Saved Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:651
msgid "RSS Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:650
msgid "REST API Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:649
msgid "Remove Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:647
msgid "Reddit Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:644
msgid "Privacy Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:643
msgid "Printer Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:639
msgid "Podio Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:638
msgid "Plus (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:637
msgid "Plus (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:635
msgid "Plugins Checked Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:632
msgid "Pinterest Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:630
msgid "Pets Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:628
msgid "PDF Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:626
msgid "Palm Tree Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:625
msgid "Open Folder Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:624
msgid "No (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:619
msgid "Money (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:614
msgid "Menu (alt3) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:613
msgid "Menu (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:612
msgid "Menu (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:607
msgid "Spreadsheet Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:606
msgid "Interactive Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:605
msgid "Document Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:604
msgid "Default Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:598
msgid "Location (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:595
msgid "LinkedIn Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:590
msgid "Instagram Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:589
msgid "Insert Before Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:588
msgid "Insert After Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:587
msgid "Insert Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:586
msgid "Info Outline Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:583
msgid "Images (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:582
msgid "Images (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:581
msgid "Rotate Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:580
msgid "Rotate Left Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:579
msgid "Rotate Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:578
msgid "Flip Vertical Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:577
msgid "Flip Horizontal Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:575
msgid "Crop Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:574
msgid "ID (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:572
msgid "HTML Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:571
msgid "Hourglass Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:568
msgid "Heading Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:564
msgid "Google Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:563
msgid "Games Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:562
msgid "Fullscreen Exit (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:561
msgid "Fullscreen (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:558
msgid "Status Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:556
msgid "Image Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:555
msgid "Gallery Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:554
msgid "Chat Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:553
#: includes/fields/class-acf-field-icon_picker.php:602
msgid "Audio Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:552
msgid "Aside Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:551
msgid "Food Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:544
msgid "Exit Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:543
msgid "Excerpt View Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:542
msgid "Embed Video Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:541
msgid "Embed Post Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:540
msgid "Embed Photo Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:539
msgid "Embed Generic Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:538
msgid "Embed Audio Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:537
msgid "Email (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:534
msgid "Ellipsis Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:530
msgid "Unordered List Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:525
msgid "RTL Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:518
msgid "Ordered List RTL Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:517
msgid "Ordered List Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:516
msgid "LTR Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:508
msgid "Custom Character Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:500
msgid "Edit Page Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:499
msgid "Edit Large Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:497
msgid "Drumstick Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:493
msgid "Database View Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:492
msgid "Database Remove Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:491
msgid "Database Import Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:490
msgid "Database Export Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:489
msgid "Database Add Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:488
msgid "Database Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:486
msgid "Cover Image Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:485
msgid "Volume On Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:484
msgid "Volume Off Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:483
msgid "Skip Forward Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:482
msgid "Skip Back Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:481
msgid "Repeat Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:480
msgid "Play Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:479
msgid "Pause Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:478
msgid "Forward Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:477
msgid "Back Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:476
msgid "Columns Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:475
msgid "Color Picker Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:474
msgid "Coffee Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:473
msgid "Code Standards Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:472
msgid "Cloud Upload Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:471
msgid "Cloud Saved Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:460
msgid "Car Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:459
msgid "Camera (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:455
msgid "Calculator Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:454
msgid "Button Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:452
msgid "Businessperson Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:449
msgid "Tracking Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:448
msgid "Topics Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:447
msgid "Replies Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:446
msgid "PM Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:444
msgid "Friends Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:442
msgid "Community Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:441
msgid "BuddyPress Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:440
msgid "bbPress Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:439
msgid "Activity Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:438
msgid "Book (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:436
msgid "Block Default Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:435
msgid "Bell Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:434
msgid "Beer Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:433
msgid "Bank Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:429
msgid "Arrow Up (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:428
msgid "Arrow Up (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:426
msgid "Arrow Right (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:425
msgid "Arrow Right (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:423
msgid "Arrow Left (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:422
msgid "Arrow Left (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:420
msgid "Arrow Down (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:419
msgid "Arrow Down (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:415
msgid "Amazon Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:414
msgid "Align Wide Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:412
msgid "Align Pull Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:411
msgid "Align Pull Left Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:408
msgid "Align Full Width Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:405
msgid "Airplane Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:402
msgid "Site (alt3) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:401
msgid "Site (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:400
msgid "Site (alt) Icon"
msgstr ""

#: includes/admin/views/options-page-preview.php:26
msgid "Upgrade to ACF PRO to create options pages in just a few clicks"
msgstr ""

#: includes/ajax/class-acf-ajax-query-users.php:24
msgid "Invalid request args."
msgstr ""

#: includes/ajax/class-acf-ajax-check-screen.php:37
#: includes/ajax/class-acf-ajax-local-json-diff.php:37
#: includes/ajax/class-acf-ajax-query-users.php:33
#: includes/ajax/class-acf-ajax-upgrade.php:24
#: includes/ajax/class-acf-ajax-user-setting.php:38
msgid "Sorry, you do not have permission to do that."
msgstr ""

#: src/Site_Health/Site_Health.php:715
msgid "Blocks Using Post Meta"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:25
#: includes/admin/views/acf-field-group/pro-features.php:27
#: includes/admin/views/global/header.php:27
msgid "ACF PRO logo"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:37
msgid "ACF PRO Logo"
msgstr ""

#. translators: %s - field/param name
#: includes/fields/class-acf-field-icon_picker.php:788
msgid "%s requires a valid attachment ID when type is set to media_library."
msgstr ""

#. translators: %s - field name
#: includes/fields/class-acf-field-icon_picker.php:772
msgid "%s is a required property of acf."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:748
msgid "The value of icon to save."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:742
msgid "The type of icon to save."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:720
msgid "Yes Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:717
msgid "WordPress Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:709
msgid "Warning Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:708
msgid "Visibility Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:704
msgid "Vault Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:703
msgid "Upload Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:701
msgid "Update Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:700
msgid "Unlock Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:698
msgid "Universal Access Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:697
msgid "Undo Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:695
msgid "Twitter Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:693
msgid "Trash Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:692
msgid "Translation Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:689
msgid "Tickets Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:688
msgid "Thumbs Up Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:687
msgid "Thumbs Down Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:608
#: includes/fields/class-acf-field-icon_picker.php:685
msgid "Text Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:684
msgid "Testimonial Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:683
msgid "Tagcloud Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:682
msgid "Tag Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:681
msgid "Tablet Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:672
msgid "Store Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:671
msgid "Sticky Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:670
msgid "Star Half Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:669
msgid "Star Filled Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:668
msgid "Star Empty Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:666
msgid "Sos Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:665
msgid "Sort Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:664
msgid "Smiley Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:663
msgid "Smartphone Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:662
msgid "Slides Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:659
msgid "Shield Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:656
msgid "Share Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:655
msgid "Search Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:654
msgid "Screen Options Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:653
msgid "Schedule Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:648
msgid "Redo Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:646
msgid "Randomize Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:645
msgid "Products Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:642
msgid "Pressthis Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:641
msgid "Post Status Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:640
msgid "Portfolio Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:636
msgid "Plus Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:634
msgid "Playlist Video Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:633
msgid "Playlist Audio Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:631
msgid "Phone Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:629
msgid "Performance Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:627
msgid "Paperclip Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:623
msgid "No Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:622
msgid "Networking Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:621
msgid "Nametag Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:620
msgid "Move Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:618
msgid "Money Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:617
msgid "Minus Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:616
msgid "Migrate Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:615
msgid "Microphone Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:610
msgid "Megaphone Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:600
msgid "Marker Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:599
msgid "Lock Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:597
msgid "Location Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:596
msgid "List View Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:594
msgid "Lightbulb Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:593
msgid "Left Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:592
msgid "Layout Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:591
msgid "Laptop Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:585
msgid "Info Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:584
msgid "Index Card Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:573
msgid "ID Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:570
msgid "Hidden Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:569
msgid "Heart Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:567
msgid "Hammer Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:445
#: includes/fields/class-acf-field-icon_picker.php:566
msgid "Groups Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:565
msgid "Grid View Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:560
msgid "Forms Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:550
msgid "Flag Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:549
#: includes/fields/class-acf-field-icon_picker.php:576
msgid "Filter Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:548
msgid "Feedback Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:547
msgid "Facebook (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:546
msgid "Facebook Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:545
msgid "External Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:536
msgid "Email (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:535
msgid "Email Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:533
#: includes/fields/class-acf-field-icon_picker.php:559
#: includes/fields/class-acf-field-icon_picker.php:609
msgid "Video Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:532
msgid "Unlink Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:531
msgid "Underline Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:529
msgid "Text Color Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:528
msgid "Table Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:527
msgid "Strikethrough Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:526
msgid "Spellcheck Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:524
msgid "Remove Formatting Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:523
#: includes/fields/class-acf-field-icon_picker.php:557
msgid "Quote Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:522
msgid "Paste Word Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:521
msgid "Paste Text Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:520
msgid "Paragraph Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:519
msgid "Outdent Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:515
msgid "Kitchen Sink Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:514
msgid "Justify Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:513
msgid "Italic Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:512
msgid "Insert More Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:511
msgid "Indent Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:510
msgid "Help Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:509
msgid "Expand Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:507
msgid "Contract Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:506
#: includes/fields/class-acf-field-icon_picker.php:603
msgid "Code Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:505
msgid "Break Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:504
msgid "Bold Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:498
msgid "Edit Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:496
msgid "Download Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:495
msgid "Dismiss Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:494
msgid "Desktop Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:487
msgid "Dashboard Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:470
msgid "Cloud Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:469
msgid "Clock Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:468
msgid "Clipboard Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:467
msgid "Chart Pie Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:466
msgid "Chart Line Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:465
msgid "Chart Bar Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:464
msgid "Chart Area Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:463
msgid "Category Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:462
msgid "Cart Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:461
msgid "Carrot Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:458
msgid "Camera Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:457
msgid "Calendar (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:456
msgid "Calendar Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:453
msgid "Businesswoman Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:450
msgid "Building Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:437
msgid "Book Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:432
msgid "Backup Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:431
msgid "Awards Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:430
msgid "Art Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:427
msgid "Arrow Up Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:424
msgid "Arrow Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:421
msgid "Arrow Left Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:418
msgid "Arrow Down Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:417
#: includes/fields/class-acf-field-icon_picker.php:601
msgid "Archive Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:416
msgid "Analytics Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:413
#: includes/fields/class-acf-field-icon_picker.php:503
msgid "Align Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:410
msgid "Align None Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:409
#: includes/fields/class-acf-field-icon_picker.php:502
msgid "Align Left Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:407
#: includes/fields/class-acf-field-icon_picker.php:501
msgid "Align Center Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:406
msgid "Album Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:404
msgid "Users Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:403
msgid "Tools Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:399
msgid "Site Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:398
msgid "Settings Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:397
msgid "Post Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:396
msgid "Plugins Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:395
msgid "Page Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:394
msgid "Network Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:393
msgid "Multisite Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:392
msgid "Media Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:391
msgid "Links Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:390
msgid "Home Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:388
msgid "Customizer Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:387
#: includes/fields/class-acf-field-icon_picker.php:711
msgid "Comments Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:386
msgid "Collapse Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:385
msgid "Appearance Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:389
msgid "Generic Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:321
msgid "Icon picker requires a value."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:316
msgid "Icon picker requires an icon type."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:285
msgid ""
"The available icons matching your search query have been updated in the icon "
"picker below."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:284
msgid "No results found for that search term"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:266
msgid "Array"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:265
msgid "String"
msgstr ""

#. translators: %s - link to documentation
#: includes/fields/class-acf-field-icon_picker.php:253
msgid "Specify the return format for the icon. %s"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:238
msgid "Select where content editors can choose the icon from."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:211
msgid "The URL to the icon you'd like to use, or svg as Data URI"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:194
msgid "Browse Media Library"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:185
msgid "The currently selected image preview"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:176
msgid "Click to change the icon in the Media Library"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:142
msgid "Search icons..."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:53
msgid "Media Library"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:49
msgid "Dashicons"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:26
msgid ""
"An interactive UI for selecting an icon. Select from Dashicons, the media "
"library, or a standalone URL input."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:23
msgid "Icon Picker"
msgstr ""

#: src/Site_Health/Site_Health.php:776
msgid "JSON Load Paths"
msgstr ""

#: src/Site_Health/Site_Health.php:770
msgid "JSON Save Paths"
msgstr ""

#: src/Site_Health/Site_Health.php:761
msgid "Registered ACF Forms"
msgstr ""

#: src/Site_Health/Site_Health.php:755
msgid "Shortcode Enabled"
msgstr ""

#: src/Site_Health/Site_Health.php:747
msgid "Field Settings Tabs Enabled"
msgstr ""

#: src/Site_Health/Site_Health.php:739
msgid "Field Type Modal Enabled"
msgstr ""

#: src/Site_Health/Site_Health.php:731
msgid "Admin UI Enabled"
msgstr ""

#: src/Site_Health/Site_Health.php:722
msgid "Block Preloading Enabled"
msgstr ""

#: src/Site_Health/Site_Health.php:710
msgid "Blocks Per ACF Block Version"
msgstr ""

#: src/Site_Health/Site_Health.php:705
msgid "Blocks Per API Version"
msgstr ""

#: src/Site_Health/Site_Health.php:678
msgid "Registered ACF Blocks"
msgstr ""

#: src/Site_Health/Site_Health.php:672
msgid "Light"
msgstr ""

#: src/Site_Health/Site_Health.php:672
msgid "Standard"
msgstr ""

#: src/Site_Health/Site_Health.php:671
msgid "REST API Format"
msgstr ""

#: src/Site_Health/Site_Health.php:663
msgid "Registered Options Pages (PHP)"
msgstr ""

#: src/Site_Health/Site_Health.php:649
msgid "Registered Options Pages (JSON)"
msgstr ""

#: src/Site_Health/Site_Health.php:644
msgid "Registered Options Pages (UI)"
msgstr ""

#: src/Site_Health/Site_Health.php:614
msgid "Options Pages UI Enabled"
msgstr ""

#: src/Site_Health/Site_Health.php:606
msgid "Registered Taxonomies (JSON)"
msgstr ""

#: src/Site_Health/Site_Health.php:594
msgid "Registered Taxonomies (UI)"
msgstr ""

#: src/Site_Health/Site_Health.php:582
msgid "Registered Post Types (JSON)"
msgstr ""

#: src/Site_Health/Site_Health.php:570
msgid "Registered Post Types (UI)"
msgstr ""

#: src/Site_Health/Site_Health.php:557
msgid "Post Types and Taxonomies Enabled"
msgstr ""

#: src/Site_Health/Site_Health.php:550
msgid "Number of Third Party Fields by Field Type"
msgstr ""

#: src/Site_Health/Site_Health.php:545
msgid "Number of Fields by Field Type"
msgstr ""

#: src/Site_Health/Site_Health.php:444
msgid "Field Groups Enabled for GraphQL"
msgstr ""

#: src/Site_Health/Site_Health.php:431
msgid "Field Groups Enabled for REST API"
msgstr ""

#: src/Site_Health/Site_Health.php:419
msgid "Registered Field Groups (JSON)"
msgstr ""

#: src/Site_Health/Site_Health.php:407
msgid "Registered Field Groups (PHP)"
msgstr ""

#: src/Site_Health/Site_Health.php:395
msgid "Registered Field Groups (UI)"
msgstr ""

#: src/Site_Health/Site_Health.php:383
msgid "Active Plugins"
msgstr ""

#: src/Site_Health/Site_Health.php:357
msgid "Parent Theme"
msgstr ""

#: src/Site_Health/Site_Health.php:346
msgid "Active Theme"
msgstr ""

#: src/Site_Health/Site_Health.php:337
msgid "Is Multisite"
msgstr ""

#: src/Site_Health/Site_Health.php:332
msgid "MySQL Version"
msgstr ""

#: src/Site_Health/Site_Health.php:327
msgid "WordPress Version"
msgstr ""

#: src/Site_Health/Site_Health.php:320
msgid "Subscription Expiry Date"
msgstr ""

#: src/Site_Health/Site_Health.php:312
msgid "License Status"
msgstr ""

#: src/Site_Health/Site_Health.php:307
msgid "License Type"
msgstr ""

#: src/Site_Health/Site_Health.php:302
msgid "Licensed URL"
msgstr ""

#: src/Site_Health/Site_Health.php:296
msgid "License Activated"
msgstr ""

#: src/Site_Health/Site_Health.php:290
msgid "Free"
msgstr ""

#: src/Site_Health/Site_Health.php:289
msgid "Plugin Type"
msgstr ""

#: src/Site_Health/Site_Health.php:284
msgid "Plugin Version"
msgstr ""

#: src/Site_Health/Site_Health.php:255
msgid ""
"This section contains debug information about your ACF configuration which "
"can be useful to provide to support."
msgstr ""

#: includes/assets.php:373
msgid "An ACF Block on this page requires attention before you can save."
msgstr ""

#. translators: %s - The clear log button opening HTML tag. %s - The closing
#. HTML tag.
#: includes/admin/views/escaped-html-notice.php:63
msgid ""
"This data is logged as we detect values that have been changed during "
"output. %1$sClear log and dismiss%2$s after escaping the values in your "
"code. The notice will reappear if we detect changed values again."
msgstr ""

#: includes/admin/views/escaped-html-notice.php:25
msgid "Dismiss permanently"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:220
msgid "Instructions for content editors. Shown when submitting data."
msgstr ""

#: includes/admin/post-types/admin-field-group.php:143
msgid "Has no term selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:142
msgid "Has any term selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:141
msgid "Terms do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:140
msgid "Terms contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:139
msgid "Term is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:138
msgid "Term is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:137
msgid "Has no user selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:136
msgid "Has any user selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:135
msgid "Users do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:134
msgid "Users contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:133
msgid "User is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:132
msgid "User is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:131
msgid "Has no page selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:130
msgid "Has any page selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:129
msgid "Pages do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:128
msgid "Pages contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:127
msgid "Page is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:126
msgid "Page is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:125
msgid "Has no relationship selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:124
msgid "Has any relationship selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:123
msgid "Has no post selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:122
msgid "Has any post selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:121
msgid "Posts do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:120
msgid "Posts contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:119
msgid "Post is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:118
msgid "Post is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:117
msgid "Relationships do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:116
msgid "Relationships contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:115
msgid "Relationship is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:114
msgid "Relationship is equal to"
msgstr ""

#: src/Blocks/Bindings.php:38
msgctxt "The core ACF block binding source name for fields on the current page"
msgid "ACF Fields"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:14
msgid "ACF PRO Feature"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:10
msgid "Renew PRO to Unlock"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:8
msgid "Renew PRO License"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:41
msgid "PRO fields cannot be edited without an active license."
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:232
msgid ""
"Please activate your ACF PRO license to edit field groups assigned to an ACF "
"Block."
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:231
msgid "Please activate your ACF PRO license to edit this options page."
msgstr ""

#: includes/api/api-template.php:385 includes/api/api-template.php:439
msgid ""
"Returning escaped HTML values is only possible when format_value is also "
"true. The field values have not been returned for security."
msgstr ""

#: includes/api/api-template.php:46 includes/api/api-template.php:251
#: includes/api/api-template.php:947
msgid ""
"Returning an escaped HTML value is only possible when format_value is also "
"true. The field value has not been returned for security."
msgstr ""

#. translators: %1$s - name of the ACF plugin. %2$s - Link to documentation.
#: includes/admin/views/escaped-html-notice.php:32
msgid ""
"%1$s ACF now automatically escapes unsafe HTML when rendered by "
"<code>the_field</code> or the ACF shortcode. We've detected the output of "
"some of your fields has been modified by this change, but this may not be a "
"breaking change. %2$s."
msgstr ""

#: includes/admin/views/escaped-html-notice.php:27
msgid "Please contact your site administrator or developer for more details."
msgstr ""

#: includes/admin/views/escaped-html-notice.php:5
msgid "Learn&nbsp;more"
msgstr ""

#: includes/admin/admin.php:63
msgid "Hide&nbsp;details"
msgstr ""

#: includes/admin/admin.php:62 includes/admin/views/escaped-html-notice.php:24
msgid "Show&nbsp;details"
msgstr ""

#. translators: %1$s - The selector used  %2$s The field name  3%$s The parent
#. function name
#: includes/admin/views/escaped-html-notice.php:49
msgid "%1$s (%2$s) - rendered via %3$s"
msgstr ""

#: includes/admin/views/global/navigation.php:226
msgid "Renew ACF PRO License"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:17
msgid "Renew License"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:14
msgid "Manage License"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:102
msgid "'High' position not supported in the Block Editor"
msgstr ""

#: includes/admin/views/options-page-preview.php:30
msgid "Upgrade to ACF PRO"
msgstr ""

#. translators: %s URL to ACF options pages documentation
#: includes/admin/views/options-page-preview.php:7
msgid ""
"ACF <a href=\"%s\" target=\"_blank\">options pages</a> are custom admin "
"pages for managing global settings via fields. You can create multiple pages "
"and sub-pages."
msgstr ""

#: includes/admin/views/global/header.php:35
msgid "Add Options Page"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:708
msgid "In the editor used as the placeholder of the title."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:707
msgid "Title Placeholder"
msgstr ""

#: includes/admin/views/global/navigation.php:97
msgid "4 Months Free"
msgstr ""

#. translators: %s - A singular label for a post type or taxonomy.
#: includes/admin/views/global/form-top.php:59
msgid "(Duplicated from %s)"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:289
msgid "Select Options Pages"
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:107
msgid "Duplicate taxonomy"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:106
#: includes/admin/post-types/admin-taxonomy.php:106
msgid "Create taxonomy"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:105
msgid "Duplicate post type"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:104
#: includes/admin/post-types/admin-taxonomy.php:108
msgid "Create post type"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:103
#: includes/admin/post-types/admin-taxonomy.php:105
msgid "Link field groups"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:102
#: includes/admin/post-types/admin-taxonomy.php:104
msgid "Add fields"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:147
msgid "This Field"
msgstr ""

#: includes/admin/admin.php:361
msgid "ACF PRO"
msgstr ""

#: includes/admin/admin.php:359
msgid "Feedback"
msgstr ""

#: includes/admin/admin.php:357
msgid "Support"
msgstr ""

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:332
msgid "is developed and maintained by"
msgstr ""

#. translators: %s - either "post type" or "taxonomy"
#: includes/admin/admin-internal-post-type.php:313
msgid "Add this %s to the location rules of the selected field groups."
msgstr ""

#. translators: %s the URL to ACF's bidirectional relationship documentation
#: includes/acf-bidirectional-functions.php:272
msgid ""
"Enabling the bidirectional setting allows you to update a value in the "
"target fields for each value selected for this field, adding or removing the "
"Post ID, Taxonomy ID or User ID of the item being updated. For more "
"information, please read the <a href=\"%s\" target=\"_blank\">documentation</"
"a>."
msgstr ""

#: includes/acf-bidirectional-functions.php:248
msgid ""
"Select field(s) to store the reference back to the item being updated. You "
"may select this field. Target fields must be compatible with where this "
"field is being displayed. For example, if this field is displayed on a "
"Taxonomy, your target field should be of type Taxonomy"
msgstr ""

#: includes/acf-bidirectional-functions.php:247
msgid "Target Field"
msgstr ""

#: includes/acf-bidirectional-functions.php:221
msgid "Update a field on the selected values, referencing back to this ID"
msgstr ""

#: includes/acf-bidirectional-functions.php:220
msgid "Bidirectional"
msgstr ""

#. translators: %s A field type name, such as "Relationship"
#: includes/acf-bidirectional-functions.php:193
msgid "%s Field"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:498
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-select.php:378
#: includes/fields/class-acf-field-user.php:111
msgid "Select Multiple"
msgstr "Seleccionar múltiplos"

#: includes/admin/views/global/navigation.php:238
msgid "WP Engine logo"
msgstr "Logótipo da WP Engine"

#: includes/admin/views/acf-taxonomy/basic-settings.php:58
msgid "Lower case letters, underscores and dashes only, Max 32 characters."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1156
msgid "The capability name for assigning terms of this taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1155
msgid "Assign Terms Capability"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1139
msgid "The capability name for deleting terms of this taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1138
msgid "Delete Terms Capability"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1122
msgid "The capability name for editing terms of this taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1121
msgid "Edit Terms Capability"
msgstr "Editar capacidade dos termos"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1105
msgid "The capability name for managing terms of this taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1104
msgid "Manage Terms Capability"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:929
msgid ""
"Sets whether posts should be excluded from search results and taxonomy "
"archive pages."
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:78
msgid "More Tools from WP Engine"
msgstr "Mais ferramentas da WP Engine"

#. translators: %s - WP Engine logo
#: includes/admin/views/acf-field-group/pro-features.php:73
msgid "Built for those that build with WordPress, by the team at %s"
msgstr "Criado para quem constrói com o WordPress, pela equipa da %s"

#: includes/admin/views/acf-field-group/pro-features.php:6
msgid "View Pricing & Upgrade"
msgstr "Ver preços e actualização"

#: includes/admin/views/acf-field-group/pro-features.php:3
#: includes/admin/views/options-page-preview.php:29
#: includes/fields/class-acf-field-icon_picker.php:248
msgid "Learn More"
msgstr "Saiba mais"

#: includes/admin/views/acf-field-group/pro-features.php:28
msgid ""
"Speed up your workflow and develop better websites with features like ACF "
"Blocks and Options Pages, and sophisticated field types like Repeater, "
"Flexible Content, Clone, and Gallery."
msgstr ""
"Acelere o seu fluxo de trabalho e desenvolva melhores sites com "
"funcionalidades como blocos ACF, páginas de opções, e tipos de campos "
"sofisticados como Repetidor, Conteúdo Flexível, Clone e Galeria."

#: includes/admin/views/acf-field-group/pro-features.php:2
msgid "Unlock Advanced Features and Build Even More with ACF PRO"
msgstr "Desbloqueie funcionalidades avançadas e crie ainda mais com o ACF PRO"

#. translators: %s - singular label of post type/taxonomy, i.e. "Movie"/"Genre"
#: includes/admin/views/global/form-top.php:19
msgid "%s fields"
msgstr "Campos de %s"

#: includes/admin/post-types/admin-taxonomies.php:267
msgid "No terms"
msgstr "Nenhum termo"

#: includes/admin/post-types/admin-taxonomies.php:240
msgid "No post types"
msgstr "Nenhum tipo de conteúdo"

#: includes/admin/post-types/admin-post-types.php:264
msgid "No posts"
msgstr "Nenhum conteúdo"

#: includes/admin/post-types/admin-post-types.php:238
msgid "No taxonomies"
msgstr "Nenhuma taxonomia"

#: includes/admin/post-types/admin-post-types.php:183
#: includes/admin/post-types/admin-taxonomies.php:182
msgid "No field groups"
msgstr "Nenhum grupo de campos"

#: includes/admin/post-types/admin-field-groups.php:255
msgid "No fields"
msgstr "Nenhum campo"

#: includes/admin/post-types/admin-field-groups.php:128
#: includes/admin/post-types/admin-post-types.php:147
#: includes/admin/post-types/admin-taxonomies.php:146
msgid "No description"
msgstr "Nenhuma descrição"

#: includes/fields/class-acf-field-page_link.php:465
#: includes/fields/class-acf-field-post_object.php:374
#: includes/fields/class-acf-field-relationship.php:573
msgid "Any post status"
msgstr "Qualquer estado de publicação"

#: includes/post-types/class-acf-taxonomy.php:288
msgid ""
"This taxonomy key is already in use by another taxonomy registered outside "
"of ACF and cannot be used."
msgstr ""
"Esta chave de taxonomia já está a ser utilizada por outra taxonomia "
"registada fora do ACF e não pode ser utilizada."

#: includes/post-types/class-acf-taxonomy.php:284
msgid ""
"This taxonomy key is already in use by another taxonomy in ACF and cannot be "
"used."
msgstr ""
"Esta chave de taxonomia já está a ser utilizada por outra taxonomia no ACF e "
"não pode ser utilizada."

#: includes/post-types/class-acf-taxonomy.php:256
msgid ""
"The taxonomy key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"A chave da taxonomia só pode conter caracteres alfanuméricos minúsculos, "
"underscores e hífenes."

#: includes/post-types/class-acf-taxonomy.php:251
msgid "The taxonomy key must be under 32 characters."
msgstr "A chave da taxonomia tem de ter menos de 32 caracteres."

#: includes/post-types/class-acf-taxonomy.php:99
msgid "No Taxonomies found in Trash"
msgstr "Nenhuma taxonomia encontrada no lixo"

#: includes/post-types/class-acf-taxonomy.php:98
msgid "No Taxonomies found"
msgstr "Nenhuma taxonomia encontrada"

#: includes/post-types/class-acf-taxonomy.php:97
msgid "Search Taxonomies"
msgstr "Pesquisar taxonomias"

#: includes/post-types/class-acf-taxonomy.php:96
msgid "View Taxonomy"
msgstr "Ver taxonomias"

#: includes/post-types/class-acf-taxonomy.php:95
msgid "New Taxonomy"
msgstr "Nova taxonomia"

#: includes/post-types/class-acf-taxonomy.php:94
msgid "Edit Taxonomy"
msgstr "Editar taxonomia"

#: includes/post-types/class-acf-taxonomy.php:93
msgid "Add New Taxonomy"
msgstr "Adicionar nova taxonomia"

#: includes/post-types/class-acf-post-type.php:100
msgid "No Post Types found in Trash"
msgstr "Nenhum tipo de conteúdo encontrado no lixo"

#: includes/post-types/class-acf-post-type.php:99
msgid "No Post Types found"
msgstr "Nenhum tipo de conteúdo encontrado"

#: includes/post-types/class-acf-post-type.php:98
msgid "Search Post Types"
msgstr "Pesquisar tipos de conteúdo"

#: includes/post-types/class-acf-post-type.php:97
msgid "View Post Type"
msgstr "Ver tipo de conteúdo"

#: includes/post-types/class-acf-post-type.php:96
msgid "New Post Type"
msgstr "Novo tipo de conteúdo"

#: includes/post-types/class-acf-post-type.php:95
msgid "Edit Post Type"
msgstr "Editar tipo de conteúdo"

#: includes/post-types/class-acf-post-type.php:94
msgid "Add New Post Type"
msgstr "Adicionar novo tipo de conteúdo"

#: includes/post-types/class-acf-post-type.php:366
msgid ""
"This post type key is already in use by another post type registered outside "
"of ACF and cannot be used."
msgstr ""
"Esta chave de tipo de conteúdo já está a ser utilizada por outro tipo de "
"conteúdo registado fora do ACF e não pode ser utilizada."

#: includes/post-types/class-acf-post-type.php:361
msgid ""
"This post type key is already in use by another post type in ACF and cannot "
"be used."
msgstr ""
"Esta chave de tipo de conteúdo já está a ser utilizada por outro tipo de "
"conteúdo no ACF e não pode ser utilizada."

#. translators: %s a link to WordPress.org's Reserved Terms page
#: includes/post-types/class-acf-post-type.php:339
#: includes/post-types/class-acf-taxonomy.php:262
msgid ""
"This field must not be a WordPress <a href=\"%s\" target=\"_blank\">reserved "
"term</a>."
msgstr ""

#: includes/post-types/class-acf-post-type.php:333
msgid ""
"The post type key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""

#: includes/post-types/class-acf-post-type.php:328
msgid "The post type key must be under 20 characters."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:27
msgid "We do not recommend using this field in ACF Blocks."
msgstr "Não recomendamos utilizar este campo em blocos ACF."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid ""
"Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing "
"for a rich text-editing experience that also allows for multimedia content."
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:22
msgid "WYSIWYG Editor"
msgstr "Editor WYSIWYG"

#: includes/fields/class-acf-field-user.php:17
msgid ""
"Allows the selection of one or more users which can be used to create "
"relationships between data objects."
msgstr ""

#: includes/fields/class-acf-field-url.php:20
msgid "A text input specifically designed for storing web addresses."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:56
#: includes/fields/class-acf-field-url.php:19
msgid "URL"
msgstr "URL"

#: includes/fields/class-acf-field-true_false.php:24
msgid ""
"A toggle that allows you to pick a value of 1 or 0 (on or off, true or "
"false, etc). Can be presented as a stylized switch or checkbox."
msgstr ""

#: includes/fields/class-acf-field-time_picker.php:24
msgid ""
"An interactive UI for picking a time. The time format can be customized "
"using the field settings."
msgstr ""

#: includes/fields/class-acf-field-textarea.php:23
msgid "A basic textarea input for storing paragraphs of text."
msgstr ""

#: includes/fields/class-acf-field-text.php:23
msgid "A basic text input, useful for storing single string values."
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:22
msgid ""
"Allows the selection of one or more taxonomy terms based on the criteria and "
"options specified in the fields settings."
msgstr ""

#: includes/fields/class-acf-field-tab.php:25
msgid ""
"Allows you to group fields into tabbed sections in the edit screen. Useful "
"for keeping fields organized and structured."
msgstr ""

#: includes/fields/class-acf-field-select.php:24
msgid "A dropdown list with a selection of choices that you specify."
msgstr ""

#: includes/fields/class-acf-field-relationship.php:19
msgid ""
"A dual-column interface to select one or more posts, pages, or custom post "
"type items to create a relationship with the item that you're currently "
"editing. Includes options to search and filter."
msgstr ""

#: includes/fields/class-acf-field-range.php:23
msgid ""
"An input for selecting a numerical value within a specified range using a "
"range slider element."
msgstr ""

#: includes/fields/class-acf-field-radio.php:24
msgid ""
"A group of radio button inputs that allows the user to make a single "
"selection from values that you specify."
msgstr ""

#: includes/fields/class-acf-field-post_object.php:17
msgid ""
"An interactive and customizable UI for picking one or many posts, pages or "
"post type items with the option to search. "
msgstr ""

#: includes/fields/class-acf-field-password.php:23
msgid "An input for providing a password using a masked field."
msgstr ""

#: includes/fields/class-acf-field-page_link.php:457
#: includes/fields/class-acf-field-post_object.php:366
#: includes/fields/class-acf-field-relationship.php:565
msgid "Filter by Post Status"
msgstr "Filtrar por estado de publicação"

#: includes/fields/class-acf-field-page_link.php:24
msgid ""
"An interactive dropdown to select one or more posts, pages, custom post type "
"items or archive URLs, with the option to search."
msgstr ""

#: includes/fields/class-acf-field-oembed.php:24
msgid ""
"An interactive component for embedding videos, images, tweets, audio and "
"other content by making use of the native WordPress oEmbed functionality."
msgstr ""

#: includes/fields/class-acf-field-number.php:23
msgid "An input limited to numerical values."
msgstr ""

#: includes/fields/class-acf-field-message.php:25
msgid ""
"Used to display a message to editors alongside other fields. Useful for "
"providing additional context or instructions around your fields."
msgstr ""

#: includes/fields/class-acf-field-link.php:24
msgid ""
"Allows you to specify a link and its properties such as title and target "
"using the WordPress native link picker."
msgstr ""

#: includes/fields/class-acf-field-image.php:24
msgid "Uses the native WordPress media picker to upload, or choose images."
msgstr ""

#: includes/fields/class-acf-field-group.php:24
msgid ""
"Provides a way to structure fields into groups to better organize the data "
"and the edit screen."
msgstr ""

#: includes/fields/class-acf-field-google-map.php:24
msgid ""
"An interactive UI for selecting a location using Google Maps. Requires a "
"Google Maps API key and additional configuration to display correctly."
msgstr ""

#: includes/fields/class-acf-field-file.php:24
msgid "Uses the native WordPress media picker to upload, or choose files."
msgstr ""

#: includes/fields/class-acf-field-email.php:23
msgid "A text input specifically designed for storing email addresses."
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:24
msgid ""
"An interactive UI for picking a date and time. The date return format can be "
"customized using the field settings."
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:24
msgid ""
"An interactive UI for picking a date. The date return format can be "
"customized using the field settings."
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:24
msgid "An interactive UI for selecting a color, or specifying a Hex value."
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:24
msgid ""
"A group of checkbox inputs that allow the user to select one, or multiple "
"values that you specify."
msgstr ""

#: includes/fields/class-acf-field-button-group.php:25
msgid ""
"A group of buttons with values that you specify, users can choose one option "
"from the values provided."
msgstr ""

#: includes/fields/class-acf-field-accordion.php:26
msgid ""
"Allows you to group and organize custom fields into collapsable panels that "
"are shown while editing content. Useful for keeping large datasets tidy."
msgstr ""

#: includes/fields.php:449
msgid ""
"This provides a solution for repeating content such as slides, team members, "
"and call-to-action tiles, by acting as a parent to a set of subfields which "
"can be repeated again and again."
msgstr ""

#: includes/fields.php:439
msgid ""
"This provides an interactive interface for managing a collection of "
"attachments. Most settings are similar to the Image field type. Additional "
"settings allow you to specify where new attachments are added in the gallery "
"and the minimum/maximum number of attachments allowed."
msgstr ""

#: includes/fields.php:429
msgid ""
"This provides a simple, structured, layout-based editor. The Flexible "
"Content field allows you to define, create and manage content with total "
"control by using layouts and subfields to design the available blocks."
msgstr ""

#: includes/fields.php:419
msgid ""
"This allows you to select and display existing fields. It does not duplicate "
"any fields in the database, but loads and displays the selected fields at "
"run-time. The Clone field can either replace itself with the selected fields "
"or display the selected fields as a group of subfields."
msgstr ""

#: includes/fields.php:416
msgctxt "noun"
msgid "Clone"
msgstr "Clone"

#: includes/admin/views/global/navigation.php:86 includes/fields.php:331
#: src/Site_Health/Site_Health.php:290
msgid "PRO"
msgstr "PRO"

#: includes/fields.php:329 includes/fields.php:386
msgid "Advanced"
msgstr "Avançado"

#: includes/ajax/class-acf-ajax-local-json-diff.php:90
msgid "JSON (newer)"
msgstr "JSON (mais recente)"

#: includes/ajax/class-acf-ajax-local-json-diff.php:86
msgid "Original"
msgstr "Original"

#: includes/ajax/class-acf-ajax-local-json-diff.php:60
msgid "Invalid post ID."
msgstr "O ID do conteúdo é inválido."

#: includes/ajax/class-acf-ajax-local-json-diff.php:52
msgid "Invalid post type selected for review."
msgstr ""

#: includes/admin/views/global/navigation.php:189
msgid "More"
msgstr "Mais"

#: includes/admin/views/browse-fields-modal.php:96
msgid "Tutorial"
msgstr "Tutorial"

#: includes/admin/views/browse-fields-modal.php:73
msgid "Select Field"
msgstr "Seleccione o campo"

#. translators: %s: A link to the popular fields used in ACF
#: includes/admin/views/browse-fields-modal.php:60
msgid "Try a different search term or browse %s"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:57
msgid "Popular fields"
msgstr ""

#. translators: %s: The invalid search term
#: includes/admin/views/browse-fields-modal.php:50
#: includes/fields/class-acf-field-icon_picker.php:155
msgid "No search results for '%s'"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:23
msgid "Search fields..."
msgstr "Pesquisar campos..."

#: includes/admin/views/browse-fields-modal.php:21
msgid "Select Field Type"
msgstr "Seleccione o tipo de campo"

#: includes/admin/views/browse-fields-modal.php:4
msgid "Popular"
msgstr "Populares"

#: includes/admin/views/acf-taxonomy/list-empty.php:15
msgid "Add Taxonomy"
msgstr "Adicionar taxonomia"

#: includes/admin/views/acf-taxonomy/list-empty.php:14
msgid "Create custom taxonomies to classify post type content"
msgstr ""

#: includes/admin/views/acf-taxonomy/list-empty.php:13
msgid "Add Your First Taxonomy"
msgstr ""

#: includes/admin/views/acf-taxonomy/basic-settings.php:122
msgid "Hierarchical taxonomies can have descendants (like categories)."
msgstr ""

#: includes/admin/views/acf-taxonomy/basic-settings.php:107
msgid "Makes a taxonomy visible on the frontend and in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-taxonomy/basic-settings.php:91
msgid "One or many post types that can be classified with this taxonomy."
msgstr ""

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:60
msgid "genre"
msgstr ""

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:42
msgid "Genre"
msgstr "Género"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:25
msgid "Genres"
msgstr "Géneros"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1231
msgid ""
"Optional custom controller to use instead of `WP_REST_Terms_Controller `."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1175
msgid "Expose this post type in the REST API."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1075
msgid "Customize the query variable name"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1048
msgid ""
"Terms can be accessed using the non-pretty permalink, e.g., {query_var}"
"={term_slug}."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1001
msgid "Parent-child terms in URLs for hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:961
msgid "Customize the slug used in the URL"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:944
msgid "Permalinks for this taxonomy are disabled."
msgstr ""

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-taxonomy/advanced-settings.php:941
msgid ""
"Rewrite the URL using the taxonomy key as the slug. Your permalink structure "
"will be"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:933
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1050
#: includes/admin/views/acf-taxonomy/basic-settings.php:57
msgid "Taxonomy Key"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:931
msgid "Select the type of permalink to use for this taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:916
msgid "Display a column for the taxonomy on post type listing screens."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:915
msgid "Show Admin Column"
msgstr "Mostrar coluna de administração"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:902
msgid "Show the taxonomy in the quick/bulk edit panel."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:901
msgid "Quick Edit"
msgstr "Edição rápida"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:888
msgid "List the taxonomy in the Tag Cloud Widget controls."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:887
msgid "Tag Cloud"
msgstr "Nuvem de etiquetas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:842
msgid ""
"A PHP function name to be called for sanitizing taxonomy data saved from a "
"meta box."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:841
msgid "Meta Box Sanitization Callback"
msgstr "Callback de sanitização da metabox"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:821
msgid "Register Meta Box Callback"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:752
msgid "No Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:751
msgid "Custom Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:768
msgid ""
"Controls the meta box on the content editor screen. By default, the "
"Categories meta box is shown for hierarchical taxonomies, and the Tags meta "
"box is shown for non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:767
msgid "Meta Box"
msgstr "Metabox"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:746
#: includes/admin/views/acf-taxonomy/advanced-settings.php:773
msgid "Categories Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:745
#: includes/admin/views/acf-taxonomy/advanced-settings.php:772
msgid "Tags Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:704
msgid "A link to a tag"
msgstr "Uma ligação para uma etiqueta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:703
msgid "Describes a navigation link block variation used in the block editor."
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:698
msgid "A link to a %s"
msgstr "Uma ligação para um %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:683
msgid "Tag Link"
msgstr "Ligação da etiqueta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:682
msgid ""
"Assigns a title for navigation link block variation used in the block editor."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:663
msgid "← Go to tags"
msgstr "← Ir para etiquetas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:662
msgid ""
"Assigns the text used to link back to the main index after updating a term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:661
msgid "Back To Items"
msgstr "Voltar para os itens"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:657
msgid "← Go to %s"
msgstr "← Ir para %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:642
msgid "Tags list"
msgstr "Lista de etiquetas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:641
msgid "Assigns text to the table hidden heading."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:622
msgid "Tags list navigation"
msgstr "Navegação da lista de etiquetas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:621
msgid "Assigns text to the table pagination hidden heading."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:597
msgid "Filter by category"
msgstr "Filtrar por categoria"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:596
msgid "Assigns text to the filter button in the posts lists table."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:595
msgid "Filter By Item"
msgstr "Filtrar por item"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:591
msgid "Filter by %s"
msgstr "Filtrar por %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:575
#: includes/admin/views/acf-taxonomy/advanced-settings.php:576
msgid ""
"The description is not prominent by default; however, some themes may show "
"it."
msgstr ""
"Por omissão, a descrição não está proeminente, no entanto alguns temas "
"poderão apresentá-la."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:574
msgid "Describes the Description field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:573
msgid "Description Field Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:554
#: includes/admin/views/acf-taxonomy/advanced-settings.php:555
msgid ""
"Assign a parent term to create a hierarchy. The term Jazz, for example, "
"would be the parent of Bebop and Big Band"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:553
msgid "Describes the Parent field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:552
msgid "Parent Field Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:538
#: includes/admin/views/acf-taxonomy/advanced-settings.php:539
msgid ""
"The \"slug\" is the URL-friendly version of the name. It is usually all "
"lower case and contains only letters, numbers, and hyphens."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:537
msgid "Describes the Slug field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:536
msgid "Slug Field Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:522
#: includes/admin/views/acf-taxonomy/advanced-settings.php:523
msgid "The name is how it appears on your site"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:521
msgid "Describes the Name field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:520
msgid "Name Field Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:507
msgid "No tags"
msgstr "Nenhuma etiqueta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:506
msgid ""
"Assigns the text displayed in the posts and media list tables when no tags "
"or categories are available."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:505
msgid "No Terms"
msgstr "Nenhum termo"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:501
msgid "No %s"
msgstr "Nenhum %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:486
msgid "No tags found"
msgstr "Nenhuma etiqueta encontrada"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:485
msgid ""
"Assigns the text displayed when clicking the 'choose from most used' text in "
"the taxonomy meta box when no tags are available, and assigns the text used "
"in the terms list table when there are no items for a taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:484
msgid "Not Found"
msgstr "Nada encontrado"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:463
msgid "Assigns text to the Title field of the Most Used tab."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:462
#: includes/admin/views/acf-taxonomy/advanced-settings.php:464
#: includes/admin/views/acf-taxonomy/advanced-settings.php:465
msgid "Most Used"
msgstr "Mais usadas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:444
msgid "Choose from the most used tags"
msgstr "Escolha entre as etiquetas mais usadas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:443
msgid ""
"Assigns the 'choose from most used' text used in the meta box when "
"JavaScript is disabled. Only used on non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:442
msgid "Choose From Most Used"
msgstr "Escolher entre os mais utilizados"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:438
msgid "Choose from the most used %s"
msgstr "Escolher entre %s mais utilizados(as)"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:418
msgid "Add or remove tags"
msgstr "Adicionar ou remover etiquetas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:417
msgid ""
"Assigns the add or remove items text used in the meta box when JavaScript is "
"disabled. Only used on non-hierarchical taxonomies"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:416
msgid "Add Or Remove Items"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:412
msgid "Add or remove %s"
msgstr "Adicionar ou remover %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:392
msgid "Separate tags with commas"
msgstr "Separe as etiquetas por vírgulas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:391
msgid ""
"Assigns the separate item with commas text used in the taxonomy meta box. "
"Only used on non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:390
msgid "Separate Items With Commas"
msgstr "Separe os itens por vírgulas"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:386
msgid "Separate %s with commas"
msgstr "Separar %s por vírgulas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:366
msgid "Popular Tags"
msgstr "Etiquetas populares"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:365
msgid "Assigns popular items text. Only used for non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:364
msgid "Popular Items"
msgstr "Itens populares"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:361
msgid "Popular %s"
msgstr "%s mais populares"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:347
msgid "Search Tags"
msgstr "Pesquisar etiquetas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:346
msgid "Assigns search items text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:323
msgid "Parent Category:"
msgstr "Categoria superior:"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:322
msgid "Assigns parent item text, but with a colon (:) added to the end."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:321
msgid "Parent Item With Colon"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:298
msgid "Parent Category"
msgstr "Categoria superior"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:297
msgid "Assigns parent item text. Only used on hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:296
msgid "Parent Item"
msgstr "Item superior"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:293
msgid "Parent %s"
msgstr "%s superior"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:278
msgid "New Tag Name"
msgstr "Nome da nova etiqueta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:277
msgid "Assigns the new item name text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:276
msgid "New Item Name"
msgstr "Nome do novo item"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:273
msgid "New %s Name"
msgstr "Nome do novo %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:258
msgid "Add New Tag"
msgstr "Adicionar nova etiqueta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:257
msgid "Assigns the add new item text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:238
msgid "Update Tag"
msgstr "Actualizar etiqueta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:237
msgid "Assigns the update item text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:236
msgid "Update Item"
msgstr "Actualizar item"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:233
msgid "Update %s"
msgstr "Actualizar %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:218
msgid "View Tag"
msgstr "Ver etiqueta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:217
msgid "In the admin bar to view term during editing."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:198
msgid "Edit Tag"
msgstr "Editar etiqueta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:197
msgid "At the top of the editor screen when editing a term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:178
msgid "All Tags"
msgstr "Todas as etiquetas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:177
msgid "Assigns the all items text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:158
msgid "Assigns the menu name text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:157
msgid "Menu Label"
msgstr "Legenda do menu"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:131
msgid "Active taxonomies are enabled and registered with WordPress."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:115
msgid "A descriptive summary of the taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:95
msgid "A descriptive summary of the term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:94
msgid "Term Description"
msgstr "Descrição do termo"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:76
msgid "Single word, no spaces. Underscores and dashes allowed."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:75
msgid "Term Slug"
msgstr "Slug do termo"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:56
msgid "The name of the default term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:55
msgid "Term Name"
msgstr "Nome do termo"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:41
msgid ""
"Create a term for the taxonomy that cannot be deleted. It will not be "
"selected for posts by default."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:40
msgid "Default Term"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:28
msgid ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to `wp_set_object_terms()`."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:27
msgid "Sort Terms"
msgstr ""

#: includes/admin/views/acf-post-type/list-empty.php:14
msgid "Add Post Type"
msgstr "Adicionar tipo de conteúdo"

#: includes/admin/views/acf-post-type/list-empty.php:13
msgid ""
"Expand the functionality of WordPress beyond standard posts and pages with "
"custom post types."
msgstr ""

#: includes/admin/views/acf-post-type/list-empty.php:12
msgid "Add Your First Post Type"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:136
#: includes/admin/views/acf-taxonomy/basic-settings.php:135
msgid "I know what I'm doing, show me all the options."
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:135
#: includes/admin/views/acf-taxonomy/basic-settings.php:134
msgid "Advanced Configuration"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:123
msgid "Hierarchical post types can have descendants (like pages)."
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:122
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1000
#: includes/admin/views/acf-taxonomy/basic-settings.php:121
msgid "Hierarchical"
msgstr "Hierárquico"

#: includes/admin/views/acf-post-type/basic-settings.php:107
msgid "Visible on the frontend and in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:106
#: includes/admin/views/acf-taxonomy/basic-settings.php:106
msgid "Public"
msgstr "Público"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:59
msgid "movie"
msgstr "filme"

#: includes/admin/views/acf-post-type/basic-settings.php:57
msgid "Lower case letters, underscores and dashes only, Max 20 characters."
msgstr ""

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:41
msgid "Movie"
msgstr "Filme"

#: includes/admin/views/acf-post-type/basic-settings.php:39
#: includes/admin/views/acf-taxonomy/basic-settings.php:40
msgid "Singular Label"
msgstr "Legenda no singular"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:24
msgid "Movies"
msgstr "Filmes"

#: includes/admin/views/acf-post-type/basic-settings.php:22
#: includes/admin/views/acf-taxonomy/basic-settings.php:23
msgid "Plural Label"
msgstr "Legenda no plural"

#: includes/admin/views/acf-post-type/advanced-settings.php:1313
msgid ""
"Optional custom controller to use instead of `WP_REST_Posts_Controller`."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1312
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1230
msgid "Controller Class"
msgstr "Classe do controlador"

#: includes/admin/views/acf-post-type/advanced-settings.php:1294
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1211
msgid "The namespace part of the REST API URL."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1293
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1210
msgid "Namespace Route"
msgstr "Rota do namespace"

#: includes/admin/views/acf-post-type/advanced-settings.php:1275
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1192
msgid "The base URL for the post type REST API URLs."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1274
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1191
msgid "Base URL"
msgstr "URL de base"

#: includes/admin/views/acf-post-type/advanced-settings.php:1260
msgid ""
"Exposes this post type in the REST API. Required to use the block editor."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1259
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1174
msgid "Show In REST API"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1238
msgid "Customize the query variable name."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1237
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1074
msgid "Query Variable"
msgstr "Variável de consulta"

#: includes/admin/views/acf-post-type/advanced-settings.php:1215
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1052
msgid "No Query Variable Support"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1214
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1051
msgid "Custom Query Variable"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1211
msgid ""
"Items can be accessed using the non-pretty permalink, eg. {post_type}"
"={post_slug}."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1210
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1047
msgid "Query Variable Support"
msgstr "Suporte para variáveis de consulta"

#: includes/admin/views/acf-post-type/advanced-settings.php:1185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1023
msgid "URLs for an item and items can be accessed with a query string."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1184
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1022
msgid "Publicly Queryable"
msgstr "Pesquisável publicamente"

#: includes/admin/views/acf-post-type/advanced-settings.php:1163
msgid "Custom slug for the Archive URL."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1162
msgid "Archive Slug"
msgstr "Slug do arquivo"

#: includes/admin/views/acf-post-type/advanced-settings.php:1149
msgid ""
"Has an item archive that can be customized with an archive template file in "
"your theme."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1148
msgid "Archive"
msgstr "Arquivo"

#: includes/admin/views/acf-post-type/advanced-settings.php:1128
msgid "Pagination support for the items URLs such as the archives."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1127
msgid "Pagination"
msgstr "Paginação"

#: includes/admin/views/acf-post-type/advanced-settings.php:1110
msgid "RSS feed URL for the post type items."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1109
msgid "Feed URL"
msgstr "URL do feed"

#: includes/admin/views/acf-post-type/advanced-settings.php:1091
#: includes/admin/views/acf-taxonomy/advanced-settings.php:981
msgid ""
"Alters the permalink structure to add the `WP_Rewrite::$front` prefix to "
"URLs."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1090
#: includes/admin/views/acf-taxonomy/advanced-settings.php:980
msgid "Front URL Prefix"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1071
msgid "Customize the slug used in the URL."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1070
#: includes/admin/views/acf-taxonomy/advanced-settings.php:960
msgid "URL Slug"
msgstr "Slug do URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1054
msgid "Permalinks for this post type are disabled."
msgstr ""

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1053
#: includes/admin/views/acf-taxonomy/advanced-settings.php:943
msgid ""
"Rewrite the URL using a custom slug defined in the input below. Your "
"permalink structure will be"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1045
#: includes/admin/views/acf-taxonomy/advanced-settings.php:935
msgid "No Permalink (prevent URL rewriting)"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1044
#: includes/admin/views/acf-taxonomy/advanced-settings.php:934
msgid "Custom Permalink"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1043
#: includes/admin/views/acf-post-type/advanced-settings.php:1213
#: includes/admin/views/acf-post-type/basic-settings.php:56
msgid "Post Type Key"
msgstr ""

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1041
#: includes/admin/views/acf-post-type/advanced-settings.php:1051
msgid ""
"Rewrite the URL using the post type key as the slug. Your permalink "
"structure will be"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1039
#: includes/admin/views/acf-taxonomy/advanced-settings.php:930
msgid "Permalink Rewrite"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1025
msgid "Delete items by a user when that user is deleted."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1024
msgid "Delete With User"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1010
msgid "Allow the post type to be exported from 'Tools' > 'Export'."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1009
msgid "Can Export"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:978
msgid "Optionally provide a plural to be used in capabilities."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:977
msgid "Plural Capability Name"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:959
msgid "Choose another post type to base the capabilities for this post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:958
msgid "Singular Capability Name"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:944
msgid ""
"By default the capabilities of the post type will inherit the 'Post' "
"capability names, eg. edit_post, delete_posts. Enable to use post type "
"specific capabilities, eg. edit_{singular}, delete_{plural}."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:943
msgid "Rename Capabilities"
msgstr "Renomear capacidades"

#: includes/admin/views/acf-post-type/advanced-settings.php:928
msgid "Exclude From Search"
msgstr "Excluir da pesquisa"

#: includes/admin/views/acf-post-type/advanced-settings.php:915
#: includes/admin/views/acf-taxonomy/advanced-settings.php:874
msgid ""
"Allow items to be added to menus in the 'Appearance' > 'Menus' screen. Must "
"be turned on in 'Screen options'."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:914
#: includes/admin/views/acf-taxonomy/advanced-settings.php:873
msgid "Appearance Menus Support"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:896
msgid "Appears as an item in the 'New' menu in the admin bar."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:895
msgid "Show In Admin Bar"
msgstr "Mostrar na barra da administração"

#: includes/admin/views/acf-post-type/advanced-settings.php:861
msgid "Custom Meta Box Callback"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:822
#: includes/fields/class-acf-field-icon_picker.php:611
msgid "Menu Icon"
msgstr "Ícone do menu"

#: includes/admin/views/acf-post-type/advanced-settings.php:778
msgid "The position in the sidebar menu in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:777
msgid "Menu Position"
msgstr "Posição no menu"

#: includes/admin/views/acf-post-type/advanced-settings.php:759
msgid ""
"By default the post type will get a new top level item in the admin menu. If "
"an existing top level item is supplied here, the post type will be added as "
"a submenu item under it."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:758
msgid "Admin Menu Parent"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:739
#: includes/admin/views/acf-taxonomy/advanced-settings.php:734
msgid "Admin editor navigation in the sidebar menu."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:738
#: includes/admin/views/acf-taxonomy/advanced-settings.php:733
msgid "Show In Admin Menu"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:725
#: includes/admin/views/acf-taxonomy/advanced-settings.php:719
msgid "Items can be edited and managed in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:724
#: includes/admin/views/acf-taxonomy/advanced-settings.php:718
msgid "Show In UI"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:694
msgid "A link to a post."
msgstr "Uma ligação para um conteúdo."

#: includes/admin/views/acf-post-type/advanced-settings.php:693
msgid "Description for a navigation link block variation."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:692
#: includes/admin/views/acf-taxonomy/advanced-settings.php:702
msgid "Item Link Description"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:688
msgid "A link to a %s."
msgstr "Uma ligação para um %s."

#: includes/admin/views/acf-post-type/advanced-settings.php:673
msgid "Post Link"
msgstr "Ligação do conteúdo"

#: includes/admin/views/acf-post-type/advanced-settings.php:672
msgid "Title for a navigation link block variation."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:671
#: includes/admin/views/acf-taxonomy/advanced-settings.php:681
msgid "Item Link"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:668
#: includes/admin/views/acf-taxonomy/advanced-settings.php:678
msgid "%s Link"
msgstr "Ligação de %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:653
msgid "Post updated."
msgstr "Conteúdo actualizado."

#: includes/admin/views/acf-post-type/advanced-settings.php:652
msgid "In the editor notice after an item is updated."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:651
msgid "Item Updated"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:648
msgid "%s updated."
msgstr "%s actualizado."

#: includes/admin/views/acf-post-type/advanced-settings.php:633
msgid "Post scheduled."
msgstr "Conteúdo agendado."

#: includes/admin/views/acf-post-type/advanced-settings.php:632
msgid "In the editor notice after scheduling an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:631
msgid "Item Scheduled"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:628
msgid "%s scheduled."
msgstr "%s agendado."

#: includes/admin/views/acf-post-type/advanced-settings.php:613
msgid "Post reverted to draft."
msgstr "Conteúdo revertido para rascunho."

#: includes/admin/views/acf-post-type/advanced-settings.php:612
msgid "In the editor notice after reverting an item to draft."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:611
msgid "Item Reverted To Draft"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:608
msgid "%s reverted to draft."
msgstr "%s revertido para rascunho."

#: includes/admin/views/acf-post-type/advanced-settings.php:593
msgid "Post published privately."
msgstr "Conteúdo publicado em privado."

#: includes/admin/views/acf-post-type/advanced-settings.php:592
msgid "In the editor notice after publishing a private item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:591
msgid "Item Published Privately"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:588
msgid "%s published privately."
msgstr "%s publicado em privado."

#: includes/admin/views/acf-post-type/advanced-settings.php:573
msgid "Post published."
msgstr "Conteúdo publicado."

#: includes/admin/views/acf-post-type/advanced-settings.php:572
msgid "In the editor notice after publishing an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:571
msgid "Item Published"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:568
msgid "%s published."
msgstr "%s publicado."

#: includes/admin/views/acf-post-type/advanced-settings.php:553
msgid "Posts list"
msgstr "Lista de conteúdos"

#: includes/admin/views/acf-post-type/advanced-settings.php:552
msgid "Used by screen readers for the items list on the post type list screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:551
#: includes/admin/views/acf-taxonomy/advanced-settings.php:640
msgid "Items List"
msgstr "Lista de itens"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:548
#: includes/admin/views/acf-taxonomy/advanced-settings.php:637
msgid "%s list"
msgstr "Lista de %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:533
msgid "Posts list navigation"
msgstr "Navegação da lista de conteúdos"

#: includes/admin/views/acf-post-type/advanced-settings.php:532
msgid ""
"Used by screen readers for the filter list pagination on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:531
#: includes/admin/views/acf-taxonomy/advanced-settings.php:620
msgid "Items List Navigation"
msgstr "Navegação da lista de itens"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:528
#: includes/admin/views/acf-taxonomy/advanced-settings.php:617
msgid "%s list navigation"
msgstr "Navegação da lista de %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:512
msgid "Filter posts by date"
msgstr "Filtrar conteúdos por data"

#: includes/admin/views/acf-post-type/advanced-settings.php:511
msgid ""
"Used by screen readers for the filter by date heading on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:510
msgid "Filter Items By Date"
msgstr "Filtrar itens por data"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:506
msgid "Filter %s by date"
msgstr "Filtrar %s por data"

#: includes/admin/views/acf-post-type/advanced-settings.php:491
msgid "Filter posts list"
msgstr "Filtrar lista de conteúdos"

#: includes/admin/views/acf-post-type/advanced-settings.php:490
msgid ""
"Used by screen readers for the filter links heading on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:489
msgid "Filter Items List"
msgstr "Filtrar lista de itens"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:485
msgid "Filter %s list"
msgstr "Filtrar lista de %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:469
msgid "In the media modal showing all media uploaded to this item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:468
msgid "Uploaded To This Item"
msgstr "Carregados para este item"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:464
msgid "Uploaded to this %s"
msgstr "Carregados para este %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:449
msgid "Insert into post"
msgstr "Inserir no conteúdo"

#: includes/admin/views/acf-post-type/advanced-settings.php:448
msgid "As the button label when adding media to content."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:447
msgid "Insert Into Media Button"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:443
msgid "Insert into %s"
msgstr "Inserir em %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:428
msgid "Use as featured image"
msgstr "Usar como imagem de destaque"

#: includes/admin/views/acf-post-type/advanced-settings.php:427
msgid ""
"As the button label for selecting to use an image as the featured image."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:426
msgid "Use Featured Image"
msgstr "Usar imagem de destaque"

#: includes/admin/views/acf-post-type/advanced-settings.php:413
msgid "Remove featured image"
msgstr "Remover imagem de destaque"

#: includes/admin/views/acf-post-type/advanced-settings.php:412
msgid "As the button label when removing the featured image."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:411
msgid "Remove Featured Image"
msgstr "Remover imagem de destaque"

#: includes/admin/views/acf-post-type/advanced-settings.php:398
msgid "Set featured image"
msgstr "Definir imagem de destaque"

#: includes/admin/views/acf-post-type/advanced-settings.php:397
msgid "As the button label when setting the featured image."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:396
msgid "Set Featured Image"
msgstr "Definir imagem de destaque"

#: includes/admin/views/acf-post-type/advanced-settings.php:383
msgid "Featured image"
msgstr "Imagem de destaque"

#: includes/admin/views/acf-post-type/advanced-settings.php:382
msgid "In the editor used for the title of the featured image meta box."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:381
msgid "Featured Image Meta Box"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:368
msgid "Post Attributes"
msgstr "Atributos do conteúdo"

#: includes/admin/views/acf-post-type/advanced-settings.php:367
msgid "In the editor used for the title of the post attributes meta box."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:366
msgid "Attributes Meta Box"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:363
msgid "%s Attributes"
msgstr "Atributos de %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:348
msgid "Post Archives"
msgstr "Arquivo de conteúdos"

#: includes/admin/views/acf-post-type/advanced-settings.php:347
msgid ""
"Adds 'Post Type Archive' items with this label to the list of posts shown "
"when adding items to an existing menu in a CPT with archives enabled. Only "
"appears when editing menus in 'Live Preview' mode and a custom archive slug "
"has been provided."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:346
msgid "Archives Nav Menu"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:343
msgid "%s Archives"
msgstr "Arquivo de %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:328
msgid "No posts found in Trash"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:327
msgid ""
"At the top of the post type list screen when there are no posts in the trash."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:326
msgid "No Items Found in Trash"
msgstr "Nenhum item encontrado no lixo"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:322
msgid "No %s found in Trash"
msgstr "Nenhum %s encontrado no lixo"

#: includes/admin/views/acf-post-type/advanced-settings.php:307
msgid "No posts found"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:306
msgid ""
"At the top of the post type list screen when there are no posts to display."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:305
msgid "No Items Found"
msgstr "Nenhum item encontrado"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:301
#: includes/admin/views/acf-taxonomy/advanced-settings.php:480
msgid "No %s found"
msgstr "Nenhum %s encontrado"

#: includes/admin/views/acf-post-type/advanced-settings.php:286
msgid "Search Posts"
msgstr "Pesquisar conteúdos"

#: includes/admin/views/acf-post-type/advanced-settings.php:285
msgid "At the top of the items screen when searching for an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:284
#: includes/admin/views/acf-taxonomy/advanced-settings.php:345
msgid "Search Items"
msgstr "Pesquisar itens"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:281
#: includes/admin/views/acf-taxonomy/advanced-settings.php:342
msgid "Search %s"
msgstr "Pesquisa %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:266
msgid "Parent Page:"
msgstr "Página superior:"

#: includes/admin/views/acf-post-type/advanced-settings.php:265
msgid "For hierarchical types in the post type list screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:264
msgid "Parent Item Prefix"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:261
#: includes/admin/views/acf-taxonomy/advanced-settings.php:318
msgid "Parent %s:"
msgstr "%s superior:"

#: includes/admin/views/acf-post-type/advanced-settings.php:246
msgid "New Post"
msgstr "Novo conteúdo"

#: includes/admin/views/acf-post-type/advanced-settings.php:244
msgid "New Item"
msgstr "Novo item"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:241
msgid "New %s"
msgstr "Novo %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:206
#: includes/admin/views/acf-post-type/advanced-settings.php:226
msgid "Add New Post"
msgstr "Adicionar novo conteúdo"

#: includes/admin/views/acf-post-type/advanced-settings.php:205
msgid "At the top of the editor screen when adding a new item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:204
#: includes/admin/views/acf-taxonomy/advanced-settings.php:256
msgid "Add New Item"
msgstr "Adicionar novo item"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:201
#: includes/admin/views/acf-post-type/advanced-settings.php:221
#: includes/admin/views/acf-taxonomy/advanced-settings.php:253
msgid "Add New %s"
msgstr "Adicionar novo %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:186
msgid "View Posts"
msgstr "Ver conteúdos"

#: includes/admin/views/acf-post-type/advanced-settings.php:185
msgid ""
"Appears in the admin bar in the 'All Posts' view, provided the post type "
"supports archives and the home page is not an archive of that post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:184
msgid "View Items"
msgstr "Ver itens"

#: includes/admin/views/acf-post-type/advanced-settings.php:166
msgid "View Post"
msgstr "Ver conteúdo"

#: includes/admin/views/acf-post-type/advanced-settings.php:165
msgid "In the admin bar to view item when editing it."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:164
#: includes/admin/views/acf-taxonomy/advanced-settings.php:216
msgid "View Item"
msgstr "Ver item"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:161
#: includes/admin/views/acf-post-type/advanced-settings.php:181
#: includes/admin/views/acf-taxonomy/advanced-settings.php:213
msgid "View %s"
msgstr "Ver %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:146
msgid "Edit Post"
msgstr "Editar conteúdo"

#: includes/admin/views/acf-post-type/advanced-settings.php:145
msgid "At the top of the editor screen when editing an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:144
#: includes/admin/views/acf-taxonomy/advanced-settings.php:196
msgid "Edit Item"
msgstr "Editar item"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:141
#: includes/admin/views/acf-taxonomy/advanced-settings.php:193
msgid "Edit %s"
msgstr "Editar %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:126
msgid "All Posts"
msgstr "Todos os conteúdos"

#: includes/admin/views/acf-post-type/advanced-settings.php:125
#: includes/admin/views/acf-post-type/advanced-settings.php:225
#: includes/admin/views/acf-post-type/advanced-settings.php:245
msgid "In the post type submenu in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:124
#: includes/admin/views/acf-taxonomy/advanced-settings.php:176
msgid "All Items"
msgstr "Todos os itens"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:121
#: includes/admin/views/acf-taxonomy/advanced-settings.php:173
msgid "All %s"
msgstr "Todos os %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:105
msgid "Admin menu name for the post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:104
msgid "Menu Name"
msgstr "Nome do menu"

#: includes/admin/views/acf-post-type/advanced-settings.php:90
#: includes/admin/views/acf-taxonomy/advanced-settings.php:142
msgid "Regenerate all labels using the Singular and Plural labels"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:88
#: includes/admin/views/acf-taxonomy/advanced-settings.php:140
msgid "Regenerate"
msgstr "Regenerar"

#: includes/admin/views/acf-post-type/advanced-settings.php:79
msgid "Active post types are enabled and registered with WordPress."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:63
msgid "A descriptive summary of the post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:48
msgid "Add Custom"
msgstr "Adicionar personalização"

#: includes/admin/views/acf-post-type/advanced-settings.php:42
msgid "Enable various features in the content editor."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:31
msgid "Post Formats"
msgstr "Formatos de artigo"

#: includes/admin/views/acf-post-type/advanced-settings.php:25
msgid "Editor"
msgstr "Editor"

#: includes/admin/views/acf-post-type/advanced-settings.php:24
msgid "Trackbacks"
msgstr "Trackbacks"

#: includes/admin/views/acf-post-type/basic-settings.php:87
msgid "Select existing taxonomies to classify items of the post type."
msgstr ""

#: includes/admin/views/acf-field-group/field.php:158
msgid "Browse Fields"
msgstr "Pesquisar campos"

#: includes/admin/tools/class-acf-admin-tool-import.php:287
msgid "Nothing to import"
msgstr "Nada para importar"

#: includes/admin/tools/class-acf-admin-tool-import.php:282
msgid ". The Custom Post Type UI plugin can be deactivated."
msgstr ""

#. translators: %d - number of items imported from CPTUI
#: includes/admin/tools/class-acf-admin-tool-import.php:273
msgid "Imported %d item from Custom Post Type UI -"
msgid_plural "Imported %d items from Custom Post Type UI -"
msgstr[0] ""
msgstr[1] ""

#: includes/admin/tools/class-acf-admin-tool-import.php:257
msgid "Failed to import taxonomies."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:239
msgid "Failed to import post types."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:228
msgid "Nothing from Custom Post Type UI plugin selected for import."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:204
msgid "Imported 1 item"
msgid_plural "Imported %s items"
msgstr[0] ""
msgstr[1] ""

#: includes/admin/tools/class-acf-admin-tool-import.php:119
msgid ""
"Importing a Post Type or Taxonomy with the same key as one that already "
"exists will overwrite the settings for the existing Post Type or Taxonomy "
"with those of the import."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:108
#: includes/admin/tools/class-acf-admin-tool-import.php:124
msgid "Import from Custom Post Type UI"
msgstr "Importar do Custom Post Type UI"

#: includes/admin/tools/class-acf-admin-tool-export.php:354
msgid ""
"The following code can be used to register a local version of the selected "
"items. Storing field groups, post types, or taxonomies locally can provide "
"many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's "
"functions.php file or include it within an external file, then deactivate or "
"delete the items from the ACF admin."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:353
msgid "Export - Generate PHP"
msgstr "Exportar - Gerar PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:330
msgid "Export"
msgstr "Exportar"

#: includes/admin/tools/class-acf-admin-tool-export.php:264
msgid "Select Taxonomies"
msgstr "Seleccionar taxonomias"

#: includes/admin/tools/class-acf-admin-tool-export.php:239
msgid "Select Post Types"
msgstr "Seleccionar tipos de conteúdo"

#: includes/admin/tools/class-acf-admin-tool-export.php:155
msgid "Exported 1 item."
msgid_plural "Exported %s items."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/post-types/admin-taxonomy.php:127
msgid "Category"
msgstr "Categoria"

#: includes/admin/post-types/admin-taxonomy.php:125
msgid "Tag"
msgstr "Etiqueta"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:82
msgid "%s taxonomy created"
msgstr "Taxonomia %s criada"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:76
msgid "%s taxonomy updated"
msgstr "Taxonomia %s actualizada"

#: includes/admin/post-types/admin-taxonomy.php:56
msgid "Taxonomy draft updated."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:55
msgid "Taxonomy scheduled for."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:54
msgid "Taxonomy submitted."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:53
msgid "Taxonomy saved."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:49
msgid "Taxonomy deleted."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:48
msgid "Taxonomy updated."
msgstr ""

#: includes/admin/post-types/admin-taxonomies.php:351
#: includes/admin/post-types/admin-taxonomy.php:153
msgid ""
"This taxonomy could not be registered because its key is in use by another "
"taxonomy registered by another plugin or theme."
msgstr ""
"Não foi possível registar esta taxonomia porque a sua chave está a ser "
"utilizada por outra taxonomia registada por outro plugin ou tema."

#. translators: %s number of taxonomies synchronized
#: includes/admin/post-types/admin-taxonomies.php:333
msgid "Taxonomy synchronized."
msgid_plural "%s taxonomies synchronized."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of taxonomies duplicated
#: includes/admin/post-types/admin-taxonomies.php:326
msgid "Taxonomy duplicated."
msgid_plural "%s taxonomies duplicated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of taxonomies deactivated
#: includes/admin/post-types/admin-taxonomies.php:319
msgid "Taxonomy deactivated."
msgid_plural "%s taxonomies deactivated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of taxonomies activated
#: includes/admin/post-types/admin-taxonomies.php:312
msgid "Taxonomy activated."
msgid_plural "%s taxonomies activated."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/post-types/admin-taxonomies.php:113
msgid "Terms"
msgstr "Termos"

#. translators: %s number of post types synchronized
#: includes/admin/post-types/admin-post-types.php:327
msgid "Post type synchronized."
msgid_plural "%s post types synchronized."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of post types duplicated
#: includes/admin/post-types/admin-post-types.php:320
msgid "Post type duplicated."
msgid_plural "%s post types duplicated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of post types deactivated
#: includes/admin/post-types/admin-post-types.php:313
msgid "Post type deactivated."
msgid_plural "%s post types deactivated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of post types activated
#: includes/admin/post-types/admin-post-types.php:306
msgid "Post type activated."
msgid_plural "%s post types activated."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/post-types/admin-post-types.php:87
#: includes/admin/post-types/admin-taxonomies.php:111
#: includes/admin/tools/class-acf-admin-tool-import.php:79
#: includes/admin/views/acf-taxonomy/basic-settings.php:82
#: includes/post-types/class-acf-post-type.php:91
msgid "Post Types"
msgstr "Tipos de conteúdo"

#: includes/admin/post-types/admin-post-type.php:158
#: includes/admin/post-types/admin-taxonomy.php:160
msgid "Advanced Settings"
msgstr "Definições avançadas"

#: includes/admin/post-types/admin-post-type.php:157
#: includes/admin/post-types/admin-taxonomy.php:159
msgid "Basic Settings"
msgstr "Definições básicas"

#: includes/admin/post-types/admin-post-type.php:151
#: includes/admin/post-types/admin-post-types.php:345
msgid ""
"This post type could not be registered because its key is in use by another "
"post type registered by another plugin or theme."
msgstr ""
"Não foi possível registar este tipo de conteúdo porque a sua chave já foi "
"utilizada para registar outro tipo de conteúdo noutro plugin ou tema."

#: includes/admin/post-types/admin-post-type.php:126
msgid "Pages"
msgstr "Páginas"

#: includes/admin/admin-internal-post-type.php:347
msgid "Link Existing Field Groups"
msgstr ""

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:80
msgid "%s post type created"
msgstr "Tipo de conteúdo %s criado"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:78
msgid "Add fields to %s"
msgstr "Adicionar campos a %s"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:76
msgid "%s post type updated"
msgstr "Tipo de conteúdo %s actualizado"

#: includes/admin/post-types/admin-post-type.php:56
msgid "Post type draft updated."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:55
msgid "Post type scheduled for."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:54
msgid "Post type submitted."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:53
msgid "Post type saved."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:50
msgid "Post type updated."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:49
msgid "Post type deleted."
msgstr ""

#: includes/admin/post-types/admin-field-group.php:146
msgid "Type to search..."
msgstr "Digite para pesquisar..."

#: includes/admin/post-types/admin-field-group.php:101
msgid "PRO Only"
msgstr "Apenas PRO"

#: includes/admin/post-types/admin-field-group.php:93
msgid "Field groups linked successfully."
msgstr "Grupos de campos ligado com sucesso."

#. translators: %s - URL to ACF tools page.
#: includes/admin/admin.php:199
msgid ""
"Import Post Types and Taxonomies registered with Custom Post Type UI and "
"manage them with ACF. <a href=\"%s\">Get Started</a>."
msgstr ""

#: includes/admin/admin.php:46 includes/admin/admin.php:361
#: src/Site_Health/Site_Health.php:254
msgid "ACF"
msgstr "ACF"

#: includes/admin/admin-internal-post-type.php:314
msgid "taxonomy"
msgstr ""

#: includes/admin/admin-internal-post-type.php:314
msgid "post type"
msgstr "tipo de conteúdo"

#: includes/admin/admin-internal-post-type.php:338
msgid "Done"
msgstr "Concluído"

#: includes/admin/admin-internal-post-type.php:324
msgid "Field Group(s)"
msgstr ""

#: includes/admin/admin-internal-post-type.php:323
msgid "Select one or many field groups..."
msgstr "Seleccione um ou vários grupos de campos..."

#: includes/admin/admin-internal-post-type.php:322
msgid "Please select the field groups to link."
msgstr ""

#: includes/admin/admin-internal-post-type.php:280
msgid "Field group linked successfully."
msgid_plural "Field groups linked successfully."
msgstr[0] "Grupo de campos ligado com sucesso."
msgstr[1] "Grupos de campos ligados com sucesso."

#: includes/admin/admin-internal-post-type-list.php:277
#: includes/admin/post-types/admin-post-types.php:346
#: includes/admin/post-types/admin-taxonomies.php:352
msgctxt "post status"
msgid "Registration Failed"
msgstr "Falhou ao registar"

#: includes/admin/admin-internal-post-type-list.php:276
msgid ""
"This item could not be registered because its key is in use by another item "
"registered by another plugin or theme."
msgstr ""
"Não foi possível registar este item porque a sua chave está a ser utilizada "
"por outro item registado por outro plugin ou tema."

#: includes/acf-internal-post-type-functions.php:509
#: includes/acf-internal-post-type-functions.php:538
msgid "REST API"
msgstr "REST API"

#: includes/acf-internal-post-type-functions.php:508
#: includes/acf-internal-post-type-functions.php:537
#: includes/acf-internal-post-type-functions.php:564
msgid "Permissions"
msgstr "Permissões"

#: includes/acf-internal-post-type-functions.php:507
#: includes/acf-internal-post-type-functions.php:536
msgid "URLs"
msgstr "URLs"

#: includes/acf-internal-post-type-functions.php:506
#: includes/acf-internal-post-type-functions.php:535
#: includes/acf-internal-post-type-functions.php:562
msgid "Visibility"
msgstr "Visibilidade"

#: includes/acf-internal-post-type-functions.php:505
#: includes/acf-internal-post-type-functions.php:534
#: includes/acf-internal-post-type-functions.php:563
msgid "Labels"
msgstr "Legendas"

#: includes/admin/post-types/admin-field-group.php:279
msgid "Field Settings Tabs"
msgstr "Separadores das definições do campo"

#. Author URI of the plugin
#: acf.php
msgid ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"
msgstr ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"

#: includes/api/api-template.php:1027
msgid "[ACF shortcode value disabled for preview]"
msgstr ""

#: includes/admin/admin-internal-post-type.php:290
#: includes/admin/post-types/admin-field-group.php:572
msgid "Close Modal"
msgstr "Fechar janela"

#: includes/admin/post-types/admin-field-group.php:92
msgid "Field moved to other group"
msgstr "Campo movido para outro grupo"

#: includes/admin/post-types/admin-field-group.php:91
msgid "Close modal"
msgstr "Fechar janela"

#: includes/fields/class-acf-field-tab.php:122
msgid "Start a new group of tabs at this tab."
msgstr ""

#: includes/fields/class-acf-field-tab.php:121
msgid "New Tab Group"
msgstr ""

#: includes/fields/class-acf-field-select.php:421
#: includes/fields/class-acf-field-true_false.php:188
msgid "Use a stylized checkbox using select2"
msgstr "Utilize uma caixa de selecção estilizada com o select2"

#: includes/fields/class-acf-field-radio.php:250
msgid "Save Other Choice"
msgstr "Guardar outra opção"

#: includes/fields/class-acf-field-radio.php:239
msgid "Allow Other Choice"
msgstr "Permitir outra opção"

#: includes/fields/class-acf-field-checkbox.php:420
msgid "Add Toggle All"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:379
msgid "Save Custom Values"
msgstr "Guardar valores personalizados"

#: includes/fields/class-acf-field-checkbox.php:368
msgid "Allow Custom Values"
msgstr "Permitir valores personalizados"

#: includes/fields/class-acf-field-checkbox.php:134
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""

#: includes/admin/views/global/navigation.php:253
msgid "Updates"
msgstr "Actualizações"

#: includes/admin/views/global/navigation.php:177
#: includes/admin/views/global/navigation.php:181
msgid "Advanced Custom Fields logo"
msgstr "Logótipo do Advanced Custom Fields"

#: includes/admin/views/global/form-top.php:92
msgid "Save Changes"
msgstr "Guardar alterações"

#: includes/admin/views/global/form-top.php:79
msgid "Field Group Title"
msgstr "Título do grupo de campos"

#: includes/admin/views/acf-post-type/advanced-settings.php:709
#: includes/admin/views/global/form-top.php:3
msgid "Add title"
msgstr "Adicionar título"

#. translators: %s url to getting started guide
#: includes/admin/views/acf-field-group/list-empty.php:30
#: includes/admin/views/acf-post-type/list-empty.php:20
#: includes/admin/views/acf-taxonomy/list-empty.php:21
#: includes/admin/views/options-page-preview.php:13
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""
"Não conhece o ACF? Consulte o nosso <a href=\"%s\" target=\"_blank\">guia "
"para iniciantes</a>."

#: includes/admin/views/acf-field-group/list-empty.php:24
msgid "Add Field Group"
msgstr "Adicionar grupo de campos"

#. translators: %s url to creating a field group page
#: includes/admin/views/acf-field-group/list-empty.php:18
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""
"O ACF utiliza <a href=\"%s\" target=\"_blank\">grupos de campos</a> para "
"agrupar campos personalizados, para depois poder anexar esses campos a ecrãs "
"de edição."

#: includes/admin/views/acf-field-group/list-empty.php:12
msgid "Add Your First Field Group"
msgstr "Adicione o seu primeiro grupo de campos"

#: includes/admin/admin-options-pages-preview.php:28
#: includes/admin/views/acf-field-group/pro-features.php:58
#: includes/admin/views/global/navigation.php:86
#: includes/admin/views/global/navigation.php:255
msgid "Options Pages"
msgstr "Páginas de opções"

#: includes/admin/views/acf-field-group/pro-features.php:54
msgid "ACF Blocks"
msgstr "Blocos do ACF"

#: includes/admin/views/acf-field-group/pro-features.php:62
msgid "Gallery Field"
msgstr "Campo de galeria"

#: includes/admin/views/acf-field-group/pro-features.php:42
msgid "Flexible Content Field"
msgstr "Campo de conteúdo flexível"

#: includes/admin/views/acf-field-group/pro-features.php:46
msgid "Repeater Field"
msgstr "Campo repetidor"

#: includes/admin/views/global/navigation.php:215
msgid "Unlock Extra Features with ACF PRO"
msgstr "Desbloqueie funcionalidades adicionais com o ACF PRO"

#: includes/admin/views/acf-field-group/options.php:267
msgid "Delete Field Group"
msgstr "Eliminar grupo de campos"

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/acf-field-group/options.php:261
msgid "Created on %1$s at %2$s"
msgstr "Criado em %1$s às %2$s"

#: includes/acf-field-group-functions.php:497
msgid "Group Settings"
msgstr "Definições do grupo"

#: includes/acf-field-group-functions.php:495
msgid "Location Rules"
msgstr "Regras de localização"

#. translators: %s url to field types list
#: includes/admin/views/acf-field-group/fields.php:73
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""
"Escolha entre mais de 30 tipos de campo. <a href=\"%s\" "
"target=\"_blank\">Saiba mais</a>."

#: includes/admin/views/acf-field-group/fields.php:65
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""
"Comece por criar novos campos personalizados para os seus artigos, páginas, "
"tipos de conteúdo personalizados e outros conteúdos do WordPress."

#: includes/admin/views/acf-field-group/fields.php:64
msgid "Add Your First Field"
msgstr "Adicione o seu primeiro campo"

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/acf-field-group/fields.php:43
msgid "#"
msgstr "#"

#: includes/admin/views/acf-field-group/fields.php:33
#: includes/admin/views/acf-field-group/fields.php:67
#: includes/admin/views/acf-field-group/fields.php:101
#: includes/admin/views/global/form-top.php:88
msgid "Add Field"
msgstr "Adicionar campo"

#: includes/acf-field-group-functions.php:496 includes/fields.php:384
msgid "Presentation"
msgstr "Apresentação"

#: includes/fields.php:383
msgid "Validation"
msgstr "Validação"

#: includes/acf-internal-post-type-functions.php:504
#: includes/acf-internal-post-type-functions.php:533 includes/fields.php:382
msgid "General"
msgstr "Geral"

#: includes/admin/tools/class-acf-admin-tool-import.php:67
msgid "Import JSON"
msgstr "Importar JSON"

#: includes/admin/tools/class-acf-admin-tool-export.php:338
msgid "Export As JSON"
msgstr "Exportar como JSON"

#. translators: %s number of field groups deactivated
#: includes/admin/post-types/admin-field-groups.php:360
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] "Grupo de campos desactivado."
msgstr[1] "%s grupos de campos desactivados."

#. translators: %s number of field groups activated
#: includes/admin/post-types/admin-field-groups.php:353
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] "Grupo de campos activado."
msgstr[1] "%s grupos de campos activados."

#: includes/admin/admin-internal-post-type-list.php:470
#: includes/admin/admin-internal-post-type-list.php:496
msgid "Deactivate"
msgstr "Desactivar"

#: includes/admin/admin-internal-post-type-list.php:470
msgid "Deactivate this item"
msgstr "Desactivar este item"

#: includes/admin/admin-internal-post-type-list.php:466
#: includes/admin/admin-internal-post-type-list.php:495
msgid "Activate"
msgstr "Activar"

#: includes/admin/admin-internal-post-type-list.php:466
msgid "Activate this item"
msgstr "Activar este item"

#: includes/admin/post-types/admin-field-group.php:88
msgid "Move field group to trash?"
msgstr "Mover o grupo de campos para o lixo?"

#: acf.php:519 includes/admin/admin-internal-post-type-list.php:264
#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Inactive"
msgstr "Inactivo"

#. Author of the plugin
#: acf.php
msgid "WP Engine"
msgstr "WP Engine"

#: acf.php:577
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""
"O Advanced Custom Fields e o Advanced Custom Fields PRO não podem estar "
"activos ao mesmo tempo. O Advanced Custom Fields PRO foi desactivado "
"automaticamente."

#: acf.php:575
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""
"O Advanced Custom Fields e o Advanced Custom Fields PRO não podem estar "
"activos ao mesmo tempo. O Advanced Custom Fields foi desactivado "
"automaticamente."

#: includes/fields/class-acf-field-user.php:578
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] "%1$s tem de ter um utilizador com o papel %2$s."
msgstr[1] "%1$s tem de ter um utilizador com um dos seguintes papéis: %2$s"

#: includes/fields/class-acf-field-user.php:569
msgid "%1$s must have a valid user ID."
msgstr "%1$s tem de ter um ID de utilizador válido."

#: includes/fields/class-acf-field-user.php:408
msgid "Invalid request."
msgstr "Pedido inválido."

#: includes/fields/class-acf-field-select.php:635
msgid "%1$s is not one of %2$s"
msgstr "%1$s não é um de %2$s"

#: includes/fields/class-acf-field-post_object.php:660
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] "%1$s tem de ter o termo %2$s."
msgstr[1] "%1$s tem de ter um dos seguintes termos: %2$s"

#: includes/fields/class-acf-field-post_object.php:644
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] "%1$s tem de ser do tipo de conteúdo %2$s."
msgstr[1] "%1$s tem de ser de um dos seguintes tipos de conteúdo: %2$s"

#: includes/fields/class-acf-field-post_object.php:635
msgid "%1$s must have a valid post ID."
msgstr "%1$s tem de ter um ID de conteúdo válido."

#: includes/fields/class-acf-field-file.php:447
msgid "%s requires a valid attachment ID."
msgstr "%s requer um ID de anexo válido."

#: includes/admin/views/acf-field-group/options.php:233
msgid "Show in REST API"
msgstr "Mostrar na REST API"

#: includes/fields/class-acf-field-color_picker.php:156
msgid "Enable Transparency"
msgstr "Activar transparência"

#: includes/fields/class-acf-field-color_picker.php:175
msgid "RGBA Array"
msgstr "Array de RGBA"

#: includes/fields/class-acf-field-color_picker.php:92
msgid "RGBA String"
msgstr "String de RGBA"

#: includes/fields/class-acf-field-color_picker.php:91
#: includes/fields/class-acf-field-color_picker.php:174
msgid "Hex String"
msgstr "String hexadecimal"

#: includes/admin/views/browse-fields-modal.php:12
msgid "Upgrade to PRO"
msgstr "Actualizar para PRO"

#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Active"
msgstr "Activo"

#: includes/fields/class-acf-field-email.php:166
msgid "'%s' is not a valid email address"
msgstr "'%s' não é um endereço de email válido"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Color value"
msgstr "Valor da cor"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Select default color"
msgstr "Seleccionar cor por omissão"

#: includes/fields/class-acf-field-color_picker.php:66
msgid "Clear color"
msgstr "Limpar cor"

#: includes/acf-wp-functions.php:90
msgid "Blocks"
msgstr "Blocos"

#: includes/acf-wp-functions.php:86
msgid "Options"
msgstr "Opções"

#: includes/acf-wp-functions.php:82
msgid "Users"
msgstr "Utilizadores"

#: includes/acf-wp-functions.php:78
msgid "Menu items"
msgstr "Itens de menu"

#: includes/acf-wp-functions.php:70
msgid "Widgets"
msgstr "Widgets"

#: includes/acf-wp-functions.php:62
msgid "Attachments"
msgstr "Anexos"

#: includes/acf-wp-functions.php:57
#: includes/admin/post-types/admin-post-types.php:112
#: includes/admin/post-types/admin-taxonomies.php:86
#: includes/admin/tools/class-acf-admin-tool-import.php:90
#: includes/admin/views/acf-post-type/basic-settings.php:86
#: includes/post-types/class-acf-taxonomy.php:90
#: includes/post-types/class-acf-taxonomy.php:91
msgid "Taxonomies"
msgstr "Taxonomias"

#: includes/acf-wp-functions.php:44
#: includes/admin/post-types/admin-post-type.php:124
#: includes/admin/post-types/admin-post-types.php:114
#: includes/admin/views/acf-post-type/advanced-settings.php:106
msgid "Posts"
msgstr "Conteúdos"

#: includes/ajax/class-acf-ajax-local-json-diff.php:81
msgid "Last updated: %s"
msgstr "Última actualização: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:75
msgid "Sorry, this post is unavailable for diff comparison."
msgstr ""
"Desculpe, este conteúdo não está disponível para comparação das diferenças."

#: includes/ajax/class-acf-ajax-local-json-diff.php:47
msgid "Invalid field group parameter(s)."
msgstr "Os parâmetros do grupo de campos são inválidos."

#: includes/admin/admin-internal-post-type-list.php:429
msgid "Awaiting save"
msgstr "Por guardar"

#: includes/admin/admin-internal-post-type-list.php:426
msgid "Saved"
msgstr "Guardado"

#: includes/admin/admin-internal-post-type-list.php:422
#: includes/admin/tools/class-acf-admin-tool-import.php:46
msgid "Import"
msgstr "Importar"

#: includes/admin/admin-internal-post-type-list.php:418
msgid "Review changes"
msgstr "Rever alterações"

#: includes/admin/admin-internal-post-type-list.php:394
msgid "Located in: %s"
msgstr "Localizado em: %s"

#: includes/admin/admin-internal-post-type-list.php:391
msgid "Located in plugin: %s"
msgstr "Localizado no plugin: %s"

#: includes/admin/admin-internal-post-type-list.php:388
msgid "Located in theme: %s"
msgstr "Localizado no tema: %s"

#: includes/admin/post-types/admin-field-groups.php:235
msgid "Various"
msgstr "Vários"

#: includes/admin/admin-internal-post-type-list.php:230
#: includes/admin/admin-internal-post-type-list.php:503
msgid "Sync changes"
msgstr "Sincronizar alterações"

#: includes/admin/admin-internal-post-type-list.php:229
msgid "Loading diff"
msgstr "A carregar diferenças"

#: includes/admin/admin-internal-post-type-list.php:228
msgid "Review local JSON changes"
msgstr "Revisão das alterações do JSON local"

#: includes/admin/admin.php:174
msgid "Visit website"
msgstr "Visitar site"

#: includes/admin/admin.php:173
msgid "View details"
msgstr "Ver detalhes"

#: includes/admin/admin.php:172
msgid "Version %s"
msgstr "Versão %s"

#: includes/admin/admin.php:171
msgid "Information"
msgstr "Informações"

#: includes/admin/admin.php:162
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Suporte</a>. Os profissionais de suporte no "
"nosso Help Desk ajudar-lhe-ão com os desafios técnicos mais complexos."

#: includes/admin/admin.php:158
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the 'how-tos' of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Discussão</a>. Temos uma comunidade activa "
"e amigável no nosso Fórum da Comunidade, que poderá ajudar a encontrar "
"soluções no mundo ACF."

#: includes/admin/admin.php:154
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Documentação</a>. A nossa vasta "
"documentação inclui referências e guias para a maioria das situações que "
"poderá encontrar."

#: includes/admin/admin.php:151
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"Somos fanáticos por suporte, queremos que tire o melhor partido do seu site "
"com o ACF. Se tiver alguma dificuldade, tem várias opções para obter ajuda:"

#: includes/admin/admin.php:148 includes/admin/admin.php:150
msgid "Help & Support"
msgstr "Ajuda e suporte"

#: includes/admin/admin.php:139
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""
"Caso precise de alguma assistência, entre em contacto através do separador "
"Ajuda e suporte."

#: includes/admin/admin.php:136
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"Antes de criar o seu primeiro Grupo de Campos, recomendamos uma primeira "
"leitura do nosso guia <a href=\"%s\" target=\"_blank\">Getting started</a> "
"para se familiarizar com a filosofia e com as melhores práticas do plugin."

#: includes/admin/admin.php:134
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"O plugin Advanced Custom Fields fornece-lhe um construtor visual de "
"formulários para personalizar os ecrãs de edição do WordPress com campos "
"adicionais, e uma interface intuitiva para mostrar os valores dos campos "
"personalizados em qualquer ficheiro de modelo de tema."

#: includes/admin/admin.php:131 includes/admin/admin.php:133
msgid "Overview"
msgstr "Visão geral"

#. translators: %s the name of the location type
#: includes/locations.php:38
msgid "Location type \"%s\" is already registered."
msgstr "O tipo de localização \"%s\" já está registado."

#. translators: %s class name for a location that could not be found
#: includes/locations.php:26
msgid "Class \"%s\" does not exist."
msgstr "A classe \"%s\" não existe."

#: includes/ajax/class-acf-ajax-query-users.php:43
#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "Nonce inválido."

#: includes/fields/class-acf-field-user.php:400
msgid "Error loading field."
msgstr "Erro ao carregar o campo."

#: includes/forms/form-user.php:328
msgid "<strong>Error</strong>: %s"
msgstr "<strong>Erro</strong>: %s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Widget"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "Papel de utilizador"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Comentário"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Formato de artigo"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Item de menu"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "Estado do conteúdo"

#: includes/acf-wp-functions.php:74
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Menus"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Localizações do menu"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Menu"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Taxonomia do artigo"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Página dependente (tem superior)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Página superior (tem dependentes)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Página de topo (sem superior)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Página de artigos"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Página inicial"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Tipo de página"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "A visualizar a administração do site"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "A visualizar a frente do site"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Sessão iniciada"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Utilizador actual"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Modelo de página"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "Registar"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Adicionar / Editar"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Formulário de utilizador"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Página superior"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Super Administrador"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Papel do utilizador actual"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Modelo por omissão"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Modelo de conteúdo"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Categoria de artigo"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "Todos os formatos de %s"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Anexo"

#: includes/validation.php:323
msgid "%s value is required"
msgstr "O valor %s é obrigatório"

#: includes/admin/views/acf-field-group/conditional-logic.php:64
msgid "Show this field if"
msgstr "Mostrar este campo se"

#: includes/admin/views/acf-field-group/conditional-logic.php:25
#: includes/admin/views/acf-field-group/field.php:122 includes/fields.php:385
msgid "Conditional Logic"
msgstr "Lógica condicional"

#: includes/admin/views/acf-field-group/conditional-logic.php:169
#: includes/admin/views/acf-field-group/location-rule.php:84
msgid "and"
msgstr "e"

#: includes/admin/post-types/admin-field-groups.php:97
#: includes/admin/post-types/admin-post-types.php:118
#: includes/admin/post-types/admin-taxonomies.php:117
msgid "Local JSON"
msgstr "JSON local"

#: includes/admin/views/acf-field-group/pro-features.php:50
msgid "Clone Field"
msgstr "Campo de clone"

#. translators: %s a list of plugin
#: includes/admin/views/upgrade/notice.php:32
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Por favor, verifique se todos os add-ons premium (%s) estão actualizados "
"para a última versão."

#: includes/admin/views/upgrade/notice.php:29
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"Esta versão inclui melhorias na base de dados e requer uma actualização."

#. translators: %1 plugin name, %2 version number
#: includes/admin/views/upgrade/notice.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "Obrigado por actualizar para o %1$s v%2$s!"

#: includes/admin/views/upgrade/notice.php:26
msgid "Database Upgrade Required"
msgstr "Actualização da base de dados necessária"

#: includes/admin/post-types/admin-field-group.php:159
#: includes/admin/views/upgrade/notice.php:17
msgid "Options Page"
msgstr "Página de opções"

#: includes/admin/views/upgrade/notice.php:14 includes/fields.php:436
msgid "Gallery"
msgstr "Galeria"

#: includes/admin/views/upgrade/notice.php:11 includes/fields.php:426
msgid "Flexible Content"
msgstr "Conteúdo flexível"

#: includes/admin/views/upgrade/notice.php:8 includes/fields.php:446
msgid "Repeater"
msgstr "Repetidor"

#: includes/admin/views/tools/tools.php:16
msgid "Back to all tools"
msgstr "Voltar para todas as ferramentas"

#: includes/admin/views/acf-field-group/options.php:195
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Se forem mostrados vários grupos de campos num ecrã de edição, serão "
"utilizadas as opções do primeiro grupo de campos. (o que tiver menor número "
"de ordem)"

#: includes/admin/views/acf-field-group/options.php:195
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>Seleccione</b> os itens a <b>esconder</b> do ecrã de edição."

#: includes/admin/views/acf-field-group/options.php:194
msgid "Hide on screen"
msgstr "Esconder no ecrã"

#: includes/admin/views/acf-field-group/options.php:186
msgid "Send Trackbacks"
msgstr "Enviar trackbacks"

#: includes/admin/post-types/admin-taxonomy.php:126
#: includes/admin/views/acf-field-group/options.php:185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:159
msgid "Tags"
msgstr "Etiquetas"

#: includes/admin/post-types/admin-taxonomy.php:128
#: includes/admin/views/acf-field-group/options.php:184
msgid "Categories"
msgstr "Categorias"

#: includes/admin/views/acf-field-group/options.php:182
#: includes/admin/views/acf-post-type/advanced-settings.php:28
msgid "Page Attributes"
msgstr "Atributos da página"

#: includes/admin/views/acf-field-group/options.php:181
msgid "Format"
msgstr "Formato"

#: includes/admin/views/acf-field-group/options.php:180
#: includes/admin/views/acf-post-type/advanced-settings.php:22
msgid "Author"
msgstr "Autor"

#: includes/admin/views/acf-field-group/options.php:179
msgid "Slug"
msgstr "Slug"

#: includes/admin/views/acf-field-group/options.php:178
#: includes/admin/views/acf-post-type/advanced-settings.php:27
msgid "Revisions"
msgstr "Revisões"

#: includes/acf-wp-functions.php:66
#: includes/admin/views/acf-field-group/options.php:177
#: includes/admin/views/acf-post-type/advanced-settings.php:23
msgid "Comments"
msgstr "Comentários"

#: includes/admin/views/acf-field-group/options.php:176
msgid "Discussion"
msgstr "Discussão"

#: includes/admin/views/acf-field-group/options.php:174
#: includes/admin/views/acf-post-type/advanced-settings.php:26
msgid "Excerpt"
msgstr "Excerto"

#: includes/admin/views/acf-field-group/options.php:173
msgid "Content Editor"
msgstr "Editor de conteúdo"

#: includes/admin/views/acf-field-group/options.php:172
msgid "Permalink"
msgstr "Ligação permanente"

#: includes/admin/views/acf-field-group/options.php:250
msgid "Shown in field group list"
msgstr "Mostrado na lista de grupos de campos"

#: includes/admin/views/acf-field-group/options.php:157
msgid "Field groups with a lower order will appear first"
msgstr ""
"Serão mostrados primeiro os grupos de campos com menor número de ordem."

#: includes/admin/views/acf-field-group/options.php:156
msgid "Order No."
msgstr "Nº. de ordem"

#: includes/admin/views/acf-field-group/options.php:147
msgid "Below fields"
msgstr "Abaixo dos campos"

#: includes/admin/views/acf-field-group/options.php:146
msgid "Below labels"
msgstr "Abaixo das legendas"

#: includes/admin/views/acf-field-group/options.php:139
msgid "Instruction Placement"
msgstr "Posição das instruções"

#: includes/admin/views/acf-field-group/options.php:122
msgid "Label Placement"
msgstr "Posição da legenda"

#: includes/admin/views/acf-field-group/options.php:110
msgid "Side"
msgstr "Lateral"

#: includes/admin/views/acf-field-group/options.php:109
msgid "Normal (after content)"
msgstr "Normal (depois do conteúdo)"

#: includes/admin/views/acf-field-group/options.php:108
msgid "High (after title)"
msgstr "Acima (depois do título)"

#: includes/admin/views/acf-field-group/options.php:101
msgid "Position"
msgstr "Posição"

#: includes/admin/views/acf-field-group/options.php:92
msgid "Seamless (no metabox)"
msgstr "Simples (sem metabox)"

#: includes/admin/views/acf-field-group/options.php:91
msgid "Standard (WP metabox)"
msgstr "Predefinido (metabox do WP)"

#: includes/admin/views/acf-field-group/options.php:84
msgid "Style"
msgstr "Estilo"

#: includes/admin/views/acf-field-group/fields.php:55
msgid "Type"
msgstr "Tipo"

#: includes/admin/post-types/admin-field-groups.php:91
#: includes/admin/post-types/admin-post-types.php:111
#: includes/admin/post-types/admin-taxonomies.php:110
#: includes/admin/views/acf-field-group/fields.php:54
msgid "Key"
msgstr "Chave"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/acf-field-group/fields.php:48
msgid "Order"
msgstr "Ordem"

#: includes/admin/views/acf-field-group/field.php:321
msgid "Close Field"
msgstr "Fechar campo"

#: includes/admin/views/acf-field-group/field.php:252
msgid "id"
msgstr "id"

#: includes/admin/views/acf-field-group/field.php:236
msgid "class"
msgstr "classe"

#: includes/admin/views/acf-field-group/field.php:278
msgid "width"
msgstr "largura"

#: includes/admin/views/acf-field-group/field.php:272
msgid "Wrapper Attributes"
msgstr "Atributos do wrapper"

#: includes/fields/class-acf-field.php:312
msgid "Required"
msgstr "Obrigatório"

#: includes/admin/views/acf-field-group/field.php:219
msgid "Instructions"
msgstr "Instruções"

#: includes/admin/views/acf-field-group/field.php:142
msgid "Field Type"
msgstr "Tipo de campo"

#: includes/admin/views/acf-field-group/field.php:183
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Uma única palavra, sem espaços. São permitidos underscores e hífenes."

#: includes/admin/views/acf-field-group/field.php:182
msgid "Field Name"
msgstr "Nome do campo"

#: includes/admin/views/acf-field-group/field.php:170
msgid "This is the name which will appear on the EDIT page"
msgstr "Este é o nome que será mostrado na página EDITAR"

#: includes/admin/views/acf-field-group/field.php:169
#: includes/admin/views/browse-fields-modal.php:69
msgid "Field Label"
msgstr "Legenda do campo"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete"
msgstr "Eliminar"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete field"
msgstr "Eliminar campo"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move"
msgstr "Mover"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move field to another group"
msgstr "Mover campo para outro grupo"

#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate field"
msgstr "Duplicar campo"

#: includes/admin/views/acf-field-group/field.php:86
#: includes/admin/views/acf-field-group/field.php:89
msgid "Edit field"
msgstr "Editar campo"

#: includes/admin/views/acf-field-group/field.php:82
msgid "Drag to reorder"
msgstr "Arraste para reordenar"

#: includes/admin/post-types/admin-field-group.php:99
#: includes/admin/views/acf-field-group/location-group.php:3
msgid "Show this field group if"
msgstr "Mostrar este grupo de campos se"

#: includes/admin/views/upgrade/upgrade.php:93
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "Nenhuma actualização disponível."

#. translators: %s the url to the field group page.
#: includes/admin/views/upgrade/upgrade.php:32
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"Actualização da base de dados concluída. <a href=\"%s\">Ver o que há de "
"novo</a>"

#: includes/admin/views/upgrade/upgrade.php:27
msgid "Reading upgrade tasks..."
msgstr "A ler tarefas de actualização..."

#: includes/admin/views/upgrade/network.php:165
#: includes/admin/views/upgrade/upgrade.php:64
msgid "Upgrade failed."
msgstr "Falhou ao actualizar."

#: includes/admin/views/upgrade/network.php:162
msgid "Upgrade complete."
msgstr "Actualização concluída."

#. translators: %s the version being upgraded to.
#. translators: %s the new ACF version
#: includes/admin/views/upgrade/network.php:148
#: includes/admin/views/upgrade/upgrade.php:29
msgid "Upgrading data to version %s"
msgstr "A actualizar dados para a versão %s"

#: includes/admin/views/upgrade/network.php:120
#: includes/admin/views/upgrade/notice.php:46
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"É recomendável que faça uma cópia de segurança da sua base de dados antes de "
"continuar. Tem a certeza que quer actualizar agora?"

#: includes/admin/views/upgrade/network.php:116
msgid "Please select at least one site to upgrade."
msgstr "Por favor, seleccione pelo menos um site para actualizar."

#. translators: %s admin dashboard url page
#: includes/admin/views/upgrade/network.php:96
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Actualização da base de dados concluída. <a href=\"%s\">Voltar ao painel da "
"rede</a>"

#: includes/admin/views/upgrade/network.php:79
msgid "Site is up to date"
msgstr "O site está actualizado"

#. translators: %1 current db version, %2 available db version
#: includes/admin/views/upgrade/network.php:77
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr "O site necessita de actualizar a base de dados de %1$s para %2$s"

#: includes/admin/views/upgrade/network.php:34
#: includes/admin/views/upgrade/network.php:45
msgid "Site"
msgstr "Site"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
#: includes/admin/views/upgrade/network.php:25
#: includes/admin/views/upgrade/network.php:94
msgid "Upgrade Sites"
msgstr "Actualizar sites"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Os sites seguintes necessitam de actualização da BD. Seleccione os que quer "
"actualizar e clique em %s."

#: includes/admin/views/acf-field-group/conditional-logic.php:184
#: includes/admin/views/acf-field-group/locations.php:37
msgid "Add rule group"
msgstr "Adicionar grupo de regras"

#: includes/admin/views/acf-field-group/locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Crie um conjunto de regras para determinar em que ecrãs de edição serão "
"utilizados estes campos personalizados avançados"

#: includes/admin/views/acf-field-group/locations.php:9
msgid "Rules"
msgstr "Regras"

#: includes/admin/tools/class-acf-admin-tool-export.php:449
msgid "Copied"
msgstr "Copiado"

#: includes/admin/tools/class-acf-admin-tool-export.php:425
msgid "Copy to clipboard"
msgstr "Copiar para a área de transferência"

#: includes/admin/tools/class-acf-admin-tool-export.php:331
msgid ""
"Select the items you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""
"Seleccione os itens que deseja exportar e seleccione o método de exportação. "
"Utilize o Exportar como JSON para exportar um ficheiro .json que poderá "
"depois importar para outra instalação do ACF. Utilize o botão Gerar PHP para "
"exportar o código PHP que poderá incorporar no seu tema."

#: includes/admin/tools/class-acf-admin-tool-export.php:215
msgid "Select Field Groups"
msgstr "Seleccione os grupos de campos"

#: includes/admin/tools/class-acf-admin-tool-export.php:88
#: includes/admin/tools/class-acf-admin-tool-export.php:121
msgid "No field groups selected"
msgstr "Nenhum grupo de campos seleccionado"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:339
#: includes/admin/tools/class-acf-admin-tool-export.php:363
msgid "Generate PHP"
msgstr "Gerar PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:34
msgid "Export Field Groups"
msgstr "Exportar grupos de campos"

#: includes/admin/tools/class-acf-admin-tool-import.php:172
msgid "Import file empty"
msgstr "Ficheiro de importação vazio"

#: includes/admin/tools/class-acf-admin-tool-import.php:163
msgid "Incorrect file type"
msgstr "Tipo de ficheiro incorrecto"

#: includes/admin/tools/class-acf-admin-tool-import.php:158
msgid "Error uploading file. Please try again"
msgstr "Erro ao carregar ficheiro. Por favor tente de novo."

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the items in that file."
msgstr ""
"Seleccione o ficheiro JSON do Advanced Custom Fields que deseja importar. Ao "
"clicar no botão Importar abaixo, o ACF irá importar os itens desse ficheiro."

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "Importar grupos de campos"

#: includes/admin/admin-internal-post-type-list.php:417
msgid "Sync"
msgstr "Sincronizar"

#. translators: %s: field group title
#: includes/admin/admin-internal-post-type-list.php:960
msgid "Select %s"
msgstr "Seleccionar %s"

#: includes/admin/admin-internal-post-type-list.php:460
#: includes/admin/admin-internal-post-type-list.php:492
#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate"
msgstr "Duplicar"

#: includes/admin/admin-internal-post-type-list.php:460
msgid "Duplicate this item"
msgstr "Duplicar este item"

#: includes/admin/views/acf-post-type/advanced-settings.php:41
msgid "Supports"
msgstr "Suporte"

#: includes/admin/admin.php:355
#: includes/admin/views/browse-fields-modal.php:102
msgid "Documentation"
msgstr "Documentação"

#: includes/admin/post-types/admin-field-groups.php:90
#: includes/admin/post-types/admin-post-types.php:110
#: includes/admin/post-types/admin-taxonomies.php:109
#: includes/admin/views/acf-field-group/options.php:249
#: includes/admin/views/acf-post-type/advanced-settings.php:62
#: includes/admin/views/acf-taxonomy/advanced-settings.php:114
#: includes/admin/views/upgrade/network.php:36
#: includes/admin/views/upgrade/network.php:47
msgid "Description"
msgstr "Descrição"

#: includes/admin/admin-internal-post-type-list.php:414
#: includes/admin/admin-internal-post-type-list.php:832
msgid "Sync available"
msgstr "Sincronização disponível"

#. translators: %s number of field groups synchronized
#: includes/admin/post-types/admin-field-groups.php:374
msgid "Field group synchronized."
msgid_plural "%s field groups synchronized."
msgstr[0] "Grupo de campos sincronizado."
msgstr[1] "%s grupos de campos sincronizados."

#. translators: %s number of field groups duplicated
#: includes/admin/post-types/admin-field-groups.php:367
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Grupo de campos duplicado."
msgstr[1] "%s grupos de campos duplicados."

#: includes/admin/admin-internal-post-type-list.php:155
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Activo <span class=\"count\">(%s)</span>"
msgstr[1] "Activos <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:251
msgid "Review sites & upgrade"
msgstr "Rever sites e actualizar"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:90
#: includes/admin/admin-upgrade.php:91 includes/admin/admin-upgrade.php:227
#: includes/admin/views/upgrade/network.php:21
#: includes/admin/views/upgrade/upgrade.php:23
msgid "Upgrade Database"
msgstr "Actualizar base de dados"

#: includes/admin/views/acf-field-group/options.php:175
#: includes/admin/views/acf-post-type/advanced-settings.php:30
msgid "Custom Fields"
msgstr "Campos personalizados"

#: includes/admin/post-types/admin-field-group.php:609
msgid "Move Field"
msgstr "Mover campo"

#: includes/admin/post-types/admin-field-group.php:602
#: includes/admin/post-types/admin-field-group.php:606
msgid "Please select the destination for this field"
msgstr "Por favor seleccione o destinho para este campo"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/post-types/admin-field-group.php:568
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "O campo %1$s pode agora ser encontrado no grupo de campos %2$s"

#: includes/admin/post-types/admin-field-group.php:565
msgid "Move Complete."
msgstr "Movido com sucesso."

#: includes/admin/views/acf-field-group/field.php:52
#: includes/admin/views/acf-field-group/options.php:217
#: includes/admin/views/acf-post-type/advanced-settings.php:78
#: includes/admin/views/acf-taxonomy/advanced-settings.php:130
msgid "Active"
msgstr "Activo"

#: includes/admin/post-types/admin-field-group.php:276
msgid "Field Keys"
msgstr "Chaves dos campos"

#: includes/admin/post-types/admin-field-group.php:180
msgid "Settings"
msgstr "Definições"

#: includes/admin/post-types/admin-field-groups.php:92
msgid "Location"
msgstr "Localização"

#: includes/admin/post-types/admin-field-group.php:100
msgid "Null"
msgstr "Nulo"

#: includes/admin/post-types/admin-field-group.php:97
#: includes/class-acf-internal-post-type.php:728
#: includes/post-types/class-acf-field-group.php:345
msgid "copy"
msgstr "cópia"

#: includes/admin/post-types/admin-field-group.php:96
msgid "(this field)"
msgstr "(este campo)"

#: includes/admin/post-types/admin-field-group.php:94
msgid "Checked"
msgstr "Seleccionado"

#: includes/admin/post-types/admin-field-group.php:90
msgid "Move Custom Field"
msgstr "Mover campo personalizado"

#: includes/admin/post-types/admin-field-group.php:89
msgid "No toggle fields available"
msgstr "Nenhum campo de opções disponível"

#: includes/admin/post-types/admin-field-group.php:87
msgid "Field group title is required"
msgstr "O título do grupo de campos é obrigatório"

#: includes/admin/post-types/admin-field-group.php:86
msgid "This field cannot be moved until its changes have been saved"
msgstr ""
"Este campo não pode ser movido até que as suas alterações sejam guardadas"

#: includes/admin/post-types/admin-field-group.php:85
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "O prefixo \"field_\" não pode ser utilizado no início do nome do campo"

#: includes/admin/post-types/admin-field-group.php:69
msgid "Field group draft updated."
msgstr "Rascunho de grupo de campos actualizado."

#: includes/admin/post-types/admin-field-group.php:68
msgid "Field group scheduled for."
msgstr "Grupo de campos agendado."

#: includes/admin/post-types/admin-field-group.php:67
msgid "Field group submitted."
msgstr "Grupo de campos enviado."

#: includes/admin/post-types/admin-field-group.php:66
msgid "Field group saved."
msgstr "Grupo de campos guardado."

#: includes/admin/post-types/admin-field-group.php:65
msgid "Field group published."
msgstr "Grupo de campos publicado."

#: includes/admin/post-types/admin-field-group.php:62
msgid "Field group deleted."
msgstr "Grupo de campos eliminado."

#: includes/admin/post-types/admin-field-group.php:60
#: includes/admin/post-types/admin-field-group.php:61
#: includes/admin/post-types/admin-field-group.php:63
msgid "Field group updated."
msgstr "Grupo de campos actualizado."

#: includes/admin/admin-tools.php:107
#: includes/admin/views/global/navigation.php:251
#: includes/admin/views/tools/tools.php:13
msgid "Tools"
msgstr "Ferramentas"

#: includes/locations/abstract-acf-location.php:105
msgid "is not equal to"
msgstr "não é igual a"

#: includes/locations/abstract-acf-location.php:104
msgid "is equal to"
msgstr "é igual a"

#: includes/locations.php:104
msgid "Forms"
msgstr "Formulários"

#: includes/admin/post-types/admin-post-type.php:125 includes/locations.php:102
#: includes/locations/class-acf-location-page.php:22
msgid "Page"
msgstr "Página"

#: includes/admin/post-types/admin-post-type.php:123 includes/locations.php:101
#: includes/locations/class-acf-location-post.php:22
msgid "Post"
msgstr "Artigo"

#: includes/fields.php:328
msgid "Relational"
msgstr "Relacional"

#: includes/fields.php:327
msgid "Choice"
msgstr "Opção"

#: includes/fields.php:325
msgid "Basic"
msgstr "Básico"

#: includes/fields.php:276
msgid "Unknown"
msgstr "Desconhecido"

#: includes/fields.php:276
msgid "Field type does not exist"
msgstr "Tipo de campo não existe"

#: includes/forms/form-front.php:220
msgid "Spam Detected"
msgstr "Spam detectado"

#: includes/forms/form-front.php:103
msgid "Post updated"
msgstr "Conteúdo actualizado"

#: includes/forms/form-front.php:102
msgid "Update"
msgstr "Actualizar"

#: includes/forms/form-front.php:63
msgid "Validate Email"
msgstr "Validar email"

#: includes/fields.php:326 includes/forms/form-front.php:55
msgid "Content"
msgstr "Conteúdo"

#: includes/admin/views/acf-post-type/advanced-settings.php:21
#: includes/forms/form-front.php:46
msgid "Title"
msgstr "Título"

#: includes/assets.php:376 includes/forms/form-comment.php:140
msgid "Edit field group"
msgstr "Editar grupo de campos"

#: includes/admin/post-types/admin-field-group.php:113
msgid "Selection is less than"
msgstr "A selecção é menor do que"

#: includes/admin/post-types/admin-field-group.php:112
msgid "Selection is greater than"
msgstr "A selecção é maior do que"

#: includes/admin/post-types/admin-field-group.php:111
msgid "Value is less than"
msgstr "O valor é menor do que"

#: includes/admin/post-types/admin-field-group.php:110
msgid "Value is greater than"
msgstr "O valor é maior do que"

#: includes/admin/post-types/admin-field-group.php:109
msgid "Value contains"
msgstr "O valor contém"

#: includes/admin/post-types/admin-field-group.php:108
msgid "Value matches pattern"
msgstr "O valor corresponde ao padrão"

#: includes/admin/post-types/admin-field-group.php:107
msgid "Value is not equal to"
msgstr "O valor é diferente de"

#: includes/admin/post-types/admin-field-group.php:106
msgid "Value is equal to"
msgstr "O valor é igual a"

#: includes/admin/post-types/admin-field-group.php:105
msgid "Has no value"
msgstr "Não tem valor"

#: includes/admin/post-types/admin-field-group.php:104
msgid "Has any value"
msgstr "Tem um valor qualquer"

#: includes/admin/admin-internal-post-type.php:337
#: includes/admin/views/browse-fields-modal.php:72 includes/assets.php:354
msgid "Cancel"
msgstr "Cancelar"

#: includes/assets.php:350
msgid "Are you sure?"
msgstr "Tem a certeza?"

#: includes/assets.php:370
msgid "%d fields require attention"
msgstr "%d campos requerem a sua atenção"

#: includes/assets.php:369
msgid "1 field requires attention"
msgstr "1 campo requer a sua atenção"

#: includes/assets.php:368 includes/validation.php:257
#: includes/validation.php:265
msgid "Validation failed"
msgstr "A validação falhou"

#: includes/assets.php:367
msgid "Validation successful"
msgstr "Validação bem sucedida"

#: includes/media.php:54
msgid "Restricted"
msgstr "Restrito"

#: includes/media.php:53
msgid "Collapse Details"
msgstr "Minimizar detalhes"

#: includes/media.php:52
msgid "Expand Details"
msgstr "Expandir detalhes"

#: includes/admin/views/acf-post-type/advanced-settings.php:470
#: includes/media.php:51
msgid "Uploaded to this post"
msgstr "Carregados neste artigo"

#: includes/media.php:50
msgctxt "verb"
msgid "Update"
msgstr "Actualizar"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Editar"

#: includes/assets.php:364
msgid "The changes you made will be lost if you navigate away from this page"
msgstr ""
"As alterações que fez serão ignoradas se navegar para fora desta página"

#: includes/api/api-helpers.php:3000
msgid "File type must be %s."
msgstr "O tipo de ficheiro deve ser %s."

#: includes/admin/post-types/admin-field-group.php:98
#: includes/admin/views/acf-field-group/conditional-logic.php:64
#: includes/admin/views/acf-field-group/conditional-logic.php:182
#: includes/admin/views/acf-field-group/location-group.php:3
#: includes/admin/views/acf-field-group/locations.php:35
#: includes/api/api-helpers.php:2997
msgid "or"
msgstr "ou"

#: includes/api/api-helpers.php:2973
msgid "File size must not exceed %s."
msgstr "O tamanho do ficheiro não deve exceder %s."

#: includes/api/api-helpers.php:2969
msgid "File size must be at least %s."
msgstr "O tamanho do ficheiro deve ser pelo menos de %s."

#: includes/api/api-helpers.php:2956
msgid "Image height must not exceed %dpx."
msgstr "A altura da imagem não deve exceder os %dpx."

#: includes/api/api-helpers.php:2952
msgid "Image height must be at least %dpx."
msgstr "A altura da imagem deve ser pelo menos de %dpx."

#: includes/api/api-helpers.php:2940
msgid "Image width must not exceed %dpx."
msgstr "A largura da imagem não deve exceder os %dpx."

#: includes/api/api-helpers.php:2936
msgid "Image width must be at least %dpx."
msgstr "A largura da imagem deve ser pelo menos de %dpx."

#: includes/api/api-helpers.php:1425 includes/api/api-term.php:140
msgid "(no title)"
msgstr "(sem título)"

#: includes/api/api-helpers.php:781
msgid "Full Size"
msgstr "Tamanho original"

#: includes/api/api-helpers.php:746
msgid "Large"
msgstr "Grande"

#: includes/api/api-helpers.php:745
msgid "Medium"
msgstr "Média"

#: includes/api/api-helpers.php:744
msgid "Thumbnail"
msgstr "Miniatura"

#: includes/acf-field-functions.php:854
#: includes/admin/post-types/admin-field-group.php:95
msgid "(no label)"
msgstr "(sem legenda)"

#: includes/fields/class-acf-field-textarea.php:135
msgid "Sets the textarea height"
msgstr "Define a altura da área de texto"

#: includes/fields/class-acf-field-textarea.php:134
msgid "Rows"
msgstr "Linhas"

#: includes/fields/class-acf-field-textarea.php:22
msgid "Text Area"
msgstr "Área de texto"

#: includes/fields/class-acf-field-checkbox.php:421
msgid "Prepend an extra checkbox to toggle all choices"
msgstr ""
"Preceder com caixa de selecção adicional para seleccionar todas as opções"

#: includes/fields/class-acf-field-checkbox.php:383
msgid "Save 'custom' values to the field's choices"
msgstr "Guarda valores personalizados nas opções do campo"

#: includes/fields/class-acf-field-checkbox.php:372
msgid "Allow 'custom' values to be added"
msgstr "Permite adicionar valores personalizados"

#: includes/fields/class-acf-field-checkbox.php:35
msgid "Add new choice"
msgstr "Adicionar nova opção"

#: includes/fields/class-acf-field-checkbox.php:157
msgid "Toggle All"
msgstr "Seleccionar tudo"

#: includes/fields/class-acf-field-page_link.php:487
msgid "Allow Archives URLs"
msgstr "Permitir URL do arquivo"

#: includes/fields/class-acf-field-page_link.php:196
msgid "Archives"
msgstr "Arquivo"

#: includes/fields/class-acf-field-page_link.php:22
msgid "Page Link"
msgstr "Ligação de página"

#: includes/fields/class-acf-field-taxonomy.php:884
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Adicionar"

#: includes/admin/views/acf-field-group/fields.php:53
#: includes/fields/class-acf-field-taxonomy.php:854
msgid "Name"
msgstr "Nome"

#: includes/fields/class-acf-field-taxonomy.php:839
msgid "%s added"
msgstr "%s adicionado(a)"

#: includes/fields/class-acf-field-taxonomy.php:803
msgid "%s already exists"
msgstr "%s já existe"

#: includes/fields/class-acf-field-taxonomy.php:791
msgid "User unable to add new %s"
msgstr "O utilizador não pôde adicionar novo(a) %s"

#: includes/fields/class-acf-field-taxonomy.php:678
msgid "Term ID"
msgstr "ID do termo"

#: includes/fields/class-acf-field-taxonomy.php:677
msgid "Term Object"
msgstr "Termo"

#: includes/fields/class-acf-field-taxonomy.php:662
msgid "Load value from posts terms"
msgstr "Carregar os valores a partir dos termos dos conteúdos"

#: includes/fields/class-acf-field-taxonomy.php:661
msgid "Load Terms"
msgstr "Carregar termos"

#: includes/fields/class-acf-field-taxonomy.php:651
msgid "Connect selected terms to the post"
msgstr "Liga os termos seleccionados ao conteúdo."

#: includes/fields/class-acf-field-taxonomy.php:650
msgid "Save Terms"
msgstr "Guardar termos"

#: includes/fields/class-acf-field-taxonomy.php:640
msgid "Allow new terms to be created whilst editing"
msgstr "Permite a criação de novos termos durante a edição"

#: includes/fields/class-acf-field-taxonomy.php:639
msgid "Create Terms"
msgstr "Criar termos"

#: includes/fields/class-acf-field-taxonomy.php:698
msgid "Radio Buttons"
msgstr "Botões de opções"

#: includes/fields/class-acf-field-taxonomy.php:697
msgid "Single Value"
msgstr "Valor único"

#: includes/fields/class-acf-field-taxonomy.php:695
msgid "Multi Select"
msgstr "Selecção múltipla"

#: includes/fields/class-acf-field-checkbox.php:22
#: includes/fields/class-acf-field-taxonomy.php:694
msgid "Checkbox"
msgstr "Caixa de selecção"

#: includes/fields/class-acf-field-taxonomy.php:693
msgid "Multiple Values"
msgstr "Valores múltiplos"

#: includes/fields/class-acf-field-taxonomy.php:688
msgid "Select the appearance of this field"
msgstr "Seleccione a apresentação deste campo"

#: includes/fields/class-acf-field-taxonomy.php:687
msgid "Appearance"
msgstr "Apresentação"

#: includes/fields/class-acf-field-taxonomy.php:629
msgid "Select the taxonomy to be displayed"
msgstr "Seleccione a taxonomia que será mostrada"

#: includes/fields/class-acf-field-taxonomy.php:593
msgctxt "No Terms"
msgid "No %s"
msgstr "Nenhum %s"

#: includes/fields/class-acf-field-number.php:240
msgid "Value must be equal to or lower than %d"
msgstr "O valor deve ser igual ou inferior a %d"

#: includes/fields/class-acf-field-number.php:235
msgid "Value must be equal to or higher than %d"
msgstr "O valor deve ser igual ou superior a %d"

#: includes/fields/class-acf-field-number.php:223
msgid "Value must be a number"
msgstr "O valor deve ser um número"

#: includes/fields/class-acf-field-number.php:22
msgid "Number"
msgstr "Número"

#: includes/fields/class-acf-field-radio.php:254
msgid "Save 'other' values to the field's choices"
msgstr "Guardar 'outros' valores nas opções do campo"

#: includes/fields/class-acf-field-radio.php:243
msgid "Add 'other' choice to allow for custom values"
msgstr ""
"Adicionar opção 'outros' para permitir a inserção de valores personalizados"

#: includes/admin/views/global/navigation.php:199
msgid "Other"
msgstr "Outro"

#: includes/fields/class-acf-field-radio.php:22
msgid "Radio Button"
msgstr "Botão de opção"

#: includes/fields/class-acf-field-accordion.php:106
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Define o fim do acordeão anterior. Este item de acordeão não será visível."

#: includes/fields/class-acf-field-accordion.php:95
msgid "Allow this accordion to open without closing others."
msgstr "Permite abrir este item de acordeão sem fechar os restantes."

#: includes/fields/class-acf-field-accordion.php:94
msgid "Multi-Expand"
msgstr "Expandir múltiplos"

#: includes/fields/class-acf-field-accordion.php:84
msgid "Display this accordion as open on page load."
msgstr "Mostrar este item de acordeão aberto ao carregar a página."

#: includes/fields/class-acf-field-accordion.php:83
msgid "Open"
msgstr "Aberto"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "Acordeão"

#: includes/fields/class-acf-field-file.php:253
#: includes/fields/class-acf-field-file.php:265
msgid "Restrict which files can be uploaded"
msgstr "Restringe que ficheiros podem ser carregados"

#: includes/fields/class-acf-field-file.php:207
msgid "File ID"
msgstr "ID do ficheiro"

#: includes/fields/class-acf-field-file.php:206
msgid "File URL"
msgstr "URL do ficheiro"

#: includes/fields/class-acf-field-file.php:205
msgid "File Array"
msgstr "Array do ficheiro"

#: includes/fields/class-acf-field-file.php:176
msgid "Add File"
msgstr "Adicionar ficheiro"

#: includes/admin/tools/class-acf-admin-tool-import.php:151
#: includes/fields/class-acf-field-file.php:176
msgid "No file selected"
msgstr "Nenhum ficheiro seleccionado"

#: includes/fields/class-acf-field-file.php:140
msgid "File name"
msgstr "Nome do ficheiro"

#: includes/fields/class-acf-field-file.php:57
msgid "Update File"
msgstr "Actualizar ficheiro"

#: includes/fields/class-acf-field-file.php:56
msgid "Edit File"
msgstr "Editar ficheiro"

#: includes/admin/tools/class-acf-admin-tool-import.php:55
#: includes/fields/class-acf-field-file.php:55
msgid "Select File"
msgstr "Seleccionar ficheiro"

#: includes/fields/class-acf-field-file.php:22
msgid "File"
msgstr "Ficheiro"

#: includes/fields/class-acf-field-password.php:22
msgid "Password"
msgstr "Senha"

#: includes/fields/class-acf-field-select.php:363
msgid "Specify the value returned"
msgstr "Especifique o valor devolvido"

#: includes/fields/class-acf-field-select.php:431
msgid "Use AJAX to lazy load choices?"
msgstr "Utilizar AJAX para carregar opções?"

#: includes/fields/class-acf-field-checkbox.php:333
#: includes/fields/class-acf-field-select.php:352
msgid "Enter each default value on a new line"
msgstr "Insira cada valor por omissão numa linha separada"

#: includes/fields/class-acf-field-select.php:227 includes/media.php:48
msgctxt "verb"
msgid "Select"
msgstr "Seleccionar"

#: includes/fields/class-acf-field-select.php:101
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Falhou ao carregar"

#: includes/fields/class-acf-field-select.php:100
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "A pesquisar..."

#: includes/fields/class-acf-field-select.php:99
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "A carregar mais resultados&hellip;"

#. translators: %d - maximum number of items that can be selected in the select
#. field
#: includes/fields/class-acf-field-select.php:98
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Só pode seleccionar %d itens"

#: includes/fields/class-acf-field-select.php:96
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Só pode seleccionar 1 item"

#. translators: %d - number of characters that should be removed from select
#. field
#: includes/fields/class-acf-field-select.php:95
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Por favor elimine %d caracteres"

#: includes/fields/class-acf-field-select.php:93
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Por favor elimine 1 caractere"

#. translators: %d - number of characters to enter into select field input
#: includes/fields/class-acf-field-select.php:92
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Por favor insira %d ou mais caracteres"

#: includes/fields/class-acf-field-select.php:90
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Por favor insira 1 ou mais caracteres"

#: includes/fields/class-acf-field-select.php:89
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Nenhuma correspondência encontrada"

#. translators: %d - number of results available in select field
#: includes/fields/class-acf-field-select.php:88
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""
"%d resultados encontrados, use as setas para cima ou baixo para navegar."

#: includes/fields/class-acf-field-select.php:86
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Um resultado encontrado, prima Enter para seleccioná-lo."

#: includes/fields/class-acf-field-select.php:22
#: includes/fields/class-acf-field-taxonomy.php:699
msgctxt "noun"
msgid "Select"
msgstr "Selecção"

#: includes/fields/class-acf-field-user.php:102
msgid "User ID"
msgstr "ID do utilizador"

#: includes/fields/class-acf-field-user.php:101
msgid "User Object"
msgstr "Objecto do utilizador"

#: includes/fields/class-acf-field-user.php:100
msgid "User Array"
msgstr "Array do utilizador"

#: includes/fields/class-acf-field-user.php:88
msgid "All user roles"
msgstr "Todos os papéis de utilizador"

#: includes/fields/class-acf-field-user.php:80
msgid "Filter by Role"
msgstr "Filtrar por papel"

#: includes/fields/class-acf-field-user.php:15 includes/locations.php:103
msgid "User"
msgstr "Utilizador"

#: includes/fields/class-acf-field-separator.php:22
msgid "Separator"
msgstr "Divisória"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Select Color"
msgstr "Seleccionar cor"

#: includes/admin/post-types/admin-post-type.php:127
#: includes/admin/post-types/admin-taxonomy.php:129
#: includes/fields/class-acf-field-color_picker.php:67
msgid "Default"
msgstr "Por omissão"

#: includes/admin/views/acf-post-type/advanced-settings.php:89
#: includes/admin/views/acf-taxonomy/advanced-settings.php:141
#: includes/fields/class-acf-field-color_picker.php:65
msgid "Clear"
msgstr "Limpar"

#: includes/fields/class-acf-field-color_picker.php:22
msgid "Color Picker"
msgstr "Selecção de cor"

#: includes/fields/class-acf-field-date_time_picker.php:82
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Seleccionar"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Concluído"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Agora"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Fuso horário"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Microsegundo"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Milissegundo"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Segundo"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minuto"

#: includes/fields/class-acf-field-date_time_picker.php:67
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Hora"

#: includes/fields/class-acf-field-date_time_picker.php:66
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Hora"

#: includes/fields/class-acf-field-date_time_picker.php:65
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Escolha a hora"

#: includes/fields/class-acf-field-date_time_picker.php:22
msgid "Date Time Picker"
msgstr "Selecção de data e hora"

#: includes/fields/class-acf-field-accordion.php:105
msgid "Endpoint"
msgstr "Fim"

#: includes/admin/views/acf-field-group/options.php:130
#: includes/fields/class-acf-field-tab.php:112
msgid "Left aligned"
msgstr "Alinhado à esquerda"

#: includes/admin/views/acf-field-group/options.php:129
#: includes/fields/class-acf-field-tab.php:111
msgid "Top aligned"
msgstr "Alinhado acima"

#: includes/fields/class-acf-field-tab.php:107
msgid "Placement"
msgstr "Posição"

#: includes/fields/class-acf-field-tab.php:23
msgid "Tab"
msgstr "Separador"

#: includes/fields/class-acf-field-url.php:138
msgid "Value must be a valid URL"
msgstr "O valor deve ser um URL válido"

#: includes/fields/class-acf-field-link.php:153
msgid "Link URL"
msgstr "URL da ligação"

#: includes/fields/class-acf-field-link.php:152
msgid "Link Array"
msgstr "Array da ligação"

#: includes/fields/class-acf-field-link.php:124
msgid "Opens in a new window/tab"
msgstr "Abre numa nova janela/separador"

#: includes/fields/class-acf-field-link.php:119
msgid "Select Link"
msgstr "Seleccionar ligação"

#: includes/fields/class-acf-field-link.php:22
msgid "Link"
msgstr "Ligação"

#: includes/fields/class-acf-field-email.php:22
msgid "Email"
msgstr "Email"

#: includes/fields/class-acf-field-number.php:173
#: includes/fields/class-acf-field-range.php:206
msgid "Step Size"
msgstr "Valor dos passos"

#: includes/fields/class-acf-field-number.php:143
#: includes/fields/class-acf-field-range.php:184
msgid "Maximum Value"
msgstr "Valor máximo"

#: includes/fields/class-acf-field-number.php:133
#: includes/fields/class-acf-field-range.php:173
msgid "Minimum Value"
msgstr "Valor mínimo"

#: includes/fields/class-acf-field-range.php:22
msgid "Range"
msgstr "Intervalo"

#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:350
#: includes/fields/class-acf-field-radio.php:210
#: includes/fields/class-acf-field-select.php:370
msgid "Both (Array)"
msgstr "Ambos (Array)"

#: includes/admin/views/acf-field-group/fields.php:52
#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:349
#: includes/fields/class-acf-field-radio.php:209
#: includes/fields/class-acf-field-select.php:369
msgid "Label"
msgstr "Legenda"

#: includes/fields/class-acf-field-button-group.php:163
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:208
#: includes/fields/class-acf-field-select.php:368
msgid "Value"
msgstr "Valor"

#: includes/fields/class-acf-field-button-group.php:211
#: includes/fields/class-acf-field-checkbox.php:411
#: includes/fields/class-acf-field-radio.php:282
msgid "Vertical"
msgstr "Vertical"

#: includes/fields/class-acf-field-button-group.php:210
#: includes/fields/class-acf-field-checkbox.php:412
#: includes/fields/class-acf-field-radio.php:283
msgid "Horizontal"
msgstr "Horizontal"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "red : Red"
msgstr "vermelho : Vermelho"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Para maior controlo, pode especificar tanto os valores como as legendas:"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "Enter each choice on a new line."
msgstr "Insira cada opção numa linha separada."

#: includes/fields/class-acf-field-button-group.php:137
#: includes/fields/class-acf-field-checkbox.php:322
#: includes/fields/class-acf-field-radio.php:182
#: includes/fields/class-acf-field-select.php:340
msgid "Choices"
msgstr "Opções"

#: includes/fields/class-acf-field-button-group.php:23
msgid "Button Group"
msgstr "Grupo de botões"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-page_link.php:519
#: includes/fields/class-acf-field-post_object.php:432
#: includes/fields/class-acf-field-radio.php:228
#: includes/fields/class-acf-field-select.php:399
#: includes/fields/class-acf-field-taxonomy.php:708
#: includes/fields/class-acf-field-user.php:132
msgid "Allow Null"
msgstr "Permitir nulo"

#: includes/fields/class-acf-field-page_link.php:273
#: includes/fields/class-acf-field-post_object.php:254
#: includes/fields/class-acf-field-taxonomy.php:872
msgid "Parent"
msgstr "Superior"

#: includes/fields/class-acf-field-wysiwyg.php:367
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "O TinyMCE não será inicializado até que clique no campo"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Delay Initialization"
msgstr "Atrasar a inicialização"

#: includes/fields/class-acf-field-wysiwyg.php:355
msgid "Show Media Upload Buttons"
msgstr "Mostrar botões de carregar multimédia"

#: includes/fields/class-acf-field-wysiwyg.php:339
msgid "Toolbar"
msgstr "Barra de ferramentas"

#: includes/fields/class-acf-field-wysiwyg.php:331
msgid "Text Only"
msgstr "Apenas HTML"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual Only"
msgstr "Apenas visual"

#: includes/fields/class-acf-field-wysiwyg.php:329
msgid "Visual & Text"
msgstr "Visual e HTML"

#: includes/fields/class-acf-field-icon_picker.php:237
#: includes/fields/class-acf-field-wysiwyg.php:324
msgid "Tabs"
msgstr "Separadores"

#: includes/fields/class-acf-field-wysiwyg.php:268
msgid "Click to initialize TinyMCE"
msgstr "Clique para inicializar o TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:262
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "HTML"

#: includes/fields/class-acf-field-wysiwyg.php:261
msgid "Visual"
msgstr "Visual"

#: includes/fields/class-acf-field-text.php:181
#: includes/fields/class-acf-field-textarea.php:217
msgid "Value must not exceed %d characters"
msgstr "O valor não deve exceder %d caracteres"

#: includes/fields/class-acf-field-text.php:116
#: includes/fields/class-acf-field-textarea.php:114
msgid "Leave blank for no limit"
msgstr "Deixe em branco para não limitar"

#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:113
msgid "Character Limit"
msgstr "Limite de caracteres"

#: includes/fields/class-acf-field-email.php:144
#: includes/fields/class-acf-field-number.php:194
#: includes/fields/class-acf-field-password.php:95
#: includes/fields/class-acf-field-range.php:228
#: includes/fields/class-acf-field-text.php:156
msgid "Appears after the input"
msgstr "Mostrado depois do campo"

#: includes/fields/class-acf-field-email.php:143
#: includes/fields/class-acf-field-number.php:193
#: includes/fields/class-acf-field-password.php:94
#: includes/fields/class-acf-field-range.php:227
#: includes/fields/class-acf-field-text.php:155
msgid "Append"
msgstr "Suceder"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:184
#: includes/fields/class-acf-field-password.php:85
#: includes/fields/class-acf-field-range.php:218
#: includes/fields/class-acf-field-text.php:146
msgid "Appears before the input"
msgstr "Mostrado antes do campo"

#: includes/fields/class-acf-field-email.php:133
#: includes/fields/class-acf-field-number.php:183
#: includes/fields/class-acf-field-password.php:84
#: includes/fields/class-acf-field-range.php:217
#: includes/fields/class-acf-field-text.php:145
msgid "Prepend"
msgstr "Preceder"

#: includes/fields/class-acf-field-email.php:124
#: includes/fields/class-acf-field-number.php:164
#: includes/fields/class-acf-field-password.php:75
#: includes/fields/class-acf-field-text.php:136
#: includes/fields/class-acf-field-textarea.php:146
#: includes/fields/class-acf-field-url.php:105
msgid "Appears within the input"
msgstr "Mostrado dentro do campo"

#: includes/fields/class-acf-field-email.php:123
#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-password.php:74
#: includes/fields/class-acf-field-text.php:135
#: includes/fields/class-acf-field-textarea.php:145
#: includes/fields/class-acf-field-url.php:104
msgid "Placeholder Text"
msgstr "Texto predefinido"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-email.php:104
#: includes/fields/class-acf-field-number.php:114
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-range.php:154
#: includes/fields/class-acf-field-text.php:96
#: includes/fields/class-acf-field-textarea.php:94
#: includes/fields/class-acf-field-url.php:85
#: includes/fields/class-acf-field-wysiwyg.php:292
msgid "Appears when creating a new post"
msgstr "Mostrado ao criar um novo conteúdo"

#: includes/fields/class-acf-field-text.php:22
msgid "Text"
msgstr "Texto"

#: includes/fields/class-acf-field-relationship.php:753
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s requer pelo menos %2$s selecção"
msgstr[1] "%1$s requer pelo menos %2$s selecções"

#: includes/fields/class-acf-field-post_object.php:402
#: includes/fields/class-acf-field-relationship.php:616
msgid "Post ID"
msgstr "ID do conteúdo"

#: includes/fields/class-acf-field-post_object.php:15
#: includes/fields/class-acf-field-post_object.php:401
#: includes/fields/class-acf-field-relationship.php:615
msgid "Post Object"
msgstr "Conteúdo"

#: includes/fields/class-acf-field-relationship.php:648
msgid "Maximum Posts"
msgstr "Máximo de conteúdos"

#: includes/fields/class-acf-field-relationship.php:638
msgid "Minimum Posts"
msgstr "Mínimo de conteúdos"

#: includes/admin/views/acf-field-group/options.php:183
#: includes/admin/views/acf-post-type/advanced-settings.php:29
#: includes/fields/class-acf-field-relationship.php:673
msgid "Featured Image"
msgstr "Imagem de destaque"

#: includes/fields/class-acf-field-relationship.php:669
msgid "Selected elements will be displayed in each result"
msgstr "Os elementos seleccionados serão mostrados em cada resultado"

#: includes/fields/class-acf-field-relationship.php:668
msgid "Elements"
msgstr "Elementos"

#: includes/fields/class-acf-field-relationship.php:602
#: includes/fields/class-acf-field-taxonomy.php:20
#: includes/fields/class-acf-field-taxonomy.php:628
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Taxonomia"

#: includes/fields/class-acf-field-relationship.php:601
#: includes/locations/class-acf-location-post-type.php:22
#: includes/post-types/class-acf-post-type.php:92
msgid "Post Type"
msgstr "Tipo de conteúdo"

#: includes/fields/class-acf-field-relationship.php:595
msgid "Filters"
msgstr "Filtros"

#: includes/fields/class-acf-field-page_link.php:480
#: includes/fields/class-acf-field-post_object.php:389
#: includes/fields/class-acf-field-relationship.php:588
msgid "All taxonomies"
msgstr "Todas as taxonomias"

#: includes/fields/class-acf-field-page_link.php:472
#: includes/fields/class-acf-field-post_object.php:381
#: includes/fields/class-acf-field-relationship.php:580
msgid "Filter by Taxonomy"
msgstr "Filtrar por taxonomia"

#: includes/fields/class-acf-field-page_link.php:450
#: includes/fields/class-acf-field-post_object.php:359
#: includes/fields/class-acf-field-relationship.php:558
msgid "All post types"
msgstr "Todos os tipos de conteúdo"

#: includes/fields/class-acf-field-page_link.php:442
#: includes/fields/class-acf-field-post_object.php:351
#: includes/fields/class-acf-field-relationship.php:550
msgid "Filter by Post Type"
msgstr "Filtrar por tipo de conteúdo"

#: includes/fields/class-acf-field-relationship.php:450
msgid "Search..."
msgstr "Pesquisar..."

#: includes/fields/class-acf-field-relationship.php:380
msgid "Select taxonomy"
msgstr "Seleccionar taxonomia"

#: includes/fields/class-acf-field-relationship.php:372
msgid "Select post type"
msgstr "Seleccionar tipo de conteúdo"

#: includes/fields/class-acf-field-relationship.php:78
msgid "No matches found"
msgstr "Nenhuma correspondência encontrada"

#: includes/fields/class-acf-field-relationship.php:77
msgid "Loading"
msgstr "A carregar"

#: includes/fields/class-acf-field-relationship.php:76
msgid "Maximum values reached ( {max} values )"
msgstr "Valor máximo alcançado ( valor {max} )"

#: includes/fields/class-acf-field-relationship.php:17
msgid "Relationship"
msgstr "Relação"

#: includes/fields/class-acf-field-file.php:277
#: includes/fields/class-acf-field-image.php:307
msgid "Comma separated list. Leave blank for all types"
msgstr ""
"Lista separada por vírgulas. Deixe em branco para permitir todos os tipos."

#: includes/fields/class-acf-field-file.php:276
#: includes/fields/class-acf-field-image.php:306
msgid "Allowed File Types"
msgstr "Tipos de ficheiros permitidos"

#: includes/fields/class-acf-field-file.php:264
#: includes/fields/class-acf-field-image.php:270
msgid "Maximum"
msgstr "Máximo"

#: includes/fields/class-acf-field-file.php:144
#: includes/fields/class-acf-field-file.php:256
#: includes/fields/class-acf-field-file.php:268
#: includes/fields/class-acf-field-image.php:261
#: includes/fields/class-acf-field-image.php:297
msgid "File size"
msgstr "Tamanho do ficheiro"

#: includes/fields/class-acf-field-image.php:235
#: includes/fields/class-acf-field-image.php:271
msgid "Restrict which images can be uploaded"
msgstr "Restringe que imagens podem ser carregadas"

#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-image.php:234
msgid "Minimum"
msgstr "Mínimo"

#: includes/fields/class-acf-field-file.php:222
#: includes/fields/class-acf-field-image.php:200
msgid "Uploaded to post"
msgstr "Carregados no conteúdo"

#: includes/fields/class-acf-field-file.php:221
#: includes/fields/class-acf-field-image.php:199
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "Todos"

#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:194
msgid "Limit the media library choice"
msgstr "Limita a escolha da biblioteca multimédia"

#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:193
msgid "Library"
msgstr "Biblioteca"

#: includes/fields/class-acf-field-image.php:326
msgid "Preview Size"
msgstr "Tamanho da pré-visualização"

#: includes/fields/class-acf-field-image.php:185
msgid "Image ID"
msgstr "ID da imagem"

#: includes/fields/class-acf-field-image.php:184
msgid "Image URL"
msgstr "URL da imagem"

#: includes/fields/class-acf-field-image.php:183
msgid "Image Array"
msgstr "Array da imagem"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-checkbox.php:343
#: includes/fields/class-acf-field-file.php:200
#: includes/fields/class-acf-field-link.php:147
#: includes/fields/class-acf-field-radio.php:203
msgid "Specify the returned value on front end"
msgstr "Especifica o valor devolvido na frente do site."

#: includes/fields/class-acf-field-button-group.php:157
#: includes/fields/class-acf-field-checkbox.php:342
#: includes/fields/class-acf-field-file.php:199
#: includes/fields/class-acf-field-link.php:146
#: includes/fields/class-acf-field-radio.php:202
#: includes/fields/class-acf-field-taxonomy.php:672
msgid "Return Value"
msgstr "Valor devolvido"

#: includes/fields/class-acf-field-image.php:155
msgid "Add Image"
msgstr "Adicionar imagem"

#: includes/fields/class-acf-field-image.php:155
msgid "No image selected"
msgstr "Nenhuma imagem seleccionada"

#: includes/assets.php:353 includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:135
#: includes/fields/class-acf-field-link.php:124
msgid "Remove"
msgstr "Remover"

#: includes/admin/views/acf-field-group/field.php:89
#: includes/fields/class-acf-field-file.php:150
#: includes/fields/class-acf-field-image.php:133
#: includes/fields/class-acf-field-link.php:124
msgid "Edit"
msgstr "Editar"

#: includes/fields/class-acf-field-image.php:63 includes/media.php:55
msgid "All images"
msgstr "Todas as imagens"

#: includes/fields/class-acf-field-image.php:62
msgid "Update Image"
msgstr "Actualizar imagem"

#: includes/fields/class-acf-field-image.php:61
msgid "Edit Image"
msgstr "Editar imagem"

#: includes/fields/class-acf-field-image.php:60
msgid "Select Image"
msgstr "Seleccionar imagem"

#: includes/fields/class-acf-field-image.php:22
msgid "Image"
msgstr "Imagem"

#: includes/fields/class-acf-field-message.php:113
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""
"Permite visualizar o código HTML como texto visível, em vez de o processar"

#: includes/fields/class-acf-field-message.php:112
msgid "Escape HTML"
msgstr "Mostrar HTML"

#: includes/fields/class-acf-field-message.php:104
#: includes/fields/class-acf-field-textarea.php:162
msgid "No Formatting"
msgstr "Sem formatação"

#: includes/fields/class-acf-field-message.php:103
#: includes/fields/class-acf-field-textarea.php:161
msgid "Automatically add &lt;br&gt;"
msgstr "Adicionar &lt;br&gt; automaticamente"

#: includes/fields/class-acf-field-message.php:102
#: includes/fields/class-acf-field-textarea.php:160
msgid "Automatically add paragraphs"
msgstr "Adicionar parágrafos automaticamente"

#: includes/fields/class-acf-field-message.php:98
#: includes/fields/class-acf-field-textarea.php:156
msgid "Controls how new lines are rendered"
msgstr "Controla como serão visualizadas novas linhas"

#: includes/fields/class-acf-field-message.php:97
#: includes/fields/class-acf-field-textarea.php:155
msgid "New Lines"
msgstr "Novas linhas"

#: includes/fields/class-acf-field-date_picker.php:221
#: includes/fields/class-acf-field-date_time_picker.php:208
msgid "Week Starts On"
msgstr "Semana começa em"

#: includes/fields/class-acf-field-date_picker.php:190
msgid "The format used when saving a value"
msgstr "O formato usado ao guardar um valor"

#: includes/fields/class-acf-field-date_picker.php:189
msgid "Save Format"
msgstr "Formato guardado"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Sem"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Anterior"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Seguinte"

#: includes/fields/class-acf-field-date_picker.php:58
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Hoje"

#: includes/fields/class-acf-field-date_picker.php:57
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Concluído"

#: includes/fields/class-acf-field-date_picker.php:22
msgid "Date Picker"
msgstr "Selecção de data"

#: includes/fields/class-acf-field-image.php:238
#: includes/fields/class-acf-field-image.php:274
#: includes/fields/class-acf-field-oembed.php:241
msgid "Width"
msgstr "Largura"

#: includes/fields/class-acf-field-oembed.php:238
#: includes/fields/class-acf-field-oembed.php:250
msgid "Embed Size"
msgstr "Tamanho da incorporação"

#: includes/fields/class-acf-field-oembed.php:198
msgid "Enter URL"
msgstr "Insira o URL"

#: includes/fields/class-acf-field-oembed.php:22
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-true_false.php:172
msgid "Text shown when inactive"
msgstr "Texto mostrado quando inactivo"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Off Text"
msgstr "Texto desligado"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "Texto mostrado quando activo"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "Texto ligado"

#: includes/fields/class-acf-field-select.php:420
#: includes/fields/class-acf-field-true_false.php:187
msgid "Stylized UI"
msgstr "Interface estilizada"

#: includes/fields/class-acf-field-button-group.php:147
#: includes/fields/class-acf-field-checkbox.php:332
#: includes/fields/class-acf-field-color_picker.php:144
#: includes/fields/class-acf-field-email.php:103
#: includes/fields/class-acf-field-number.php:113
#: includes/fields/class-acf-field-radio.php:192
#: includes/fields/class-acf-field-range.php:153
#: includes/fields/class-acf-field-select.php:351
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:93
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:84
#: includes/fields/class-acf-field-wysiwyg.php:291
msgid "Default Value"
msgstr "Valor por omissão"

#: includes/fields/class-acf-field-true_false.php:126
msgid "Displays text alongside the checkbox"
msgstr "Texto mostrado ao lado da caixa de selecção"

#: includes/fields/class-acf-field-message.php:23
#: includes/fields/class-acf-field-message.php:87
#: includes/fields/class-acf-field-true_false.php:125
msgid "Message"
msgstr "Mensagem"

#: includes/assets.php:352 includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:175
#: src/Site_Health/Site_Health.php:281 src/Site_Health/Site_Health.php:338
msgid "No"
msgstr "Não"

#: includes/assets.php:351 includes/fields/class-acf-field-true_false.php:76
#: includes/fields/class-acf-field-true_false.php:159
#: src/Site_Health/Site_Health.php:280 src/Site_Health/Site_Health.php:338
msgid "Yes"
msgstr "Sim"

#: includes/fields/class-acf-field-true_false.php:22
msgid "True / False"
msgstr "Verdadeiro / Falso"

#: includes/fields/class-acf-field-group.php:415
msgid "Row"
msgstr "Linha"

#: includes/fields/class-acf-field-group.php:414
msgid "Table"
msgstr "Tabela"

#: includes/admin/post-types/admin-field-group.php:158
#: includes/fields/class-acf-field-group.php:413
msgid "Block"
msgstr "Bloco"

#: includes/fields/class-acf-field-group.php:408
msgid "Specify the style used to render the selected fields"
msgstr "Especifique o estilo usado para mostrar os campos seleccionados"

#: includes/fields.php:330 includes/fields/class-acf-field-button-group.php:204
#: includes/fields/class-acf-field-checkbox.php:405
#: includes/fields/class-acf-field-group.php:407
#: includes/fields/class-acf-field-radio.php:276
msgid "Layout"
msgstr "Layout"

#: includes/fields/class-acf-field-group.php:391
msgid "Sub Fields"
msgstr "Subcampos"

#: includes/fields/class-acf-field-group.php:22
msgid "Group"
msgstr "Grupo"

#: includes/fields/class-acf-field-google-map.php:222
msgid "Customize the map height"
msgstr "Personalizar a altura do mapa"

#: includes/fields/class-acf-field-google-map.php:221
#: includes/fields/class-acf-field-image.php:249
#: includes/fields/class-acf-field-image.php:285
#: includes/fields/class-acf-field-oembed.php:253
msgid "Height"
msgstr "Altura"

#: includes/fields/class-acf-field-google-map.php:210
msgid "Set the initial zoom level"
msgstr "Definir o nível de zoom inicial"

#: includes/fields/class-acf-field-google-map.php:209
msgid "Zoom"
msgstr "Zoom"

#: includes/fields/class-acf-field-google-map.php:183
#: includes/fields/class-acf-field-google-map.php:196
msgid "Center the initial map"
msgstr "Centrar o mapa inicial"

#: includes/fields/class-acf-field-google-map.php:182
#: includes/fields/class-acf-field-google-map.php:195
msgid "Center"
msgstr "Centrar"

#: includes/fields/class-acf-field-google-map.php:154
msgid "Search for address..."
msgstr "Pesquisar endereço..."

#: includes/fields/class-acf-field-google-map.php:151
msgid "Find current location"
msgstr "Encontrar a localização actual"

#: includes/fields/class-acf-field-google-map.php:150
msgid "Clear location"
msgstr "Limpar localização"

#: includes/fields/class-acf-field-google-map.php:149
#: includes/fields/class-acf-field-relationship.php:600
msgid "Search"
msgstr "Pesquisa"

#: includes/fields/class-acf-field-google-map.php:57
msgid "Sorry, this browser does not support geolocation"
msgstr "Desculpe, este navegador não suporta geolocalização"

#: includes/fields/class-acf-field-google-map.php:22
msgid "Google Map"
msgstr "Mapa do Google"

#: includes/fields/class-acf-field-date_picker.php:201
#: includes/fields/class-acf-field-date_time_picker.php:189
#: includes/fields/class-acf-field-time_picker.php:122
msgid "The format returned via template functions"
msgstr "O formato devolvido através das <em>template functions</em>"

#: includes/fields/class-acf-field-color_picker.php:168
#: includes/fields/class-acf-field-date_picker.php:200
#: includes/fields/class-acf-field-date_time_picker.php:188
#: includes/fields/class-acf-field-icon_picker.php:260
#: includes/fields/class-acf-field-image.php:177
#: includes/fields/class-acf-field-post_object.php:396
#: includes/fields/class-acf-field-relationship.php:610
#: includes/fields/class-acf-field-select.php:362
#: includes/fields/class-acf-field-time_picker.php:121
#: includes/fields/class-acf-field-user.php:95
msgid "Return Format"
msgstr "Formato devolvido"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_picker.php:210
#: includes/fields/class-acf-field-date_time_picker.php:180
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:113
#: includes/fields/class-acf-field-time_picker.php:129
msgid "Custom:"
msgstr "Personalizado:"

#: includes/fields/class-acf-field-date_picker.php:171
#: includes/fields/class-acf-field-date_time_picker.php:171
#: includes/fields/class-acf-field-time_picker.php:106
msgid "The format displayed when editing a post"
msgstr "O formato de visualização ao editar um conteúdo"

#: includes/fields/class-acf-field-date_picker.php:170
#: includes/fields/class-acf-field-date_time_picker.php:170
#: includes/fields/class-acf-field-time_picker.php:105
msgid "Display Format"
msgstr "Formato de visualização"

#: includes/fields/class-acf-field-time_picker.php:22
msgid "Time Picker"
msgstr "Selecção de hora"

#. translators: counts for inactive field groups
#: acf.php:525
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Inactivo <span class=\"count\">(%s)</span>"
msgstr[1] "Inactivos <span class=\"count\">(%s)</span>"

#: acf.php:486
msgid "No Fields found in Trash"
msgstr "Nenhum campo encontrado no lixo"

#: acf.php:485
msgid "No Fields found"
msgstr "Nenhum campo encontrado"

#: acf.php:484
msgid "Search Fields"
msgstr "Pesquisar campos"

#: acf.php:483
msgid "View Field"
msgstr "Ver campo"

#: acf.php:482 includes/admin/views/acf-field-group/fields.php:113
msgid "New Field"
msgstr "Novo campo"

#: acf.php:481
msgid "Edit Field"
msgstr "Editar campo"

#: acf.php:480
msgid "Add New Field"
msgstr "Adicionar novo campo"

#: acf.php:478
msgid "Field"
msgstr "Campo"

#: acf.php:477 includes/admin/post-types/admin-field-group.php:179
#: includes/admin/post-types/admin-field-groups.php:93
#: includes/admin/views/acf-field-group/fields.php:32
msgid "Fields"
msgstr "Campos"

#: acf.php:452
msgid "No Field Groups found in Trash"
msgstr "Nenhum grupo de campos encontrado no lixo"

#: acf.php:451
msgid "No Field Groups found"
msgstr "Nenhum grupo de campos encontrado"

#: acf.php:450
msgid "Search Field Groups"
msgstr "Pesquisar grupos de campos"

#: acf.php:449
msgid "View Field Group"
msgstr "Ver grupo de campos"

#: acf.php:448
msgid "New Field Group"
msgstr "Novo grupo de campos"

#: acf.php:447
msgid "Edit Field Group"
msgstr "Editar grupo de campos"

#: acf.php:446
msgid "Add New Field Group"
msgstr "Adicionar novo grupo de campos"

#: acf.php:445 acf.php:479
#: includes/admin/views/acf-post-type/advanced-settings.php:224
#: includes/post-types/class-acf-post-type.php:93
#: includes/post-types/class-acf-taxonomy.php:92
msgid "Add New"
msgstr "Adicionar novo"

#: acf.php:444
msgid "Field Group"
msgstr "Grupo de campos"

#: acf.php:443 includes/admin/post-types/admin-field-groups.php:55
#: includes/admin/post-types/admin-post-types.php:113
#: includes/admin/post-types/admin-taxonomies.php:112
msgid "Field Groups"
msgstr "Grupos de campos"

#. Description of the plugin
#: acf.php
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr ""
"Personalize o WordPress com campos intuitivos, poderosos e profissionais."

#. Plugin URI of the plugin
#: acf.php
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php acf.php:289
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/blocks.php:170
msgid "Block type name is required."
msgstr "O nome do tipo de bloco é obrigatório."

#. translators: The name of the block type
#: pro/blocks.php:178
msgid "Block type \"%s\" is already registered."
msgstr "O tipo de bloco \"%s\" já está registado."

#: pro/blocks.php:726
msgid "Switch to Edit"
msgstr "Mudar para o editor"

#: pro/blocks.php:727
msgid "Switch to Preview"
msgstr "Mudar para pré-visualização"

#: pro/blocks.php:728
msgid "Change content alignment"
msgstr "Alterar o alinhamento do conteúdo"

#. translators: %s: Block type title
#: pro/blocks.php:731
msgid "%s settings"
msgstr "Definições de %s"

#: pro/blocks.php:943
msgid "This block contains no editable fields."
msgstr "Este bloco não contém campos editáveis."

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:949
msgid ""
"Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to "
"this block."
msgstr ""
"Atribua um <a href=\"%s\" target=\"_blank\">grupo de campos</a> para "
"adicionar campos a este bloco."

#: pro/options-page.php:78
msgid "Options Updated"
msgstr "Opções actualizadas"

#: pro/updates.php:99
msgid ""
"To enable updates, please enter your license key on the <a "
"href=\"%1$s\">Updates</a> page. If you don't have a licence key, please see "
"<a href=\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""
"Para activar as actualizações, por favor insira a sua chave de licença na "
"página das <a href=\"%1$s\">actualizações</a>. Se não tiver uma chave de "
"licença, por favor consulte os <a href=\"%2$s\" target=\"_blank\">detalhes e "
"preços</a>."

#: pro/updates.php:159
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when deactivating your old licence"
msgstr ""
"<b>Erro de activação do ACF</b>. A chave de licença definida foi alterada, "
"mas ocorreu um erro ao desactivar a licença antiga"

#: pro/updates.php:154
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when connecting to activation server"
msgstr ""
"<b>Erro de activação do ACF</b>. A chave de licença definida foi alterada, "
"mas ocorreu um erro ao ligar ao servidor de activação"

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr "<b>Erro de activação do ACF</b>"

#: pro/updates.php:187
msgid ""
"<b>ACF Activation Error</b>. An error occurred when connecting to activation "
"server"
msgstr ""
"<b>Erro de activação do ACF</b>. Ocorreu um erro ao ligar ao servidor de "
"activação"

#: pro/updates.php:279
msgid "Check Again"
msgstr "Verificar de novo"

#: pro/updates.php:593
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr ""
"<b>Erro de activação do ACF</b>. Não foi possível ligar ao servidor de "
"activação"

#: pro/admin/admin-options-page.php:205
msgid "Publish"
msgstr "Publicado"

#: pro/admin/admin-options-page.php:209
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Nenhum grupo de campos personalizado encontrado na página de opções. <a "
"href=\"%s\">Criar um grupo de campos personalizado</a>"

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Erro</b>. Não foi possível ligar ao servidor de actualização"

#: pro/admin/admin-updates.php:212
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""
"<b>Erro</b>. Não foi possível autenticar o pacote de actualização. Por favor "
"verifique de novo, ou desactive e reactive a sua licença do ACF PRO."

#: pro/admin/admin-updates.php:199
msgid ""
"<b>Error</b>. Your license for this site has expired or been deactivated. "
"Please reactivate your ACF PRO license."
msgstr ""
"<b>Erro</b>. A sua licença para este site expirou ou foi desactivada. Por "
"favor active de novo a sua licença ACF PRO."

#: pro/fields/class-acf-field-clone.php:27,
#: pro/fields/class-acf-field-repeater.php:31
msgid ""
"Allows you to select and display existing fields. It does not duplicate any "
"fields in the database, but loads and displays the selected fields at run-"
"time. The Clone field can either replace itself with the selected fields or "
"display the selected fields as a group of subfields."
msgstr ""
"Permite-lhe seleccionar e mostrar campos existentes. Não duplica nenhum "
"campo na base de dados, mas carrega e mosta os campos seleccionados durante "
"a execução. O campo Clone pode substituir-se a si próprio com os campos "
"seleccionados, ou mostrar os campos seleccionados como um grupo de subcampos."

#: pro/fields/class-acf-field-clone.php:820
msgid "Select one or more fields you wish to clone"
msgstr "Seleccione um ou mais campos que deseje clonar"

#: pro/fields/class-acf-field-clone.php:839
msgid "Display"
msgstr "Visualização"

#: pro/fields/class-acf-field-clone.php:840
msgid "Specify the style used to render the clone field"
msgstr "Especifique o estilo usado para mostrar o campo de clone"

#: pro/fields/class-acf-field-clone.php:845
msgid "Group (displays selected fields in a group within this field)"
msgstr "Grupo (mostra os campos seleccionados num grupo dentro deste campo)"

#: pro/fields/class-acf-field-clone.php:846
msgid "Seamless (replaces this field with selected fields)"
msgstr "Simples (substitui este campo pelos campos seleccionados)"

#: pro/fields/class-acf-field-clone.php:869
msgid "Labels will be displayed as %s"
msgstr "As legendas serão mostradas com %s"

#: pro/fields/class-acf-field-clone.php:874
msgid "Prefix Field Labels"
msgstr "Prefixo nas legendas dos campos"

#: pro/fields/class-acf-field-clone.php:884
msgid "Values will be saved as %s"
msgstr "Os valores serão guardados como %s"

#: pro/fields/class-acf-field-clone.php:889
msgid "Prefix Field Names"
msgstr "Prefixos nos nomes dos campos"

#: pro/fields/class-acf-field-clone.php:1006
msgid "Unknown field"
msgstr "Campo desconhecido"

#: pro/fields/class-acf-field-clone.php:1043
msgid "Unknown field group"
msgstr "Grupo de campos desconhecido"

#: pro/fields/class-acf-field-clone.php:1047
msgid "All fields from %s field group"
msgstr "Todos os campos do grupo de campos %s"

#: pro/fields/class-acf-field-flexible-content.php:27
msgid ""
"Allows you to define, create and manage content with total control by "
"creating layouts that contain subfields that content editors can choose from."
msgstr ""
"Permite-lhe definir, criar e gerir conteúdos com total controlo através da "
"criação de layouts que contêm subcampos que pode ser escolhidos pelos "
"editores de conteúdos."

#: pro/fields/class-acf-field-flexible-content.php:36,
#: pro/fields/class-acf-field-repeater.php:103,
#: pro/fields/class-acf-field-repeater.php:297
msgid "Add Row"
msgstr "Adicionar linha"

#: pro/fields/class-acf-field-flexible-content.php:76,
#: pro/fields/class-acf-field-flexible-content.php:943,
#: pro/fields/class-acf-field-flexible-content.php:1022
msgid "layout"
msgid_plural "layouts"
msgstr[0] "layout"
msgstr[1] "layouts"

#: pro/fields/class-acf-field-flexible-content.php:77
msgid "layouts"
msgstr "layouts"

#: pro/fields/class-acf-field-flexible-content.php:81,
#: pro/fields/class-acf-field-flexible-content.php:942,
#: pro/fields/class-acf-field-flexible-content.php:1021
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Este campo requer pelo menos {min} {identifier} {label}"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "Este campo está limitado a {max} {identifier} {label}"

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {identifier} {label} disponível (máx {max})"

#: pro/fields/class-acf-field-flexible-content.php:86
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {identifier} {label} em falta (mín {min})"

#: pro/fields/class-acf-field-flexible-content.php:89
msgid "Flexible Content requires at least 1 layout"
msgstr "O conteúdo flexível requer pelo menos 1 layout"

#: pro/fields/class-acf-field-flexible-content.php:282
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Clique no botão \"%s\" abaixo para começar a criar o seu layout"

#: pro/fields/class-acf-field-flexible-content.php:423
msgid "Add layout"
msgstr "Adicionar layout"

#: pro/fields/class-acf-field-flexible-content.php:424
msgid "Duplicate layout"
msgstr "Duplicar layout"

#: pro/fields/class-acf-field-flexible-content.php:425
msgid "Remove layout"
msgstr "Remover layout"

#: pro/fields/class-acf-field-flexible-content.php:426,
#: pro/fields/class-acf-repeater-table.php:382
msgid "Click to toggle"
msgstr "Clique para alternar"

#: pro/fields/class-acf-field-flexible-content.php:562
msgid "Delete Layout"
msgstr "Eliminar layout"

#: pro/fields/class-acf-field-flexible-content.php:563
msgid "Duplicate Layout"
msgstr "Duplicar layout"

#: pro/fields/class-acf-field-flexible-content.php:564
msgid "Add New Layout"
msgstr "Adicionar novo layout"

#: pro/fields/class-acf-field-flexible-content.php:564
msgid "Add Layout"
msgstr "Adicionar layout"

#: pro/fields/class-acf-field-flexible-content.php:647
msgid "Min"
msgstr "Mín"

#: pro/fields/class-acf-field-flexible-content.php:662
msgid "Max"
msgstr "Máx"

#: pro/fields/class-acf-field-flexible-content.php:705
msgid "Minimum Layouts"
msgstr "Mínimo de layouts"

#: pro/fields/class-acf-field-flexible-content.php:716
msgid "Maximum Layouts"
msgstr "Máximo de layouts"

#: pro/fields/class-acf-field-flexible-content.php:727,
#: pro/fields/class-acf-field-repeater.php:293
msgid "Button Label"
msgstr "Legenda do botão"

#: pro/fields/class-acf-field-flexible-content.php:1710,
#: pro/fields/class-acf-field-repeater.php:918
msgid "%s must be of type array or null."
msgstr "%s tem de ser do tipo array ou nulo."

#: pro/fields/class-acf-field-flexible-content.php:1721
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] "%1$s tem de conter pelo menos %2$s layout %3$s."
msgstr[1] "%1$s tem de conter pelo menos %2$s layouts %3$s."

#: pro/fields/class-acf-field-flexible-content.php:1737
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] "%1$s tem de conter no máximo %2$s layout %3$s."
msgstr[1] "%1$s tem de conter no máximo %2$s layouts %3$s."

#: pro/fields/class-acf-field-gallery.php:27
msgid ""
"An interactive interface for managing a collection of attachments, such as "
"images."
msgstr ""
"Uma interface interactiva para gerir uma colecção de anexos, como imagens."

#: pro/fields/class-acf-field-gallery.php:78
msgid "Add Image to Gallery"
msgstr "Adicionar imagem à galeria"

#: pro/fields/class-acf-field-gallery.php:79
msgid "Maximum selection reached"
msgstr "Máximo de selecção alcançado"

#: pro/fields/class-acf-field-gallery.php:325
msgid "Length"
msgstr "Comprimento"

#: pro/fields/class-acf-field-gallery.php:369
msgid "Caption"
msgstr "Legenda"

#: pro/fields/class-acf-field-gallery.php:381
msgid "Alt Text"
msgstr "Texto alternativo"

#: pro/fields/class-acf-field-gallery.php:505
msgid "Add to gallery"
msgstr "Adicionar à galeria"

#: pro/fields/class-acf-field-gallery.php:509
msgid "Bulk actions"
msgstr "Acções por lotes"

#: pro/fields/class-acf-field-gallery.php:510
msgid "Sort by date uploaded"
msgstr "Ordenar por data de carregamento"

#: pro/fields/class-acf-field-gallery.php:511
msgid "Sort by date modified"
msgstr "Ordenar por data de modificação"

#: pro/fields/class-acf-field-gallery.php:512
msgid "Sort by title"
msgstr "Ordenar por título"

#: pro/fields/class-acf-field-gallery.php:513
msgid "Reverse current order"
msgstr "Inverter ordem actual"

#: pro/fields/class-acf-field-gallery.php:525
msgid "Close"
msgstr "Fechar"

#: pro/fields/class-acf-field-gallery.php:616
msgid "Minimum Selection"
msgstr "Selecção mínima"

#: pro/fields/class-acf-field-gallery.php:626
msgid "Maximum Selection"
msgstr "Selecção máxima"

#: pro/fields/class-acf-field-gallery.php:728
msgid "Insert"
msgstr "Inserir"

#: pro/fields/class-acf-field-gallery.php:729
msgid "Specify where new attachments are added"
msgstr "Especifique onde serão adicionados os novos anexos"

#: pro/fields/class-acf-field-gallery.php:733
msgid "Append to the end"
msgstr "No fim"

#: pro/fields/class-acf-field-gallery.php:734
msgid "Prepend to the beginning"
msgstr "No início"

#: pro/fields/class-acf-field-repeater.php:66,
#: pro/fields/class-acf-field-repeater.php:463
msgid "Minimum rows not reached ({min} rows)"
msgstr "Mínimo de linhas não alcançado ({min} linhas)"

#: pro/fields/class-acf-field-repeater.php:67
msgid "Maximum rows reached ({max} rows)"
msgstr "Máximo de linhas alcançado ({max} linhas)"

#: pro/fields/class-acf-field-repeater.php:68
msgid "Error loading page"
msgstr "Erro ao carregar a página"

#: pro/fields/class-acf-field-repeater.php:69
msgid "Order will be assigned upon save"
msgstr "A ordem será atribuída ao guardar"

#: pro/fields/class-acf-field-repeater.php:196
msgid "Useful for fields with a large number of rows."
msgstr "Útil para campos com uma grande quantidade de linhas."

#: pro/fields/class-acf-field-repeater.php:207
msgid "Rows Per Page"
msgstr "Linhas por página"

#: pro/fields/class-acf-field-repeater.php:208
msgid "Set the number of rows to be displayed on a page."
msgstr "Define o número de linhas a mostrar numa página."

#: pro/fields/class-acf-field-repeater.php:240
msgid "Minimum Rows"
msgstr "Mínimo de linhas"

#: pro/fields/class-acf-field-repeater.php:251
msgid "Maximum Rows"
msgstr "Máximo de linhas"

#: pro/fields/class-acf-field-repeater.php:281
msgid "Collapsed"
msgstr "Minimizado"

#: pro/fields/class-acf-field-repeater.php:282
msgid "Select a sub field to show when row is collapsed"
msgstr "Seleccione o subcampo a mostrar ao minimizar a linha"

#: pro/fields/class-acf-field-repeater.php:1060
msgid "Invalid field key or name."
msgstr "Chave ou nome de campo inválido."

#: pro/fields/class-acf-field-repeater.php:1069
msgid "There was an error retrieving the field."
msgstr "Ocorreu um erro ao obter o campo."

#: pro/fields/class-acf-repeater-table.php:369
msgid "Click to reorder"
msgstr "Arraste para reordenar"

#: pro/fields/class-acf-repeater-table.php:402
msgid "Add row"
msgstr "Adicionar linha"

#: pro/fields/class-acf-repeater-table.php:403
msgid "Duplicate row"
msgstr "Duplicar linha"

#: pro/fields/class-acf-repeater-table.php:404
msgid "Remove row"
msgstr "Remover linha"

#: pro/fields/class-acf-repeater-table.php:448,
#: pro/fields/class-acf-repeater-table.php:465,
#: pro/fields/class-acf-repeater-table.php:466
msgid "Current Page"
msgstr "Página actual"

#: pro/fields/class-acf-repeater-table.php:456,
#: pro/fields/class-acf-repeater-table.php:457
msgid "First Page"
msgstr "Primeira página"

#: pro/fields/class-acf-repeater-table.php:460,
#: pro/fields/class-acf-repeater-table.php:461
msgid "Previous Page"
msgstr "Página anterior"

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:470
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr "%1$s de %2$s"

#: pro/fields/class-acf-repeater-table.php:477,
#: pro/fields/class-acf-repeater-table.php:478
msgid "Next Page"
msgstr "Página seguinte"

#: pro/fields/class-acf-repeater-table.php:481,
#: pro/fields/class-acf-repeater-table.php:482
msgid "Last Page"
msgstr "Última página"

#: pro/locations/class-acf-location-block.php:71
msgid "No block types exist"
msgstr "Não existem tipos de blocos"

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "Não existem páginas de opções"

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "Desactivar licença"

#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "Activar licença"

#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr "Informações da licença"

#: pro/admin/views/html-settings-updates.php:34
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Para desbloquear as actualizações, por favor insira a sua chave de licença. "
"Se não tiver uma chave de licença, por favor consulte os <a href=\"%s\" "
"target=\"_blank\">detalhes e preços</a>."

#: pro/admin/views/html-settings-updates.php:37
msgid "License Key"
msgstr "Chave de licença"

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr "A sua chave de licença está definida no wp-config.php."

#: pro/admin/views/html-settings-updates.php:29
msgid "Retry Activation"
msgstr "Tentar activar de novo"

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Informações de actualização"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Versão actual"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Última versão"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Actualização disponível"

#: pro/admin/views/html-settings-updates.php:98
msgid "Upgrade Notice"
msgstr "Informações sobre a actualização"

#: pro/admin/views/html-settings-updates.php:126
msgid "Check For Updates"
msgstr "Verificar actualizações"

#: pro/admin/views/html-settings-updates.php:121
msgid "Enter your license key to unlock updates"
msgstr "Digite a sua chave de licença para desbloquear as actualizações"

#: pro/admin/views/html-settings-updates.php:119
msgid "Update Plugin"
msgstr "Actualizar plugin"

#: pro/admin/views/html-settings-updates.php:117
msgid "Please reactivate your license to unlock updates"
msgstr "Por favor reactive a sua licença para desbloquear as actualizações"
