#ddev-generated
# If you remove the ddev-generated line above you
# are responsible for maintaining this file. DDEV will not then
# update it, for example if you add `additional_hostnames`, etc.

http:
  routers:
    vongbicongnghiep.vn-phpmyadmin-80-http:
      entrypoints:
        - http-8036
      rule: HostRegexp(`^vongbicongnghiep\.vn\.ddev\.site$`)
      
      service: "vongbicongnghiep.vn-phpmyadmin-80"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "vongbicongnghiep.vn-redirectHttps"
    vongbicongnghiep.vn-web-80-http:
      entrypoints:
        - http-80
      rule: HostRegexp(`^vongbicongnghiep\.vn\.ddev\.site$`)
      
      service: "vongbicongnghiep.vn-web-80"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "vongbicongnghiep.vn-redirectHttps"
    vongbicongnghiep.vn-web-8025-http:
      entrypoints:
        - http-8025
      rule: HostRegexp(`^vongbicongnghiep\.vn\.ddev\.site$`)
      
      service: "vongbicongnghiep.vn-web-8025"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "vongbicongnghiep.vn-redirectHttps"
    vongbicongnghiep.vn-xhgui-80-http:
      entrypoints:
        - http-8143
      rule: HostRegexp(`^vongbicongnghiep\.vn\.ddev\.site$`)
      
      service: "vongbicongnghiep.vn-xhgui-80"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "vongbicongnghiep.vn-redirectHttps"
    vongbicongnghiep.vn-adminer-8080-http:
      entrypoints:
        - http-9100
      rule: HostRegexp(`^vongbicongnghiep\.vn\.ddev\.site$`)
      
      service: "vongbicongnghiep.vn-adminer-8080"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "vongbicongnghiep.vn-redirectHttps"
    
    
    vongbicongnghiep.vn-phpmyadmin-80-https:
      entrypoints:
        - http-8037
      rule: HostRegexp(`^vongbicongnghiep\.vn\.ddev\.site$`)
      
      service: "vongbicongnghiep.vn-phpmyadmin-80"
      ruleSyntax: v3
      
      tls: true
      
    
    
    vongbicongnghiep.vn-web-80-https:
      entrypoints:
        - http-443
      rule: HostRegexp(`^vongbicongnghiep\.vn\.ddev\.site$`)
      
      service: "vongbicongnghiep.vn-web-80"
      ruleSyntax: v3
      
      tls: true
      
    vongbicongnghiep.vn-web-8025-https:
      entrypoints:
        - http-8026
      rule: HostRegexp(`^vongbicongnghiep\.vn\.ddev\.site$`)
      
      service: "vongbicongnghiep.vn-web-8025"
      ruleSyntax: v3
      
      tls: true
      
    
    vongbicongnghiep.vn-xhgui-80-https:
      entrypoints:
        - http-8142
      rule: HostRegexp(`^vongbicongnghiep\.vn\.ddev\.site$`)
      
      service: "vongbicongnghiep.vn-xhgui-80"
      ruleSyntax: v3
      
      tls: true
      
    
    vongbicongnghiep.vn-adminer-8080-https:
      entrypoints:
        - http-9101
      rule: HostRegexp(`^vongbicongnghiep\.vn\.ddev\.site$`)
      
      service: "vongbicongnghiep.vn-adminer-8080"
      ruleSyntax: v3
      
      tls: true
      
    

  middlewares:
    vongbicongnghiep.vn-redirectHttps:
      redirectScheme:
        scheme: https
        permanent: true

  services:
    vongbicongnghiep.vn-phpmyadmin-80:
      loadbalancer:
        servers:
          - url: http://ddev-vongbicongnghiep.vn-phpmyadmin:80
        
    
    vongbicongnghiep.vn-web-80:
      loadbalancer:
        servers:
          - url: http://ddev-vongbicongnghiep.vn-web:80
        
    vongbicongnghiep.vn-web-8025:
      loadbalancer:
        servers:
          - url: http://ddev-vongbicongnghiep.vn-web:8025
        
    
    
    vongbicongnghiep.vn-xhgui-80:
      loadbalancer:
        servers:
          - url: http://ddev-vongbicongnghiep.vn-xhgui:80
        
    
    vongbicongnghiep.vn-adminer-8080:
      loadbalancer:
        servers:
          - url: http://ddev-vongbicongnghiep.vn-adminer:8080
        
    
    

tls:
  certificates:
    - certFile: /mnt/ddev-global-cache/traefik/certs/vongbicongnghiep.vn.crt
      keyFile: /mnt/ddev-global-cache/traefik/certs/vongbicongnghiep.vn.key