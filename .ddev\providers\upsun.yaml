#ddev-generated
# Upsun provider configuration. This works out of the box, but can be edited to add
# your own preferences. If you edit it, remove the `ddev-generated` line from the top so
# that it won't be overwritten.

# To use this configuration,

# 1. Check out the site from Upsun and then configure it with `ddev config`. You'll want to use `ddev start` and make sure the basic functionality is working.
# 2. Obtain and configure an API token.
#    a. <PERSON>gin to the Upsun Dashboard and go to My Profile->API Tokens to create an API token for DDEV to use.
#    b. Add the API token to the `web_environment` section in your global ddev configuration at ~/.ddev/global_config.yaml:
#    ```yaml
#    web_environment:
#        - UPSUN_CLI_TOKEN=abcdeyourtoken
#    ```
#    You can also do this with `ddev config global --web-environment-add="UPSUN_CLI_TOKEN=abcdeyourtoken"`.
#
#    To use multiple API tokens for different projects, add them to your per-project configuration
#    using the .ddev/config.local.yaml file instead. This file is gitignored by default.
#    ```yaml
#    web_environment:
#        - UPSUN_CLI_TOKEN=abcdeyourtoken
#    ```
#
# 3. Add PLATFORM_PROJECT and PLATFORM_ENVIRONMENT variables to your project `.ddev/config.yaml` or a `.ddev/config.upsun.yaml`
#    ```yaml
#    web_environment:
#        - PLATFORM_PROJECT=nf4amudfn23biyourproject
#        - PLATFORM_ENVIRONMENT=main
#    ```
#    You can also do this with `ddev config --web-environment-add="PLATFORM_PROJECT=nf4amudfn23biyourproject,PLATFORM_ENVIRONMENT=main"`.
#
# 4. `ddev restart`
# 5. Run `ddev pull upsun`. After you agree to the prompt, the current upstream database and files will be downloaded.
# 6. Optionally use `ddev push upsun` to push local files and database to Upsun. Note that `ddev push` is a command that can potentially damage your production site, so this is not recommended.

# Debugging: Use `ddev exec upsun` to see what Upsun knows about
# your configuration and whether it's working correctly.

auth_command:
  command: |
    set -eu -o pipefail
    export PLATFORM_PROJECT="${PLATFORM_PROJECT:-${UPSUN_PROJECT:-}}"
    export PLATFORM_ENVIRONMENT="${PLATFORM_ENVIRONMENT:-${UPSUN_ENVIRONMENT:-}}"
    if [ -z "${UPSUN_CLI_TOKEN:-}" ]; then echo "Please make sure you have set UPSUN_CLI_TOKEN." && exit 1; fi
    if [ -z "${PLATFORM_PROJECT:-}" ]; then echo "Please make sure you have set PLATFORM_PROJECT." && exit 1; fi
    if [ -z "${PLATFORM_ENVIRONMENT:-}" ]; then echo "Please make sure you have set PLATFORM_ENVIRONMENT." && exit 1; fi

db_pull_command:
  command: |
    # set -x   # You can enable bash debugging output by uncommenting
    set -eu -o pipefail
    export PLATFORM_PROJECT="${PLATFORM_PROJECT:-${UPSUN_PROJECT:-}}"
    export PLATFORM_ENVIRONMENT="${PLATFORM_ENVIRONMENT:-${UPSUN_ENVIRONMENT:-}}"
    export UPSUN_CLI_NO_INTERACTION=1
    upsun db:dump --yes --gzip --file=/var/www/html/.ddev/.downloads/db.sql.gz --project="${PLATFORM_PROJECT}" --environment="${PLATFORM_ENVIRONMENT}"

files_import_command:
  command: |
    # set -x   # You can enable bash debugging output by uncommenting
    set -eu -o pipefail
    export PLATFORM_PROJECT="${PLATFORM_PROJECT:-${UPSUN_PROJECT:-}}"
    export PLATFORM_ENVIRONMENT="${PLATFORM_ENVIRONMENT:-${UPSUN_ENVIRONMENT:-}}"
    export UPSUN_CLI_NO_INTERACTION=1
    upsun mount:download --all --yes --quiet --project="${PLATFORM_PROJECT}" --environment="${PLATFORM_ENVIRONMENT}"  --target=/var/www/html


# push is a dangerous command. If not absolutely needed it's better to delete these lines.
db_push_command:
  command: |
    # set -x   # You can enable bash debugging output by uncommenting
    set -eu -o pipefail
    export PLATFORM_PROJECT="${PLATFORM_PROJECT:-${UPSUN_PROJECT:-}}"
    export PLATFORM_ENVIRONMENT="${PLATFORM_ENVIRONMENT:-${UPSUN_ENVIRONMENT:-}}"
    export UPSUN_CLI_NO_INTERACTION=1
    pushd /var/www/html/.ddev/.downloads >/dev/null
    gzip -dc db.sql.gz | upsun db:sql --project="${PLATFORM_PROJECT}" --environment="${PLATFORM_ENVIRONMENT}"

# push is a dangerous command. If not absolutely needed it's better to delete these lines.
# TODO: This is a naive, Drupal-centric push, which needs adjustment for the mount to be pushed.
files_push_command:
  command: |
    # set -x   # You can enable bash debugging output by uncommenting
    set -eu -o pipefail
    export PLATFORM_PROJECT="${PLATFORM_PROJECT:-${UPSUN_PROJECT:-}}"
    export PLATFORM_ENVIRONMENT="${PLATFORM_ENVIRONMENT:-${UPSUN_ENVIRONMENT:-}}"
    export UPSUN_CLI_NO_INTERACTION=1
    upsun mount:upload --yes --quiet --project="${PLATFORM_PROJECT}" --environment="${PLATFORM_ENVIRONMENT}" --source="${DDEV_FILES_DIR}" --mount=web/sites/default/files
