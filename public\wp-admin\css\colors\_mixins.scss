@use 'sass:color';

/*
 * Button mixin- creates a button effect with correct
 * highlights/shadows, based on a base color.
 */
@mixin button( $button-color, $button-text-color: #fff ) {
	background: $button-color;
	border-color: $button-color;
	color: $button-text-color;

	&:hover,
	&:focus {
		background: color.adjust($button-color, $lightness: 3%);
		border-color: color.adjust($button-color, $lightness: -3%);
		color: $button-text-color;
	}

	&:focus {
		box-shadow:
			0 0 0 1px #fff,
			0 0 0 3px $button-color;
	}

	&:active {
		background: color.adjust($button-color, $lightness: -5%);
		border-color: color.adjust($button-color, $lightness: -5%);
		color: $button-text-color;
	}

	&.active,
	&.active:focus,
	&.active:hover {
		background: $button-color;
		color: $button-text-color;
		border-color: color.adjust($button-color, $lightness: -15%);
		box-shadow: inset 0 2px 5px -3px color.adjust($button-color, $lightness: -50%);
	}
}
