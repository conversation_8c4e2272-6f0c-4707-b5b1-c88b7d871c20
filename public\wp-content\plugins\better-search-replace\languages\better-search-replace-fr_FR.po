# Copyright (C) 2015 Better Search Replace
# This file is distributed under the same license as the Better Search Replace package.
msgid ""
msgstr ""
"Project-Id-Version: Better Search Replace 1.0.2\n"
"Report-Msgid-Bugs-To: http://wordpress.org/support/plugin/better-search-replace\n"
"POT-Creation-Date: 2015-02-05 04:52:47+00:00\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"PO-Revision-Date: 2015-05-12 10:08-0500\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"Last-Translator: TWF <<EMAIL>>\n"
"Language-Team: TWF <<EMAIL>>\n"
"Language: fr\n"
"X-Generator: Poedit 1.7.6\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e\n"
"X-Poedit-Basepath: .\n"
"X-Poedit-SearchPath-0: ..\n"

#. Plugin Name of the plugin/theme
#: includes/class-better-search-replace-admin.php:69 templates/bsr-dashboard.php:17
msgid "Better Search Replace"
msgstr "Better Search Replace"

#: includes/class-better-search-replace-admin.php:138
msgid "No search string was defined, please enter a URL or string to search for."
msgstr ""
"Aucune chaîne de recherche a été définie, entrez une URL ou une chaîne à rechercher."

#: includes/class-better-search-replace-admin.php:141
msgid "Please select the tables that you want to update."
msgstr "Sélectionnez les tables que vous souhaitez mettre à jour."

#: includes/class-better-search-replace-admin.php:150
msgid ""
"<p><strong>DRY RUN:</strong> <strong>%d</strong> tables were searched, <strong>%d</"
"strong> cells were found that need to be updated, and <strong>%d</strong> changes "
"were made.</p><p><a href=\"%s\" class=\"thickbox\" title=\"Dry Run Details\">Click "
"here</a> for more details, or use the form below to run the search/replace.</p>"
msgstr ""
"<p><strong>Résultat de l'essai :</strong> <strong>%d</strong> tables ont été "
"parcourues, <strong>%d</strong> chaînes trouvées qui peuvent être mises à jour et "
"<strong>%d</strong> modifications auraient pu être apportées.</p><p><a href=\"%s\" "
"class=\"thickbox\" title=\"Plus de détails\">Cliquez ici</a> pour plus de détails, "
"ou utilisez le formulaire ci-dessous pour exécuter la recherche/remplace.</p>"

#: includes/class-better-search-replace-admin.php:157
msgid ""
"<p>During the search/replace, <strong>%d</strong> tables were searched, with <strong>"
"%d</strong> cells changed in <strong>%d</strong> updates.</p><p><a href=\"%s\" class="
"\"thickbox\" title=\"Search/Replace Details\">Click here</a> for more details.</p>"
msgstr ""
"<p>Pendant la recherche/remplace, <strong>%d</strong> tables ont été fouillés, avec "
"<strong>%d</strong> cellules changées dans <strong>%d</strong> mises à jour.</"
"p><p><a href=\"%s\" class=\"thickbox\" title=\"Plus de Details\">Cliquez ici</a> "
"pour plus de détails.</p>"

#: templates/bsr-dashboard.php:19
msgid ""
"This tool allows you to search and replace text in your database (supports "
"serialized arrays and objects)."
msgstr ""
"Cet outil vous permet de rechercher et remplacer du texte dans votre base de données "
"(prend en charge la sérialisation des tables et objets)."

#: templates/bsr-dashboard.php:20
msgid ""
"To get started, use the form below to enter the text to be replaced and select the "
"tables to update."
msgstr ""
"Pour commencer, utiliser le formulaire ci-dessous pour entrer dans le texte pour le "
"remplacement et sélectionnez les tables à mettre à jour."

#: templates/bsr-dashboard.php:21
msgid ""
"<strong>WARNING:</strong> Make sure you backup your database before using this "
"plugin!"
msgstr ""
"<strong>Avertissement :</strong> Assurez-vous que vous avez sauvegardé votre base de "
"données avant d'utiliser ce plugin !"

#: templates/bsr-dashboard.php:28
msgid "Search for"
msgstr "Rechercher"

#: templates/bsr-dashboard.php:33
msgid "Replace with"
msgstr "Remplacer avec"

#: templates/bsr-dashboard.php:38
msgid "Select tables"
msgstr "Dans les tables"

#: templates/bsr-dashboard.php:41
msgid "Select multiple tables with Ctrl-Click for Windows or Cmd-Click for Mac."
msgstr "Sélectionnez plusieurs tables avec la touche CTRL+clic ou CMD+clic pour Mac"

#: templates/bsr-dashboard.php:46
msgid ""
"Replace GUIDs<a href=\"http://codex.wordpress.org/"
"Changing_The_Site_URL#Important_GUID_Note\" target=\"_blank\">?</a>"
msgstr ""
"Remplacez les GUID ?<a href=\"http://codex.wordpress.org/"
"Changing_The_Site_URL#Important_GUID_Note\" target=\"_blank\"> En savoir plus sur "
"les GUID</a>"

#: templates/bsr-dashboard.php:49
msgid "If left unchecked, all database columns titled 'guid' will be skipped."
msgstr "Si décoché, toutes les colonnes 'guid' de la base de données seront igniorés."

#: templates/bsr-dashboard.php:54
msgid "Run as dry run?"
msgstr "Juste faire un test ?"

#: templates/bsr-dashboard.php:57
msgid ""
"If checked, no changes will be made to the database, allowing you to check the "
"results beforehand."
msgstr ""
"Si coché, aucun changement ne sera apporté à la base de données, vous aurez un bilan "
"des résultats possibles"

#: templates/bsr-dashboard.php:66
msgid "Run Search/Replace"
msgstr "Rechercher/Remplacer"

#. Plugin URI of the plugin/theme
msgid "http://expandedfronts.com/better-search-replace"
msgstr "http://expandedfronts.com/better-search-replace"

#. Description of the plugin/theme
msgid "A small plugin for running a search/replace on your WordPress database."
msgstr ""
"Un petit plugin pour rechercher et remplacer des cabine dans votre base de données "
"WordPress."

#. Author of the plugin/theme
msgid "Expanded Fronts"
msgstr "Expanded Fronts"

#. Author URI of the plugin/theme
msgid "http://expandedfronts.com"
msgstr "http://expandedfronts.com"
