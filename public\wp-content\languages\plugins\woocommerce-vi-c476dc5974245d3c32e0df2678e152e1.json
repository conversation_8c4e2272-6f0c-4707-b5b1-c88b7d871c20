{"translation-revision-date": "2024-11-20 18:08:48+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "vi_VN"}, "productGalleryClientId and clientId codes mismatch.": ["mã productGalleryClientId và clientId không khớp."], "Open pop-up when clicked": ["Mở c<PERSON>a sổ bật lên khi nhấp vào"], "Gallery Navigation": ["<PERSON><PERSON><PERSON><PERSON> viện"], "Pager": ["<PERSON><PERSON> trang"], "No pager will be displayed.": ["Sẽ không hiển thị phân trang."], "A series of dots will show to indicate the number of items.": ["<PERSON><PERSON><PERSON> lo<PERSON>t các dấu chấm sẽ hiển thị để biểu thị số lượng mục."], "A list of numbers will show to indicate the number of items.": ["<PERSON><PERSON><PERSON> danh sách các số sẽ hiển thị để biểu thị số lượng mục."], "Next/Prev Buttons": ["<PERSON><PERSON><PERSON> theo/<PERSON><PERSON><PERSON><PERSON><PERSON> đó"], "No next or previous button will be displayed.": ["Sẽ không hiển thị nút tiếp theo hoặc trước đó."], "Next and previous buttons will appear on outside the large image.": ["<PERSON><PERSON><PERSON> tiếp theo và trước đó sẽ xuất hiện bên ngoài hình ảnh lớn."], "Next and previous buttons will appear inside the large image.": ["<PERSON><PERSON><PERSON> nút tiếp theo và trước đó sẽ xuất hiện bên trong hình ảnh lớn."], "Clicking on the large image will open a full-screen gallery experience.": ["<PERSON><PERSON><PERSON><PERSON> vào hình ảnh lớn sẽ mở ra trải nghiệm thư viện toàn màn hình."], "While hovering the large image will zoom in by 30%.": ["<PERSON>rong khi di chuột qua hình ảnh lớn sẽ phóng to 30%."], "Zoom while hovering": ["<PERSON><PERSON>g to khi di chuột"], "Images will be cropped to fit within a square space.": ["Hình ảnh sẽ được cắt xén để vừa với không gian hình vuông."], "Crop images to fit": ["<PERSON><PERSON><PERSON> hình ảnh để vừa"], "Media Settings": ["<PERSON>ài đặt phương tiện"], "Choose how many thumbnails (3-8) will display. If more images exist, a “View all” button will display.": ["<PERSON><PERSON><PERSON> số lượng hình thu nhỏ (3-8) sẽ hiển thị. <PERSON><PERSON><PERSON> có nhiều hình ảnh hơn, n<PERSON><PERSON> \"<PERSON>em tất cả\" sẽ hiển thị."], "Number of Thumbnails": ["<PERSON><PERSON> lư<PERSON>ng hình thu nhỏ"], "Off": ["Tắt"], "Thumbnails": ["<PERSON><PERSON>nh thu nhỏ"], "A strip of small images will appear to the right of the main gallery image.": ["M<PERSON>t dải hình ảnh nhỏ sẽ xuất hiện ở bên phải của hình ảnh thư viện ch<PERSON>h."], "A strip of small images will appear below the main gallery image.": ["<PERSON><PERSON>t dải hình ảnh nhỏ sẽ xuất hiện bên dưới hình ảnh thư viện ch<PERSON>h."], "A strip of small images will appear to the left of the main gallery image.": ["M<PERSON>t dải hình ảnh nhỏ sẽ xuất hiện ở bên trái của hình ảnh thư viện ch<PERSON>h."], "No thumbnails will be displayed.": ["<PERSON><PERSON><PERSON><PERSON> có hình thu nhỏ nào sẽ được hiển thị."], "An error has prevented the block from being updated.": ["<PERSON><PERSON> xảy ra lỗi ngăn khối đ<PERSON><PERSON><PERSON> cập nh<PERSON>t."], "The following error was returned": ["Lỗi sau đã được trả về"], "Sorry, an error occurred": ["<PERSON>n lỗi, phần này bị lỗi"], "The following error was returned from the API": ["Lỗi sau là do từ đoạn đáp của API"], "Retry": ["<PERSON><PERSON><PERSON> lại"]}}, "comment": {"reference": "assets/client/blocks/product-gallery.js"}}