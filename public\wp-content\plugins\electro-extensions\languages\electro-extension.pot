# Copyright (C) 2024 MadrasThemes
# This file is distributed under the same license as the Electro Extensions package.
msgid ""
msgstr ""
"Project-Id-Version: Electro Extensions 3.5.5\n"
"Report-Msgid-Bugs-To: "
"https://themeforest.net/item/electro-electronics-store-woocommerce-theme/157"
"20624/support/\n"
"POT-Creation-Date: 2024-11-20 04:44:15+00:00\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"PO-Revision-Date: 2024-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: Electro POT <<EMAIL>>\n"
"X-Generator: grunt-wp-i18n 1.0.3\n"

#: electro-extensions.php:190 electro-extensions.php:199
msgid "Cheatin&#8217; huh?"
msgstr ""

#: modules/elementor/elementor.php:89 modules/js_composer/config/map.php:74
#: modules/js_composer/config/map.php:109
#: modules/js_composer/config/map.php:168
#: modules/js_composer/config/map.php:411
#: modules/js_composer/config/map.php:482
#: modules/js_composer/config/map.php:547
#: modules/js_composer/config/map.php:716
#: modules/js_composer/config/map.php:1050
#: modules/js_composer/config/map.php:1207
#: modules/js_composer/config/map.php:1432
#: modules/js_composer/config/map.php:1666
#: modules/js_composer/config/map.php:1885
#: modules/js_composer/config/map.php:2098
#: modules/js_composer/config/map.php:2205
#: modules/js_composer/config/map.php:2275
#: modules/js_composer/config/map.php:2363
#: modules/js_composer/config/map.php:2448
#: modules/js_composer/config/map.php:2585
#: modules/js_composer/config/map.php:2820
#: modules/js_composer/config/map.php:2897
#: modules/js_composer/config/map.php:3037
#: modules/js_composer/config/map.php:3161
#: modules/js_composer/config/map.php:3279
#: modules/js_composer/config/map.php:3438
#: modules/js_composer/config/map.php:3597
#: modules/js_composer/config/map.php:3854
#: modules/js_composer/config/map.php:4110
#: modules/js_composer/config/map.php:4172
#: modules/js_composer/config/map.php:4331
#: modules/js_composer/config/map.php:4567
#: modules/js_composer/config/map.php:4773
#: modules/js_composer/config/map.php:4957
#: modules/js_composer/config/map.php:5169
#: modules/js_composer/config/map.php:5356
#: modules/js_composer/config/map.php:5450
#: modules/js_composer/config/map.php:5477
#: modules/js_composer/config/map.php:5521
#: modules/js_composer/config/map.php:5782
#: modules/js_composer/config/map.php:6000
#: modules/js_composer/config/map.php:6066
#: modules/js_composer/config/map.php:6226
#: modules/js_composer/config/map.php:6266
#: modules/js_composer/config/map.php:6324
#: modules/js_composer/config/map.php:6437
#: modules/js_composer/config/map.php:6578
#: modules/js_composer/config/map.php:6655
#: modules/js_composer/config/map.php:6720
#: modules/js_composer/config/map.php:6797
#: modules/js_composer/config/map.php:6812
#: modules/js_composer/config/map.php:7014
#: modules/js_composer/config/map.php:7078
#: modules/js_composer/config/map.php:7080
#: modules/js_composer/config/map.php:7253
#: modules/js_composer/config/map.php:7402
#: modules/js_composer/config/map.php:7592
#: modules/js_composer/config/map.php:7668
#: modules/js_composer/config/map.php:7748
msgid "Electro Elements"
msgstr ""

#: modules/elementor/elementor.php:168
msgid "Container CSS Classes"
msgstr ""

#: modules/elementor/elementor.php:171
msgid ""
"Applied to elementor-container element. You can use additional bootstrap "
"utility classes here."
msgstr ""

#: modules/elementor/elementor.php:196
msgid "CSS Classes"
msgstr ""

#: modules/elementor/elementor.php:199
msgid "Applied to elementor-widget-wrap element."
msgstr ""

#: modules/elementor/widgets/ad-block.php:43
#: modules/js_composer/config/map.php:71
msgid "Ad Block"
msgstr ""

#: modules/elementor/widgets/ad-block.php:87
#: modules/elementor/widgets/ads-with-banner.php:87
#: modules/elementor/widgets/banner-1-6-block.php:87
#: modules/elementor/widgets/banner-with-products-carousel.php:87
#: modules/elementor/widgets/blog-posts-block.php:87
#: modules/elementor/widgets/brands-block.php:89
#: modules/elementor/widgets/brands-carousel.php:87
#: modules/elementor/widgets/category-icons-carousel.php:87
#: modules/elementor/widgets/deal-and-product-tabs.php:87
#: modules/elementor/widgets/deal-products-block.php:87
#: modules/elementor/widgets/deals-products-carousel.php:86
#: modules/elementor/widgets/feature-block.php:87
#: modules/elementor/widgets/jumbotron.php:87
#: modules/elementor/widgets/mobile-product-deals-block.php:87
#: modules/elementor/widgets/nav-menu.php:97
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:87
#: modules/elementor/widgets/product-categories-block-v2.php:87
#: modules/elementor/widgets/product-categories-block.php:87
#: modules/elementor/widgets/product-categories-list-with-header.php:87
#: modules/elementor/widgets/product-categories-list.php:87
#: modules/elementor/widgets/product-categories-menu-list.php:87
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:87
#: modules/elementor/widgets/product-category-tags.php:87
#: modules/elementor/widgets/product-list-categories.php:87
#: modules/elementor/widgets/product-onsale-carousel-2.php:87
#: modules/elementor/widgets/product-onsale-carousel.php:87
#: modules/elementor/widgets/product-onsale.php:87
#: modules/elementor/widgets/products-2-1-2-block.php:87
#: modules/elementor/widgets/products-6-1-block.php:87
#: modules/elementor/widgets/products-6-1-with-categories.php:87
#: modules/elementor/widgets/products-cards-carousel.php:87
#: modules/elementor/widgets/products-carousel-1.php:87
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:87
#: modules/elementor/widgets/products-carousel-category-with-image.php:87
#: modules/elementor/widgets/products-carousel-tabs-1.php:87
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:87
#: modules/elementor/widgets/products-carousel-tabs.php:87
#: modules/elementor/widgets/products-carousel-with-timer.php:87
#: modules/elementor/widgets/products-carousel.php:87
#: modules/elementor/widgets/products-categories-1-6.php:73
#: modules/elementor/widgets/products-category-with-image.php:87
#: modules/elementor/widgets/products-list-block.php:87
#: modules/elementor/widgets/products-one-two-block.php:87
#: modules/elementor/widgets/products-tabs-element.php:87
#: modules/elementor/widgets/products-with-category-image.php:87
#: modules/elementor/widgets/recent-viewed-products.php:87
#: modules/elementor/widgets/recently-viewed-products-carousel.php:87
#: modules/elementor/widgets/sidebar-with-products.php:88
#: modules/elementor/widgets/slider-with-ads-block-v2.php:106
#: modules/elementor/widgets/slider-with-ads-block.php:102
#: modules/elementor/widgets/team-member.php:87
#: modules/elementor/widgets/two-row-products.php:87
#: modules/elementor/widgets/vertical-nav-menu.php:97
msgid "Content"
msgstr ""

#: modules/elementor/widgets/ad-block.php:97
#: modules/elementor/widgets/slider-with-ads-block-v2.php:142
#: modules/elementor/widgets/slider-with-ads-block.php:130
msgid "Ad text"
msgstr ""

#: modules/elementor/widgets/ad-block.php:99
#: modules/elementor/widgets/ad-block.php:108
#: modules/elementor/widgets/ad-block.php:125
#: modules/elementor/widgets/ads-with-banner.php:99
#: modules/elementor/widgets/ads-with-banner.php:108
#: modules/elementor/widgets/ads-with-banner.php:117
#: modules/elementor/widgets/ads-with-banner.php:134
#: modules/elementor/widgets/ads-with-banner.php:143
#: modules/elementor/widgets/ads-with-banner.php:152
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:288
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:105
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:114
#: modules/elementor/widgets/slider-with-ads-block-v2.php:162
#: modules/elementor/widgets/slider-with-ads-block.php:132
#: modules/elementor/widgets/slider-with-ads-block.php:141
#: modules/elementor/widgets/slider-with-ads-block.php:150
msgid "Enter your link text here"
msgstr ""

#: modules/elementor/widgets/ad-block.php:106
#: modules/elementor/widgets/brands-with-category-block.php:107
#: modules/elementor/widgets/brands-with-category-block.php:249
#: modules/elementor/widgets/deal-products-block.php:202
#: modules/elementor/widgets/product-categories-menu-list.php:187
#: modules/elementor/widgets/products-carousel-1.php:105
#: modules/elementor/widgets/products-carousel-tabs-1.php:103
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:103
#: modules/elementor/widgets/products-carousel-with-timer.php:139
#: modules/elementor/widgets/products-list-block.php:116
#: modules/elementor/widgets/products-one-two-block.php:182
#: modules/elementor/widgets/slider-with-ads-block-v2.php:151
#: modules/elementor/widgets/slider-with-ads-block.php:139
#: modules/elementor/widgets/two-row-products.php:105
#: modules/elementor/widgets/vertical-nav-menu.php:115
#: modules/js_composer/config/map.php:89 modules/js_composer/config/map.php:136
#: modules/js_composer/config/map.php:1677
#: modules/js_composer/config/map.php:1919
#: modules/js_composer/config/map.php:2598
#: modules/js_composer/config/map.php:3012
#: modules/js_composer/config/map.php:3136
#: modules/js_composer/config/map.php:4588
#: modules/js_composer/config/map.php:4786
#: modules/js_composer/config/map.php:5420
#: modules/js_composer/config/map.php:6077
#: modules/js_composer/config/map.php:6237
#: modules/js_composer/config/map.php:7047
msgid "Action Text"
msgstr ""

#: modules/elementor/widgets/ad-block.php:115
#: modules/elementor/widgets/slider-with-ads-block-v2.php:134
#: modules/elementor/widgets/slider-with-ads-block.php:122
msgid "Ad image"
msgstr ""

#: modules/elementor/widgets/ad-block.php:123
#: modules/elementor/widgets/banner-1-6-block.php:106
#: modules/elementor/widgets/brands-with-category-block.php:117
#: modules/elementor/widgets/deal-products-block.php:210
#: modules/elementor/widgets/product-categories-menu-list.php:195
#: modules/elementor/widgets/products-carousel-1.php:113
#: modules/elementor/widgets/products-carousel-tabs-1.php:111
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:112
#: modules/elementor/widgets/products-carousel-with-timer.php:147
#: modules/elementor/widgets/products-list-block.php:124
#: modules/elementor/widgets/products-one-two-block.php:190
#: modules/elementor/widgets/slider-with-ads-block-v2.php:160
#: modules/elementor/widgets/slider-with-ads-block.php:148
#: modules/elementor/widgets/two-row-products.php:113
#: modules/elementor/widgets/vertical-nav-menu.php:125
#: modules/js_composer/config/map.php:48 modules/js_composer/config/map.php:94
#: modules/js_composer/config/map.php:141
#: modules/js_composer/config/map.php:437
#: modules/js_composer/config/map.php:1684
#: modules/js_composer/config/map.php:1926
#: modules/js_composer/config/map.php:2605
#: modules/js_composer/config/map.php:3018
#: modules/js_composer/config/map.php:3142
#: modules/js_composer/config/map.php:4595
#: modules/js_composer/config/map.php:4793
#: modules/js_composer/config/map.php:5425
#: modules/js_composer/config/map.php:6083
#: modules/js_composer/config/map.php:6242
#: modules/js_composer/config/map.php:7053
msgid "Action Link"
msgstr ""

#: modules/elementor/widgets/ad-block.php:132
msgid "Ad block"
msgstr ""

#: modules/elementor/widgets/ad-block.php:142
#: modules/elementor/widgets/ads-with-banner.php:181
#: modules/elementor/widgets/product-categories-block-v2.php:166
#: modules/elementor/widgets/product-categories-block.php:185
#: modules/elementor/widgets/sidebar-with-products.php:313
#: modules/elementor/widgets/slider-with-ads-block-v2.php:179
#: modules/elementor/widgets/team-member.php:142
msgid "Extra class name"
msgstr ""

#: modules/elementor/widgets/ad-block.php:144
#: modules/elementor/widgets/ads-with-banner.php:183
#: modules/elementor/widgets/product-categories-block-v2.php:168
#: modules/elementor/widgets/product-categories-block.php:187
#: modules/elementor/widgets/team-member.php:144
msgid ""
"If you wish to style particular content element differently, please add a "
"class name to this field and refer to it in your custom CSS file."
msgstr ""

#: modules/elementor/widgets/ads-with-banner.php:43
#: modules/elementor/widgets/ads-with-banner.php:171
msgid "Ads with banners block"
msgstr ""

#: modules/elementor/widgets/ads-with-banner.php:97
#: modules/elementor/widgets/banner-with-products-carousel.php:103
#: modules/elementor/widgets/blog-posts-block.php:95
#: modules/elementor/widgets/brands-block.php:97
#: modules/elementor/widgets/brands-carousel.php:95
#: modules/elementor/widgets/brands-with-category-block.php:97
#: modules/elementor/widgets/deal-and-product-tabs.php:95
#: modules/elementor/widgets/deal-and-product-tabs.php:166
#: modules/elementor/widgets/jumbotron.php:95
#: modules/elementor/widgets/nav-menu.php:105
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:95
#: modules/elementor/widgets/product-categories-menu-list.php:107
#: modules/elementor/widgets/product-category-tags.php:95
#: modules/elementor/widgets/product-onsale-carousel.php:95
#: modules/elementor/widgets/product-onsale.php:95
#: modules/elementor/widgets/products-carousel-1.php:95
#: modules/elementor/widgets/products-carousel-category-with-image.php:95
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:190
#: modules/elementor/widgets/products-carousel-with-timer.php:95
#: modules/elementor/widgets/products-carousel.php:95
#: modules/elementor/widgets/products-category-with-image.php:95
#: modules/elementor/widgets/products-with-category-image.php:95
#: modules/elementor/widgets/sidebar-with-products.php:96
#: modules/elementor/widgets/slider-with-ads-block-v2.php:122
#: modules/elementor/widgets/slider-with-ads-block.php:110
#: modules/elementor/widgets/two-row-products.php:95
#: modules/elementor/widgets/vertical-nav-menu.php:105
#: modules/js_composer/config/map.php:422
#: modules/js_composer/config/map.php:552
#: modules/js_composer/config/map.php:728
#: modules/js_composer/config/map.php:1074
#: modules/js_composer/config/map.php:2669
#: modules/js_composer/config/map.php:4806
#: modules/js_composer/config/map.php:5224
#: modules/js_composer/config/map.php:5372
#: modules/js_composer/config/map.php:5454
#: modules/js_composer/config/map.php:6230
#: modules/js_composer/config/map.php:7413
msgid "Title"
msgstr ""

#: modules/elementor/widgets/ads-with-banner.php:106
#: modules/js_composer/config/map.php:427
msgid "Description"
msgstr ""

#: modules/elementor/widgets/ads-with-banner.php:115
#: modules/js_composer/config/map.php:432
msgid "Price"
msgstr ""

#: modules/elementor/widgets/ads-with-banner.php:124
#: modules/elementor/widgets/banner-1-6-block.php:97
#: modules/elementor/widgets/banner-with-products-carousel.php:95
#: modules/elementor/widgets/jumbotron.php:111
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:219
#: modules/elementor/widgets/products-carousel-category-with-image.php:289
#: modules/elementor/widgets/products-category-with-image.php:297
#: modules/elementor/widgets/products-with-category-image.php:360
#: modules/js_composer/config/map.php:42 modules/js_composer/config/map.php:79
#: modules/js_composer/config/map.php:126
#: modules/js_composer/config/map.php:442
#: modules/js_composer/config/map.php:562
#: modules/js_composer/config/map.php:4085
#: modules/js_composer/config/map.php:5596
#: modules/js_composer/config/map.php:5857
#: modules/js_composer/config/map.php:7036
msgid "Image"
msgstr ""

#: modules/elementor/widgets/ads-with-banner.php:132
msgid "Action link"
msgstr ""

#: modules/elementor/widgets/ads-with-banner.php:141
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:286
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:236
#: modules/js_composer/config/map.php:207
#: modules/js_composer/config/map.php:447
#: modules/js_composer/config/map.php:6963
msgid "Banner Action Link"
msgstr ""

#: modules/elementor/widgets/ads-with-banner.php:150
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:278
#: modules/elementor/widgets/three-banners.php:103
#: modules/js_composer/config/map.php:214
#: modules/js_composer/config/map.php:452
#: modules/js_composer/config/map.php:6957
msgid "Banner Image"
msgstr ""

#: modules/elementor/widgets/ads-with-banner.php:159
#: modules/js_composer/config/map.php:458
msgid "Banner Alignment"
msgstr ""

#: modules/elementor/widgets/ads-with-banner.php:161
#: modules/elementor/widgets/banner-with-products-carousel.php:297
#: modules/elementor/widgets/brands-carousel.php:169
#: modules/elementor/widgets/brands-carousel.php:181
#: modules/elementor/widgets/category-icons-carousel.php:107
#: modules/elementor/widgets/category-icons-carousel.php:205
#: modules/elementor/widgets/category-icons-carousel.php:217
#: modules/elementor/widgets/category-icons-carousel.php:229
#: modules/elementor/widgets/category-icons-carousel.php:241
#: modules/elementor/widgets/deal-and-product-tabs.php:107
#: modules/elementor/widgets/deals-products-carousel.php:107
#: modules/elementor/widgets/deals-products-carousel.php:281
#: modules/elementor/widgets/deals-products-carousel.php:293
#: modules/elementor/widgets/deals-products-carousel.php:314
#: modules/elementor/widgets/mobile-product-deals-block.php:106
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:192
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:221
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:331
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:344
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:356
#: modules/elementor/widgets/product-categories-block.php:124
#: modules/elementor/widgets/product-categories-block.php:173
#: modules/elementor/widgets/product-categories-list-with-header.php:122
#: modules/elementor/widgets/product-categories-list-with-header.php:152
#: modules/elementor/widgets/product-categories-list.php:116
#: modules/elementor/widgets/product-categories-menu-list.php:128
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:108
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:141
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:193
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:216
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:268
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:307
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:319
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:331
#: modules/elementor/widgets/product-list-categories.php:115
#: modules/elementor/widgets/product-onsale-carousel-2.php:131
#: modules/elementor/widgets/product-onsale-carousel-2.php:143
#: modules/elementor/widgets/product-onsale-carousel-2.php:155
#: modules/elementor/widgets/product-onsale-carousel-2.php:167
#: modules/elementor/widgets/product-onsale-carousel.php:107
#: modules/elementor/widgets/product-onsale-carousel.php:175
#: modules/elementor/widgets/product-onsale-carousel.php:187
#: modules/elementor/widgets/product-onsale-carousel.php:199
#: modules/elementor/widgets/product-onsale-carousel.php:211
#: modules/elementor/widgets/product-onsale-carousel.php:223
#: modules/elementor/widgets/product-onsale-carousel.php:235
#: modules/elementor/widgets/product-onsale-carousel.php:273
#: modules/elementor/widgets/product-onsale.php:107
#: modules/elementor/widgets/products-2-1-2-block.php:196
#: modules/elementor/widgets/products-6-1-block.php:195
#: modules/elementor/widgets/products-6-1-with-categories.php:235
#: modules/elementor/widgets/products-6-1-with-categories.php:256
#: modules/elementor/widgets/products-6-1-with-categories.php:316
#: modules/elementor/widgets/products-cards-carousel.php:137
#: modules/elementor/widgets/products-cards-carousel.php:149
#: modules/elementor/widgets/products-cards-carousel.php:253
#: modules/elementor/widgets/products-cards-carousel.php:273
#: modules/elementor/widgets/products-carousel-1.php:271
#: modules/elementor/widgets/products-carousel-1.php:284
#: modules/elementor/widgets/products-carousel-1.php:297
#: modules/elementor/widgets/products-carousel-1.php:309
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:315
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:327
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:339
#: modules/elementor/widgets/products-carousel-category-with-image.php:134
#: modules/elementor/widgets/products-carousel-category-with-image.php:217
#: modules/elementor/widgets/products-carousel-category-with-image.php:239
#: modules/elementor/widgets/products-carousel-category-with-image.php:355
#: modules/elementor/widgets/products-carousel-category-with-image.php:368
#: modules/elementor/widgets/products-carousel-category-with-image.php:380
#: modules/elementor/widgets/products-carousel-tabs-1.php:279
#: modules/elementor/widgets/products-carousel-tabs-1.php:307
#: modules/elementor/widgets/products-carousel-tabs-1.php:319
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:131
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:367
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:379
#: modules/elementor/widgets/products-carousel-tabs.php:278
#: modules/elementor/widgets/products-carousel-tabs.php:290
#: modules/elementor/widgets/products-carousel-with-timer.php:107
#: modules/elementor/widgets/products-carousel-with-timer.php:301
#: modules/elementor/widgets/products-carousel-with-timer.php:314
#: modules/elementor/widgets/products-carousel-with-timer.php:326
#: modules/elementor/widgets/products-carousel-with-timer.php:338
#: modules/elementor/widgets/products-carousel.php:181
#: modules/elementor/widgets/products-carousel.php:269
#: modules/elementor/widgets/products-carousel.php:281
#: modules/elementor/widgets/products-carousel.php:294
#: modules/elementor/widgets/products-category-with-image.php:225
#: modules/elementor/widgets/products-category-with-image.php:247
#: modules/elementor/widgets/products-list-block.php:235
#: modules/elementor/widgets/products-list-block.php:264
#: modules/elementor/widgets/products-with-category-image.php:225
#: modules/elementor/widgets/products-with-category-image.php:247
#: modules/elementor/widgets/products-with-category-image.php:310
#: modules/elementor/widgets/recently-viewed-products-carousel.php:172
#: modules/elementor/widgets/recently-viewed-products-carousel.php:184
#: modules/elementor/widgets/recently-viewed-products-carousel.php:196
#: modules/elementor/widgets/sidebar-with-products.php:108
#: modules/elementor/widgets/sidebar-with-products.php:303
msgid "Enable"
msgstr ""

#: modules/elementor/widgets/ads-with-banner.php:162
#: modules/elementor/widgets/banner-with-products-carousel.php:298
#: modules/elementor/widgets/brands-carousel.php:170
#: modules/elementor/widgets/brands-carousel.php:182
#: modules/elementor/widgets/category-icons-carousel.php:108
#: modules/elementor/widgets/category-icons-carousel.php:206
#: modules/elementor/widgets/category-icons-carousel.php:218
#: modules/elementor/widgets/category-icons-carousel.php:230
#: modules/elementor/widgets/category-icons-carousel.php:242
#: modules/elementor/widgets/deal-and-product-tabs.php:108
#: modules/elementor/widgets/deals-products-carousel.php:108
#: modules/elementor/widgets/deals-products-carousel.php:282
#: modules/elementor/widgets/deals-products-carousel.php:294
#: modules/elementor/widgets/deals-products-carousel.php:315
#: modules/elementor/widgets/mobile-product-deals-block.php:107
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:193
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:222
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:332
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:345
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:357
#: modules/elementor/widgets/product-categories-block.php:125
#: modules/elementor/widgets/product-categories-block.php:174
#: modules/elementor/widgets/product-categories-list-with-header.php:123
#: modules/elementor/widgets/product-categories-list-with-header.php:153
#: modules/elementor/widgets/product-categories-list.php:117
#: modules/elementor/widgets/product-categories-menu-list.php:129
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:109
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:142
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:194
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:217
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:269
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:308
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:320
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:332
#: modules/elementor/widgets/product-list-categories.php:116
#: modules/elementor/widgets/product-onsale-carousel-2.php:132
#: modules/elementor/widgets/product-onsale-carousel-2.php:144
#: modules/elementor/widgets/product-onsale-carousel-2.php:156
#: modules/elementor/widgets/product-onsale-carousel-2.php:168
#: modules/elementor/widgets/product-onsale-carousel.php:108
#: modules/elementor/widgets/product-onsale-carousel.php:176
#: modules/elementor/widgets/product-onsale-carousel.php:188
#: modules/elementor/widgets/product-onsale-carousel.php:200
#: modules/elementor/widgets/product-onsale-carousel.php:212
#: modules/elementor/widgets/product-onsale-carousel.php:224
#: modules/elementor/widgets/product-onsale-carousel.php:236
#: modules/elementor/widgets/product-onsale-carousel.php:274
#: modules/elementor/widgets/product-onsale.php:108
#: modules/elementor/widgets/products-2-1-2-block.php:197
#: modules/elementor/widgets/products-6-1-block.php:196
#: modules/elementor/widgets/products-6-1-with-categories.php:236
#: modules/elementor/widgets/products-6-1-with-categories.php:257
#: modules/elementor/widgets/products-6-1-with-categories.php:317
#: modules/elementor/widgets/products-cards-carousel.php:138
#: modules/elementor/widgets/products-cards-carousel.php:150
#: modules/elementor/widgets/products-cards-carousel.php:254
#: modules/elementor/widgets/products-cards-carousel.php:274
#: modules/elementor/widgets/products-carousel-1.php:272
#: modules/elementor/widgets/products-carousel-1.php:285
#: modules/elementor/widgets/products-carousel-1.php:298
#: modules/elementor/widgets/products-carousel-1.php:310
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:316
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:328
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:340
#: modules/elementor/widgets/products-carousel-category-with-image.php:135
#: modules/elementor/widgets/products-carousel-category-with-image.php:218
#: modules/elementor/widgets/products-carousel-category-with-image.php:240
#: modules/elementor/widgets/products-carousel-category-with-image.php:356
#: modules/elementor/widgets/products-carousel-category-with-image.php:369
#: modules/elementor/widgets/products-carousel-category-with-image.php:381
#: modules/elementor/widgets/products-carousel-tabs-1.php:280
#: modules/elementor/widgets/products-carousel-tabs-1.php:308
#: modules/elementor/widgets/products-carousel-tabs-1.php:320
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:132
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:368
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:380
#: modules/elementor/widgets/products-carousel-tabs.php:279
#: modules/elementor/widgets/products-carousel-tabs.php:291
#: modules/elementor/widgets/products-carousel-with-timer.php:108
#: modules/elementor/widgets/products-carousel-with-timer.php:302
#: modules/elementor/widgets/products-carousel-with-timer.php:315
#: modules/elementor/widgets/products-carousel-with-timer.php:327
#: modules/elementor/widgets/products-carousel-with-timer.php:339
#: modules/elementor/widgets/products-carousel.php:182
#: modules/elementor/widgets/products-carousel.php:270
#: modules/elementor/widgets/products-carousel.php:282
#: modules/elementor/widgets/products-carousel.php:295
#: modules/elementor/widgets/products-category-with-image.php:226
#: modules/elementor/widgets/products-category-with-image.php:248
#: modules/elementor/widgets/products-list-block.php:236
#: modules/elementor/widgets/products-list-block.php:265
#: modules/elementor/widgets/products-with-category-image.php:226
#: modules/elementor/widgets/products-with-category-image.php:248
#: modules/elementor/widgets/products-with-category-image.php:311
#: modules/elementor/widgets/recently-viewed-products-carousel.php:173
#: modules/elementor/widgets/recently-viewed-products-carousel.php:185
#: modules/elementor/widgets/recently-viewed-products-carousel.php:197
#: modules/elementor/widgets/sidebar-with-products.php:109
#: modules/elementor/widgets/sidebar-with-products.php:304
msgid "Disable"
msgstr ""

#: modules/elementor/widgets/banner-1-6-block.php:43
msgid "Banner 1-6 Block"
msgstr ""

#: modules/elementor/widgets/banner-1-6-block.php:108
#: modules/elementor/widgets/banner-with-products-carousel.php:145
msgid "Enter URL"
msgstr ""

#: modules/elementor/widgets/banner-1-6-block.php:115
#: modules/elementor/widgets/banner-with-products-carousel.php:317
#: modules/elementor/widgets/blog-posts-block.php:157
#: modules/elementor/widgets/brands-block.php:172
#: modules/elementor/widgets/brands-with-category-block.php:269
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:341
#: modules/elementor/widgets/product-category-tags.php:169
#: modules/elementor/widgets/product-onsale-carousel-2.php:177
#: modules/elementor/widgets/products-carousel-1.php:319
#: modules/elementor/widgets/products-carousel-tabs.php:300
#: modules/elementor/widgets/products-categories-1-6.php:145
#: modules/elementor/widgets/three-banners.php:248
msgid "Element Class"
msgstr ""

#: modules/elementor/widgets/banner-1-6-block.php:117
msgid "Enter class here"
msgstr ""

#: modules/elementor/widgets/banner-1-6-block.php:124
msgid "Banners block"
msgstr ""

#: modules/elementor/widgets/banner-1-6-block.php:126
msgid "Maximum 7 Banners"
msgstr ""

#: modules/elementor/widgets/banner-1-6-block.php:135
msgid "Section Element Class"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:43
#: modules/js_composer/config/map.php:7399
msgid "Banner With Products Carousel"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:105
#: modules/elementor/widgets/blog-posts-block.php:97
#: modules/elementor/widgets/brands-block.php:99
#: modules/elementor/widgets/brands-with-category-block.php:99
#: modules/elementor/widgets/deal-and-product-tabs.php:98
#: modules/elementor/widgets/deals-products-carousel.php:128
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:97
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:97
#: modules/elementor/widgets/product-category-tags.php:97
#: modules/elementor/widgets/product-onsale-carousel.php:98
#: modules/elementor/widgets/product-onsale.php:98
#: modules/elementor/widgets/products-2-1-2-block.php:95
#: modules/elementor/widgets/products-carousel-category-with-image.php:97
#: modules/elementor/widgets/products-carousel-tabs-1.php:95
#: modules/elementor/widgets/products-category-with-image.php:97
#: modules/elementor/widgets/products-with-category-image.php:97
#: modules/elementor/widgets/sidebar-with-products.php:98
#: modules/elementor/widgets/sidebar-with-products.php:120
#: modules/js_composer/config/map.php:1211
#: modules/js_composer/config/map.php:1436
#: modules/js_composer/config/map.php:1670
#: modules/js_composer/config/map.php:1889
#: modules/js_composer/config/map.php:2102
#: modules/js_composer/config/map.php:2209
#: modules/js_composer/config/map.php:2279
#: modules/js_composer/config/map.php:2901
#: modules/js_composer/config/map.php:3041
#: modules/js_composer/config/map.php:3165
#: modules/js_composer/config/map.php:3283
#: modules/js_composer/config/map.php:3442
#: modules/js_composer/config/map.php:3601
#: modules/js_composer/config/map.php:3858
#: modules/js_composer/config/map.php:4114
#: modules/js_composer/config/map.php:4176
#: modules/js_composer/config/map.php:4335
#: modules/js_composer/config/map.php:4571
#: modules/js_composer/config/map.php:4779
#: modules/js_composer/config/map.php:5173
#: modules/js_composer/config/map.php:5525
#: modules/js_composer/config/map.php:5786
#: modules/js_composer/config/map.php:6004
#: modules/js_composer/config/map.php:6070
#: modules/js_composer/config/map.php:7102
#: modules/js_composer/config/map.php:7596
#: modules/js_composer/config/map.php:7672
msgid "Enter title"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:113
#: modules/js_composer/config/map.php:7421
msgid "Subtitle"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:115
msgid "Enter subtitle"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:116
#: modules/js_composer/config/map.php:7424
#: modules/js_composer/includes/elements/vc-banner-with-products-carousel.php:9
msgid "SAVE UP TO"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:123
#: modules/js_composer/config/map.php:7429
msgid "Offer Text"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:125
msgid "Enter offer text"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:133
#: modules/js_composer/config/map.php:7437
msgid "Button Text"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:135
msgid "Enter button text"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:136
#: modules/js_composer/config/map.php:7440
#: modules/js_composer/includes/elements/vc-banner-with-products-carousel.php:11
msgid "Start Buying"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:143
#: modules/js_composer/config/map.php:7445
msgid "Button URL"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:152
#: modules/elementor/widgets/deals-products-carousel.php:147
#: modules/elementor/widgets/mobile-product-deals-block.php:134
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:104
#: modules/elementor/widgets/products-carousel-1.php:121
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:95
#: modules/elementor/widgets/products-carousel-category-with-image.php:104
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:199
#: modules/elementor/widgets/products-carousel-with-timer.php:155
#: modules/elementor/widgets/products-carousel.php:106
#: modules/elementor/widgets/products-category-with-image.php:104
#: modules/elementor/widgets/products-list-block.php:132
#: modules/elementor/widgets/products-with-category-image.php:104
#: modules/elementor/widgets/sidebar-with-products.php:127
#: modules/elementor/widgets/two-row-products.php:121
msgid "Shortcode Tags"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:155
#: modules/elementor/widgets/deal-and-product-tabs.php:178
#: modules/elementor/widgets/deal-products-block.php:106
#: modules/elementor/widgets/deals-products-carousel.php:150
#: modules/elementor/widgets/mobile-product-deals-block.php:137
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:107
#: modules/elementor/widgets/products-2-1-2-block.php:107
#: modules/elementor/widgets/products-6-1-block.php:107
#: modules/elementor/widgets/products-6-1-with-categories.php:107
#: modules/elementor/widgets/products-6-1-with-categories.php:187
#: modules/elementor/widgets/products-cards-carousel.php:171
#: modules/elementor/widgets/products-carousel-1.php:124
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:98
#: modules/elementor/widgets/products-carousel-category-with-image.php:107
#: modules/elementor/widgets/products-carousel-tabs-1.php:133
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:202
#: modules/elementor/widgets/products-carousel-tabs.php:123
#: modules/elementor/widgets/products-carousel-with-timer.php:158
#: modules/elementor/widgets/products-carousel.php:109
#: modules/elementor/widgets/products-category-with-image.php:107
#: modules/elementor/widgets/products-list-block.php:135
#: modules/elementor/widgets/products-one-two-block.php:106
#: modules/elementor/widgets/products-tabs-element.php:109
#: modules/elementor/widgets/products-with-category-image.php:107
#: modules/elementor/widgets/sidebar-with-products.php:130
#: modules/elementor/widgets/two-row-products.php:124
#: modules/js_composer/config/map.php:232
#: modules/js_composer/config/map.php:592
#: modules/js_composer/config/map.php:630
#: modules/js_composer/config/map.php:668
#: modules/js_composer/config/map.php:739
#: modules/js_composer/config/map.php:870
#: modules/js_composer/config/map.php:908
#: modules/js_composer/config/map.php:946
#: modules/js_composer/config/map.php:1085
#: modules/js_composer/config/map.php:1278
#: modules/js_composer/config/map.php:1447
#: modules/js_composer/config/map.php:1695
#: modules/js_composer/config/map.php:1937
#: modules/js_composer/config/map.php:2680
#: modules/js_composer/config/map.php:2912
#: modules/js_composer/config/map.php:3052
#: modules/js_composer/config/map.php:3294
#: modules/js_composer/config/map.php:3453
#: modules/js_composer/config/map.php:3612
#: modules/js_composer/config/map.php:3700
#: modules/js_composer/config/map.php:3869
#: modules/js_composer/config/map.php:4346
#: modules/js_composer/config/map.php:4606
#: modules/js_composer/config/map.php:4817
#: modules/js_composer/config/map.php:5002
#: modules/js_composer/config/map.php:5235
#: modules/js_composer/config/map.php:5613
#: modules/js_composer/config/map.php:5874
#: modules/js_composer/config/map.php:6093
#: modules/js_composer/config/map.php:6475
#: modules/js_composer/config/map.php:7114
#: modules/js_composer/config/map.php:7457
msgid "Featured Products"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:156
#: modules/elementor/widgets/deal-and-product-tabs.php:179
#: modules/elementor/widgets/deal-products-block.php:107
#: modules/elementor/widgets/deals-products-carousel.php:151
#: modules/elementor/widgets/mobile-product-deals-block.php:138
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:108
#: modules/elementor/widgets/products-2-1-2-block.php:108
#: modules/elementor/widgets/products-6-1-block.php:108
#: modules/elementor/widgets/products-6-1-with-categories.php:108
#: modules/elementor/widgets/products-6-1-with-categories.php:188
#: modules/elementor/widgets/products-cards-carousel.php:172
#: modules/elementor/widgets/products-carousel-1.php:125
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:99
#: modules/elementor/widgets/products-carousel-category-with-image.php:108
#: modules/elementor/widgets/products-carousel-tabs-1.php:134
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:203
#: modules/elementor/widgets/products-carousel-tabs.php:124
#: modules/elementor/widgets/products-carousel-with-timer.php:159
#: modules/elementor/widgets/products-carousel.php:110
#: modules/elementor/widgets/products-category-with-image.php:108
#: modules/elementor/widgets/products-list-block.php:136
#: modules/elementor/widgets/products-one-two-block.php:107
#: modules/elementor/widgets/products-tabs-element.php:110
#: modules/elementor/widgets/products-with-category-image.php:108
#: modules/elementor/widgets/sidebar-with-products.php:131
#: modules/elementor/widgets/two-row-products.php:125
#: modules/js_composer/config/map.php:233
#: modules/js_composer/config/map.php:593
#: modules/js_composer/config/map.php:631
#: modules/js_composer/config/map.php:669
#: modules/js_composer/config/map.php:740
#: modules/js_composer/config/map.php:871
#: modules/js_composer/config/map.php:909
#: modules/js_composer/config/map.php:947
#: modules/js_composer/config/map.php:1086
#: modules/js_composer/config/map.php:1279
#: modules/js_composer/config/map.php:1448
#: modules/js_composer/config/map.php:1696
#: modules/js_composer/config/map.php:1938
#: modules/js_composer/config/map.php:2681
#: modules/js_composer/config/map.php:2913
#: modules/js_composer/config/map.php:3053
#: modules/js_composer/config/map.php:3295
#: modules/js_composer/config/map.php:3454
#: modules/js_composer/config/map.php:3613
#: modules/js_composer/config/map.php:3701
#: modules/js_composer/config/map.php:3870
#: modules/js_composer/config/map.php:4347
#: modules/js_composer/config/map.php:4607
#: modules/js_composer/config/map.php:4818
#: modules/js_composer/config/map.php:5003
#: modules/js_composer/config/map.php:5236
#: modules/js_composer/config/map.php:5614
#: modules/js_composer/config/map.php:5875
#: modules/js_composer/config/map.php:6094
#: modules/js_composer/config/map.php:6476
#: modules/js_composer/config/map.php:7115
#: modules/js_composer/config/map.php:7458
msgid "On Sale Products"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:157
#: modules/elementor/widgets/deal-and-product-tabs.php:180
#: modules/elementor/widgets/deal-products-block.php:108
#: modules/elementor/widgets/deals-products-carousel.php:152
#: modules/elementor/widgets/mobile-product-deals-block.php:139
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:109
#: modules/elementor/widgets/products-2-1-2-block.php:109
#: modules/elementor/widgets/products-6-1-block.php:109
#: modules/elementor/widgets/products-6-1-with-categories.php:109
#: modules/elementor/widgets/products-6-1-with-categories.php:189
#: modules/elementor/widgets/products-cards-carousel.php:173
#: modules/elementor/widgets/products-carousel-1.php:126
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:100
#: modules/elementor/widgets/products-carousel-category-with-image.php:109
#: modules/elementor/widgets/products-carousel-tabs-1.php:135
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:204
#: modules/elementor/widgets/products-carousel-tabs.php:125
#: modules/elementor/widgets/products-carousel-with-timer.php:160
#: modules/elementor/widgets/products-carousel.php:111
#: modules/elementor/widgets/products-category-with-image.php:109
#: modules/elementor/widgets/products-list-block.php:137
#: modules/elementor/widgets/products-one-two-block.php:108
#: modules/elementor/widgets/products-tabs-element.php:111
#: modules/elementor/widgets/products-with-category-image.php:109
#: modules/elementor/widgets/sidebar-with-products.php:132
#: modules/elementor/widgets/two-row-products.php:126
#: modules/js_composer/config/map.php:234
#: modules/js_composer/config/map.php:594
#: modules/js_composer/config/map.php:632
#: modules/js_composer/config/map.php:670
#: modules/js_composer/config/map.php:741
#: modules/js_composer/config/map.php:872
#: modules/js_composer/config/map.php:910
#: modules/js_composer/config/map.php:948
#: modules/js_composer/config/map.php:1087
#: modules/js_composer/config/map.php:1280
#: modules/js_composer/config/map.php:1449
#: modules/js_composer/config/map.php:1697
#: modules/js_composer/config/map.php:1939
#: modules/js_composer/config/map.php:2682
#: modules/js_composer/config/map.php:2914
#: modules/js_composer/config/map.php:3054
#: modules/js_composer/config/map.php:3296
#: modules/js_composer/config/map.php:3455
#: modules/js_composer/config/map.php:3614
#: modules/js_composer/config/map.php:3702
#: modules/js_composer/config/map.php:3871
#: modules/js_composer/config/map.php:4348
#: modules/js_composer/config/map.php:4608
#: modules/js_composer/config/map.php:4819
#: modules/js_composer/config/map.php:5004
#: modules/js_composer/config/map.php:5237
#: modules/js_composer/config/map.php:5615
#: modules/js_composer/config/map.php:5876
#: modules/js_composer/config/map.php:6095
#: modules/js_composer/config/map.php:6477
#: modules/js_composer/config/map.php:7116
#: modules/js_composer/config/map.php:7459
msgid "Top Rated Products"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:158
#: modules/elementor/widgets/deal-and-product-tabs.php:181
#: modules/elementor/widgets/deal-products-block.php:109
#: modules/elementor/widgets/deals-products-carousel.php:153
#: modules/elementor/widgets/mobile-product-deals-block.php:140
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:110
#: modules/elementor/widgets/products-2-1-2-block.php:110
#: modules/elementor/widgets/products-6-1-block.php:110
#: modules/elementor/widgets/products-6-1-with-categories.php:110
#: modules/elementor/widgets/products-6-1-with-categories.php:190
#: modules/elementor/widgets/products-cards-carousel.php:174
#: modules/elementor/widgets/products-carousel-1.php:127
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:101
#: modules/elementor/widgets/products-carousel-category-with-image.php:110
#: modules/elementor/widgets/products-carousel-tabs-1.php:136
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:205
#: modules/elementor/widgets/products-carousel-tabs.php:126
#: modules/elementor/widgets/products-carousel-with-timer.php:161
#: modules/elementor/widgets/products-carousel.php:112
#: modules/elementor/widgets/products-category-with-image.php:110
#: modules/elementor/widgets/products-list-block.php:138
#: modules/elementor/widgets/products-one-two-block.php:109
#: modules/elementor/widgets/products-tabs-element.php:112
#: modules/elementor/widgets/products-with-category-image.php:110
#: modules/elementor/widgets/sidebar-with-products.php:133
#: modules/elementor/widgets/two-row-products.php:127
#: modules/js_composer/config/map.php:235
#: modules/js_composer/config/map.php:595
#: modules/js_composer/config/map.php:633
#: modules/js_composer/config/map.php:671
#: modules/js_composer/config/map.php:742
#: modules/js_composer/config/map.php:873
#: modules/js_composer/config/map.php:911
#: modules/js_composer/config/map.php:949
#: modules/js_composer/config/map.php:1088
#: modules/js_composer/config/map.php:1281
#: modules/js_composer/config/map.php:1450
#: modules/js_composer/config/map.php:1698
#: modules/js_composer/config/map.php:1940
#: modules/js_composer/config/map.php:2683
#: modules/js_composer/config/map.php:2915
#: modules/js_composer/config/map.php:3055
#: modules/js_composer/config/map.php:3297
#: modules/js_composer/config/map.php:3456
#: modules/js_composer/config/map.php:3615
#: modules/js_composer/config/map.php:3703
#: modules/js_composer/config/map.php:3872
#: modules/js_composer/config/map.php:4349
#: modules/js_composer/config/map.php:4609
#: modules/js_composer/config/map.php:4820
#: modules/js_composer/config/map.php:5005
#: modules/js_composer/config/map.php:5238
#: modules/js_composer/config/map.php:5616
#: modules/js_composer/config/map.php:5877
#: modules/js_composer/config/map.php:6096
#: modules/js_composer/config/map.php:6478
#: modules/js_composer/config/map.php:7117
#: modules/js_composer/config/map.php:7460
msgid "Recent Products"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:159
#: modules/elementor/widgets/deal-and-product-tabs.php:182
#: modules/elementor/widgets/deal-products-block.php:110
#: modules/elementor/widgets/deals-products-carousel.php:154
#: modules/elementor/widgets/mobile-product-deals-block.php:141
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:111
#: modules/elementor/widgets/products-2-1-2-block.php:111
#: modules/elementor/widgets/products-6-1-block.php:111
#: modules/elementor/widgets/products-6-1-with-categories.php:111
#: modules/elementor/widgets/products-6-1-with-categories.php:191
#: modules/elementor/widgets/products-cards-carousel.php:175
#: modules/elementor/widgets/products-carousel-1.php:128
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:102
#: modules/elementor/widgets/products-carousel-category-with-image.php:111
#: modules/elementor/widgets/products-carousel-tabs-1.php:137
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:206
#: modules/elementor/widgets/products-carousel-tabs.php:127
#: modules/elementor/widgets/products-carousel-with-timer.php:162
#: modules/elementor/widgets/products-carousel.php:113
#: modules/elementor/widgets/products-category-with-image.php:111
#: modules/elementor/widgets/products-list-block.php:139
#: modules/elementor/widgets/products-one-two-block.php:110
#: modules/elementor/widgets/products-tabs-element.php:113
#: modules/elementor/widgets/products-with-category-image.php:111
#: modules/elementor/widgets/sidebar-with-products.php:134
#: modules/elementor/widgets/two-row-products.php:128
#: modules/js_composer/config/map.php:236
#: modules/js_composer/config/map.php:596
#: modules/js_composer/config/map.php:634
#: modules/js_composer/config/map.php:672
#: modules/js_composer/config/map.php:743
#: modules/js_composer/config/map.php:874
#: modules/js_composer/config/map.php:912
#: modules/js_composer/config/map.php:950
#: modules/js_composer/config/map.php:1089
#: modules/js_composer/config/map.php:1282
#: modules/js_composer/config/map.php:1451
#: modules/js_composer/config/map.php:1699
#: modules/js_composer/config/map.php:1941
#: modules/js_composer/config/map.php:2684
#: modules/js_composer/config/map.php:2916
#: modules/js_composer/config/map.php:3056
#: modules/js_composer/config/map.php:3298
#: modules/js_composer/config/map.php:3457
#: modules/js_composer/config/map.php:3616
#: modules/js_composer/config/map.php:3704
#: modules/js_composer/config/map.php:3873
#: modules/js_composer/config/map.php:4350
#: modules/js_composer/config/map.php:4610
#: modules/js_composer/config/map.php:4821
#: modules/js_composer/config/map.php:5006
#: modules/js_composer/config/map.php:5239
#: modules/js_composer/config/map.php:5617
#: modules/js_composer/config/map.php:5878
#: modules/js_composer/config/map.php:6097
#: modules/js_composer/config/map.php:6479
#: modules/js_composer/config/map.php:7118
#: modules/js_composer/config/map.php:7461
msgid "Best Selling Products"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:160
#: modules/elementor/widgets/deal-and-product-tabs.php:183
#: modules/elementor/widgets/deal-products-block.php:111
#: modules/elementor/widgets/deals-products-carousel.php:155
#: modules/elementor/widgets/mobile-product-deals-block.php:142
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:112
#: modules/elementor/widgets/products-2-1-2-block.php:112
#: modules/elementor/widgets/products-6-1-block.php:112
#: modules/elementor/widgets/products-6-1-with-categories.php:112
#: modules/elementor/widgets/products-cards-carousel.php:176
#: modules/elementor/widgets/products-carousel-1.php:129
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:103
#: modules/elementor/widgets/products-carousel-category-with-image.php:112
#: modules/elementor/widgets/products-carousel-tabs-1.php:138
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:207
#: modules/elementor/widgets/products-carousel-tabs.php:128
#: modules/elementor/widgets/products-carousel-with-timer.php:163
#: modules/elementor/widgets/products-carousel.php:114
#: modules/elementor/widgets/products-category-with-image.php:112
#: modules/elementor/widgets/products-list-block.php:140
#: modules/elementor/widgets/products-one-two-block.php:111
#: modules/elementor/widgets/products-tabs-element.php:114
#: modules/elementor/widgets/products-with-category-image.php:112
#: modules/elementor/widgets/sidebar-with-products.php:135
#: modules/elementor/widgets/two-row-products.php:129
#: modules/js_composer/config/map.php:238
#: modules/js_composer/config/map.php:598
#: modules/js_composer/config/map.php:636
#: modules/js_composer/config/map.php:674
#: modules/js_composer/config/map.php:745
#: modules/js_composer/config/map.php:876
#: modules/js_composer/config/map.php:914
#: modules/js_composer/config/map.php:952
#: modules/js_composer/config/map.php:1091
#: modules/js_composer/config/map.php:1284
#: modules/js_composer/config/map.php:1453
#: modules/js_composer/config/map.php:1701
#: modules/js_composer/config/map.php:1943
#: modules/js_composer/config/map.php:2686
#: modules/js_composer/config/map.php:2918
#: modules/js_composer/config/map.php:3058
#: modules/js_composer/config/map.php:3300
#: modules/js_composer/config/map.php:3459
#: modules/js_composer/config/map.php:3618
#: modules/js_composer/config/map.php:3875
#: modules/js_composer/config/map.php:4352
#: modules/js_composer/config/map.php:4612
#: modules/js_composer/config/map.php:4823
#: modules/js_composer/config/map.php:5008
#: modules/js_composer/config/map.php:5241
#: modules/js_composer/config/map.php:5619
#: modules/js_composer/config/map.php:5880
#: modules/js_composer/config/map.php:6099
#: modules/js_composer/config/map.php:6481
#: modules/js_composer/config/map.php:7120
#: modules/js_composer/config/map.php:7463
msgid "Product Category"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:161
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:208
#: modules/elementor/widgets/sidebar-with-products.php:136
#: modules/js_composer/config/map.php:239
#: modules/js_composer/config/map.php:746
#: modules/js_composer/config/map.php:1092
#: modules/js_composer/config/map.php:1285
#: modules/js_composer/config/map.php:1454
#: modules/js_composer/config/map.php:1702
#: modules/js_composer/config/map.php:1944
#: modules/js_composer/config/map.php:2687
#: modules/js_composer/config/map.php:2919
#: modules/js_composer/config/map.php:3059
#: modules/js_composer/config/map.php:3301
#: modules/js_composer/config/map.php:3460
#: modules/js_composer/config/map.php:3619
#: modules/js_composer/config/map.php:3876
#: modules/js_composer/config/map.php:4353
#: modules/js_composer/config/map.php:4613
#: modules/js_composer/config/map.php:4824
#: modules/js_composer/config/map.php:5009
#: modules/js_composer/config/map.php:5242
#: modules/js_composer/config/map.php:5620
#: modules/js_composer/config/map.php:5881
#: modules/js_composer/config/map.php:6100
#: modules/js_composer/config/map.php:6482
#: modules/js_composer/config/map.php:7121
#: modules/js_composer/config/map.php:7464
msgid "Product Attribute"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:162
#: modules/elementor/widgets/deal-and-product-tabs.php:184
#: modules/elementor/widgets/deal-products-block.php:112
#: modules/elementor/widgets/deals-products-carousel.php:156
#: modules/elementor/widgets/mobile-product-deals-block.php:143
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:113
#: modules/elementor/widgets/products-2-1-2-block.php:113
#: modules/elementor/widgets/products-6-1-block.php:113
#: modules/elementor/widgets/products-6-1-with-categories.php:113
#: modules/elementor/widgets/products-6-1-with-categories.php:192
#: modules/elementor/widgets/products-cards-carousel.php:177
#: modules/elementor/widgets/products-carousel-1.php:130
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:104
#: modules/elementor/widgets/products-carousel-category-with-image.php:113
#: modules/elementor/widgets/products-carousel-tabs-1.php:139
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:209
#: modules/elementor/widgets/products-carousel-tabs.php:129
#: modules/elementor/widgets/products-carousel-with-timer.php:164
#: modules/elementor/widgets/products-carousel.php:115
#: modules/elementor/widgets/products-category-with-image.php:113
#: modules/elementor/widgets/products-list-block.php:141
#: modules/elementor/widgets/products-one-two-block.php:112
#: modules/elementor/widgets/products-tabs-element.php:115
#: modules/elementor/widgets/products-with-category-image.php:113
#: modules/elementor/widgets/sidebar-with-products.php:137
#: modules/elementor/widgets/two-row-products.php:130
#: modules/js_composer/config/map.php:237
#: modules/js_composer/config/map.php:597
#: modules/js_composer/config/map.php:635
#: modules/js_composer/config/map.php:673
#: modules/js_composer/config/map.php:744
#: modules/js_composer/config/map.php:875
#: modules/js_composer/config/map.php:913
#: modules/js_composer/config/map.php:951
#: modules/js_composer/config/map.php:1090
#: modules/js_composer/config/map.php:1283
#: modules/js_composer/config/map.php:1452
#: modules/js_composer/config/map.php:1700
#: modules/js_composer/config/map.php:1942
#: modules/js_composer/config/map.php:2685
#: modules/js_composer/config/map.php:2917
#: modules/js_composer/config/map.php:3057
#: modules/js_composer/config/map.php:3299
#: modules/js_composer/config/map.php:3458
#: modules/js_composer/config/map.php:3617
#: modules/js_composer/config/map.php:3705
#: modules/js_composer/config/map.php:3874
#: modules/js_composer/config/map.php:4351
#: modules/js_composer/config/map.php:4611
#: modules/js_composer/config/map.php:4822
#: modules/js_composer/config/map.php:5007
#: modules/js_composer/config/map.php:5240
#: modules/js_composer/config/map.php:5618
#: modules/js_composer/config/map.php:5879
#: modules/js_composer/config/map.php:6098
#: modules/js_composer/config/map.php:6480
#: modules/js_composer/config/map.php:7119
#: modules/js_composer/config/map.php:7462
msgid "Products"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:171
#: modules/elementor/widgets/deal-and-product-tabs.php:140
#: modules/elementor/widgets/deal-and-product-tabs.php:253
#: modules/elementor/widgets/deals-products-carousel.php:194
#: modules/elementor/widgets/mobile-product-deals-block.php:182
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:152
#: modules/elementor/widgets/product-onsale-carousel-2.php:105
#: modules/elementor/widgets/product-onsale-carousel.php:148
#: modules/elementor/widgets/product-onsale.php:148
#: modules/elementor/widgets/products-2-1-2-block.php:142
#: modules/elementor/widgets/products-6-1-block.php:142
#: modules/elementor/widgets/products-6-1-with-categories.php:142
#: modules/elementor/widgets/products-cards-carousel.php:208
#: modules/elementor/widgets/products-carousel-1.php:170
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:134
#: modules/elementor/widgets/products-carousel-tabs-1.php:178
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:162
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:238
#: modules/elementor/widgets/products-carousel-tabs.php:168
#: modules/elementor/widgets/products-carousel-with-timer.php:203
#: modules/elementor/widgets/products-carousel.php:152
#: modules/elementor/widgets/products-list-block.php:190
#: modules/elementor/widgets/sidebar-with-products.php:210
#: modules/elementor/widgets/two-row-products.php:191
#: modules/js_composer/config/map.php:269
#: modules/js_composer/config/map.php:789
#: modules/js_composer/config/map.php:1121
#: modules/js_composer/config/map.php:1307
#: modules/js_composer/config/map.php:1483
#: modules/js_composer/config/map.php:1731
#: modules/js_composer/config/map.php:1973
#: modules/js_composer/config/map.php:2645
#: modules/js_composer/config/map.php:2709
#: modules/js_composer/config/map.php:2957
#: modules/js_composer/config/map.php:3081
#: modules/js_composer/config/map.php:3323
#: modules/js_composer/config/map.php:3482
#: modules/js_composer/config/map.php:3641
#: modules/js_composer/config/map.php:3919
#: modules/js_composer/config/map.php:4143
#: modules/js_composer/config/map.php:4216
#: modules/js_composer/config/map.php:4383
#: modules/js_composer/config/map.php:4643
#: modules/js_composer/config/map.php:4853
#: modules/js_composer/config/map.php:5038
#: modules/js_composer/config/map.php:5202
#: modules/js_composer/config/map.php:5284
#: modules/js_composer/config/map.php:5652
#: modules/js_composer/config/map.php:5924
#: modules/js_composer/config/map.php:6145
#: modules/js_composer/config/map.php:6511
#: modules/js_composer/config/map.php:6731
#: modules/js_composer/config/map.php:7171
#: modules/js_composer/config/map.php:7470
msgid "Product Choice"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:174
#: modules/elementor/widgets/deal-and-product-tabs.php:256
#: modules/elementor/widgets/deal-products-block.php:164
#: modules/elementor/widgets/products-2-1-2-block.php:145
#: modules/elementor/widgets/products-6-1-block.php:145
#: modules/elementor/widgets/products-6-1-with-categories.php:145
#: modules/elementor/widgets/products-6-1-with-categories.php:205
#: modules/elementor/widgets/products-cards-carousel.php:211
#: modules/elementor/widgets/products-carousel-1.php:173
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:137
#: modules/elementor/widgets/products-carousel-category-with-image.php:167
#: modules/elementor/widgets/products-carousel-tabs-1.php:181
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:241
#: modules/elementor/widgets/products-carousel-tabs.php:171
#: modules/elementor/widgets/products-carousel-with-timer.php:206
#: modules/elementor/widgets/products-category-with-image.php:175
#: modules/elementor/widgets/products-list-block.php:194
#: modules/elementor/widgets/products-one-two-block.php:144
#: modules/elementor/widgets/products-with-category-image.php:175
#: modules/elementor/widgets/sidebar-with-products.php:213
#: modules/elementor/widgets/two-row-products.php:194
#: modules/js_composer/config/map.php:273
#: modules/js_composer/config/map.php:793
#: modules/js_composer/config/map.php:1125
#: modules/js_composer/config/map.php:1311
#: modules/js_composer/config/map.php:1487
#: modules/js_composer/config/map.php:1735
#: modules/js_composer/config/map.php:1977
#: modules/js_composer/config/map.php:2713
#: modules/js_composer/config/map.php:2961
#: modules/js_composer/config/map.php:3085
#: modules/js_composer/config/map.php:3327
#: modules/js_composer/config/map.php:3486
#: modules/js_composer/config/map.php:3645
#: modules/js_composer/config/map.php:3715
#: modules/js_composer/config/map.php:3923
#: modules/js_composer/config/map.php:4387
#: modules/js_composer/config/map.php:4647
#: modules/js_composer/config/map.php:4857
#: modules/js_composer/config/map.php:5042
#: modules/js_composer/config/map.php:5288
#: modules/js_composer/config/map.php:5656
#: modules/js_composer/config/map.php:5928
#: modules/js_composer/config/map.php:6149
#: modules/js_composer/config/map.php:6515
#: modules/js_composer/config/map.php:7175
#: modules/js_composer/config/map.php:7474
msgid "IDs"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:175
#: modules/elementor/widgets/deal-and-product-tabs.php:257
#: modules/elementor/widgets/deal-products-block.php:165
#: modules/elementor/widgets/products-2-1-2-block.php:146
#: modules/elementor/widgets/products-6-1-block.php:146
#: modules/elementor/widgets/products-6-1-with-categories.php:146
#: modules/elementor/widgets/products-6-1-with-categories.php:206
#: modules/elementor/widgets/products-cards-carousel.php:212
#: modules/elementor/widgets/products-carousel-1.php:174
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:138
#: modules/elementor/widgets/products-carousel-category-with-image.php:168
#: modules/elementor/widgets/products-carousel-tabs-1.php:182
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:242
#: modules/elementor/widgets/products-carousel-tabs.php:172
#: modules/elementor/widgets/products-carousel-with-timer.php:207
#: modules/elementor/widgets/products-category-with-image.php:176
#: modules/elementor/widgets/products-list-block.php:195
#: modules/elementor/widgets/products-one-two-block.php:145
#: modules/elementor/widgets/products-with-category-image.php:176
#: modules/elementor/widgets/sidebar-with-products.php:214
#: modules/elementor/widgets/two-row-products.php:195
#: modules/js_composer/config/map.php:274
#: modules/js_composer/config/map.php:794
#: modules/js_composer/config/map.php:1126
#: modules/js_composer/config/map.php:1312
#: modules/js_composer/config/map.php:1488
#: modules/js_composer/config/map.php:1736
#: modules/js_composer/config/map.php:1978
#: modules/js_composer/config/map.php:2714
#: modules/js_composer/config/map.php:2962
#: modules/js_composer/config/map.php:3086
#: modules/js_composer/config/map.php:3328
#: modules/js_composer/config/map.php:3487
#: modules/js_composer/config/map.php:3646
#: modules/js_composer/config/map.php:3716
#: modules/js_composer/config/map.php:3924
#: modules/js_composer/config/map.php:4388
#: modules/js_composer/config/map.php:4648
#: modules/js_composer/config/map.php:4858
#: modules/js_composer/config/map.php:5043
#: modules/js_composer/config/map.php:5289
#: modules/js_composer/config/map.php:5657
#: modules/js_composer/config/map.php:5929
#: modules/js_composer/config/map.php:6150
#: modules/js_composer/config/map.php:6516
#: modules/js_composer/config/map.php:7176
#: modules/js_composer/config/map.php:7475
msgid "SKUs"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:186
#: modules/elementor/widgets/deals-products-carousel.php:203
#: modules/elementor/widgets/mobile-product-deals-block.php:191
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:161
#: modules/elementor/widgets/products-carousel-1.php:184
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:149
#: modules/elementor/widgets/products-carousel-category-with-image.php:177
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:176
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:253
#: modules/elementor/widgets/products-carousel-with-timer.php:217
#: modules/elementor/widgets/products-carousel.php:161
#: modules/elementor/widgets/products-category-with-image.php:185
#: modules/elementor/widgets/products-with-category-image.php:185
#: modules/elementor/widgets/sidebar-with-products.php:225
#: modules/elementor/widgets/two-row-products.php:205
msgid "Product id or SKUs"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:188
#: modules/elementor/widgets/sidebar-with-products.php:227
msgid "Separate multiple Product ids or SKUs by Comma "
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:189
#: modules/elementor/widgets/deal-and-product-tabs.php:268
#: modules/elementor/widgets/deal-products-block.php:176
#: modules/elementor/widgets/deals-products-carousel.php:205
#: modules/elementor/widgets/mobile-product-deals-block.php:193
#: modules/elementor/widgets/products-2-1-2-block.php:159
#: modules/elementor/widgets/products-6-1-block.php:159
#: modules/elementor/widgets/products-6-1-with-categories.php:159
#: modules/elementor/widgets/products-cards-carousel.php:224
#: modules/elementor/widgets/products-carousel-1.php:186
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:151
#: modules/elementor/widgets/products-carousel-category-with-image.php:179
#: modules/elementor/widgets/products-carousel-tabs-1.php:193
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:178
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:255
#: modules/elementor/widgets/products-carousel-tabs.php:183
#: modules/elementor/widgets/products-carousel-with-timer.php:219
#: modules/elementor/widgets/products-carousel.php:163
#: modules/elementor/widgets/products-category-with-image.php:187
#: modules/elementor/widgets/products-one-two-block.php:156
#: modules/elementor/widgets/products-with-category-image.php:187
#: modules/elementor/widgets/sidebar-with-products.php:228
#: modules/elementor/widgets/two-row-products.php:207
msgid "Enter IDs/SKUs separate by comma(,)."
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:199
#: modules/elementor/widgets/brands-with-category-block.php:126
#: modules/elementor/widgets/category-icons-carousel.php:95
#: modules/elementor/widgets/deal-and-product-tabs.php:193
#: modules/elementor/widgets/deal-products-block.php:141
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:142
#: modules/elementor/widgets/product-category-tags.php:105
#: modules/elementor/widgets/products-cards-carousel.php:159
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:181
#: modules/elementor/widgets/products-carousel-category-with-image.php:122
#: modules/elementor/widgets/products-carousel-tabs-1.php:148
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:338
#: modules/elementor/widgets/products-carousel-tabs.php:138
#: modules/elementor/widgets/products-carousel.php:124
#: modules/elementor/widgets/products-categories-1-6.php:93
#: modules/elementor/widgets/products-category-with-image.php:122
#: modules/elementor/widgets/products-list-block.php:170
#: modules/elementor/widgets/products-tabs-element.php:124
#: modules/elementor/widgets/products-with-category-image.php:122
#: modules/elementor/widgets/recent-viewed-products.php:103
#: modules/elementor/widgets/recently-viewed-products-carousel.php:103
#: modules/elementor/widgets/sidebar-with-products.php:146
#: modules/elementor/widgets/two-row-products.php:139
#: modules/js_composer/config/map.php:752
#: modules/js_composer/config/map.php:1098
#: modules/js_composer/config/map.php:1267
#: modules/js_composer/config/map.php:2766
#: modules/js_composer/config/map.php:2941
#: modules/js_composer/config/map.php:3882
#: modules/js_composer/config/map.php:4375
#: modules/js_composer/config/map.php:4635
#: modules/js_composer/config/map.php:4830
#: modules/js_composer/config/map.php:5248
#: modules/js_composer/config/map.php:5487
#: modules/js_composer/config/map.php:5903
#: modules/js_composer/config/map.php:6122
#: modules/js_composer/config/map.php:7147
msgid "Limit"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:201
#: modules/elementor/widgets/brands-with-category-block.php:128
#: modules/elementor/widgets/product-category-tags.php:107
#: modules/elementor/widgets/products-carousel-category-with-image.php:124
#: modules/elementor/widgets/products-categories-1-6.php:95
#: modules/elementor/widgets/products-category-with-image.php:124
#: modules/elementor/widgets/products-with-category-image.php:124
#: modules/elementor/widgets/sidebar-with-products.php:148
msgid "Enter limit of the products."
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:209
#: modules/elementor/widgets/blog-posts-block.php:133
#: modules/elementor/widgets/brands-block.php:135
#: modules/elementor/widgets/brands-with-category-block.php:136
#: modules/elementor/widgets/brands-with-category-block.php:194
#: modules/elementor/widgets/deals-products-carousel.php:174
#: modules/elementor/widgets/mobile-product-deals-block.php:161
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:122
#: modules/elementor/widgets/product-categories-block-v2.php:126
#: modules/elementor/widgets/product-category-tags.php:127
#: modules/elementor/widgets/products-carousel-1.php:148
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:113
#: modules/elementor/widgets/products-carousel-category-with-image.php:144
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:218
#: modules/elementor/widgets/products-carousel-with-timer.php:182
#: modules/elementor/widgets/products-carousel.php:133
#: modules/elementor/widgets/products-categories-1-6.php:103
#: modules/elementor/widgets/products-category-with-image.php:152
#: modules/elementor/widgets/products-list-block.php:150
#: modules/elementor/widgets/products-with-category-image.php:152
#: modules/elementor/widgets/sidebar-with-products.php:186
#: modules/elementor/widgets/two-row-products.php:169
msgid "Orderby"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:211
#: modules/elementor/widgets/blog-posts-block.php:135
#: modules/elementor/widgets/brands-carousel.php:128
#: modules/elementor/widgets/deal-products-block.php:123
#: modules/elementor/widgets/deals-products-carousel.php:176
#: modules/elementor/widgets/mobile-product-deals-block.php:163
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:124
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:233
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:229
#: modules/elementor/widgets/products-2-1-2-block.php:124
#: modules/elementor/widgets/products-6-1-block.php:124
#: modules/elementor/widgets/products-6-1-with-categories.php:124
#: modules/elementor/widgets/products-6-1-with-categories.php:330
#: modules/elementor/widgets/products-cards-carousel.php:189
#: modules/elementor/widgets/products-cards-carousel.php:286
#: modules/elementor/widgets/products-carousel-1.php:150
#: modules/elementor/widgets/products-carousel-category-with-image.php:146
#: modules/elementor/widgets/products-carousel-category-with-image.php:251
#: modules/elementor/widgets/products-carousel-with-timer.php:184
#: modules/elementor/widgets/products-categories-1-6.php:105
#: modules/elementor/widgets/products-category-with-image.php:154
#: modules/elementor/widgets/products-category-with-image.php:259
#: modules/elementor/widgets/products-list-block.php:152
#: modules/elementor/widgets/products-list-block.php:276
#: modules/elementor/widgets/products-with-category-image.php:154
#: modules/elementor/widgets/products-with-category-image.php:259
#: modules/elementor/widgets/products-with-category-image.php:322
#: modules/elementor/widgets/sidebar-with-products.php:188
#: modules/elementor/widgets/two-row-products.php:171
#: modules/js_composer/config/map.php:255
#: modules/js_composer/config/map.php:1293
#: modules/js_composer/config/map.php:1390
#: modules/js_composer/config/map.php:1469
#: modules/js_composer/config/map.php:1717
#: modules/js_composer/config/map.php:1959
#: modules/js_composer/config/map.php:2128
#: modules/js_composer/config/map.php:2927
#: modules/js_composer/config/map.php:3067
#: modules/js_composer/config/map.php:3309
#: modules/js_composer/config/map.php:3397
#: modules/js_composer/config/map.php:3468
#: modules/js_composer/config/map.php:3556
#: modules/js_composer/config/map.php:3627
#: modules/js_composer/config/map.php:3766
#: modules/js_composer/config/map.php:3813
#: modules/js_composer/config/map.php:3905
#: modules/js_composer/config/map.php:4010
#: modules/js_composer/config/map.php:4057
#: modules/js_composer/config/map.php:4361
#: modules/js_composer/config/map.php:4474
#: modules/js_composer/config/map.php:4621
#: modules/js_composer/config/map.php:4734
#: modules/js_composer/config/map.php:5024
#: modules/js_composer/config/map.php:5387
#: modules/js_composer/config/map.php:5568
#: modules/js_composer/config/map.php:5638
#: modules/js_composer/config/map.php:5829
#: modules/js_composer/config/map.php:5889
#: modules/js_composer/config/map.php:6108
#: modules/js_composer/config/map.php:6497
#: modules/js_composer/config/map.php:6864
#: modules/js_composer/config/map.php:6920
#: modules/js_composer/config/map.php:7129
#: modules/js_composer/config/map.php:7499
msgid ""
" Sort retrieved posts by parameter. Defaults to 'date'. One or more options "
"can be passed"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:219
#: modules/elementor/widgets/blog-posts-block.php:143
#: modules/elementor/widgets/brands-block.php:145
#: modules/elementor/widgets/brands-carousel.php:137
#: modules/elementor/widgets/brands-with-category-block.php:146
#: modules/elementor/widgets/brands-with-category-block.php:204
#: modules/elementor/widgets/category-icons-carousel.php:126
#: modules/elementor/widgets/deal-and-product-tabs.php:243
#: modules/elementor/widgets/deal-products-block.php:131
#: modules/elementor/widgets/deals-products-carousel.php:184
#: modules/elementor/widgets/mobile-product-deals-block.php:171
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:132
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:241
#: modules/elementor/widgets/product-categories-block-v2.php:136
#: modules/elementor/widgets/product-categories-block.php:145
#: modules/elementor/widgets/product-categories-list-with-header.php:173
#: modules/elementor/widgets/product-categories-list.php:137
#: modules/elementor/widgets/product-categories-menu-list.php:148
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:161
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:236
#: modules/elementor/widgets/product-category-tags.php:137
#: modules/elementor/widgets/product-list-categories.php:136
#: modules/elementor/widgets/products-2-1-2-block.php:132
#: modules/elementor/widgets/products-2-1-2-block.php:217
#: modules/elementor/widgets/products-6-1-block.php:132
#: modules/elementor/widgets/products-6-1-block.php:216
#: modules/elementor/widgets/products-6-1-with-categories.php:132
#: modules/elementor/widgets/products-6-1-with-categories.php:277
#: modules/elementor/widgets/products-6-1-with-categories.php:338
#: modules/elementor/widgets/products-cards-carousel.php:197
#: modules/elementor/widgets/products-cards-carousel.php:294
#: modules/elementor/widgets/products-carousel-1.php:158
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:123
#: modules/elementor/widgets/products-carousel-category-with-image.php:154
#: modules/elementor/widgets/products-carousel-category-with-image.php:259
#: modules/elementor/widgets/products-carousel-tabs-1.php:168
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:228
#: modules/elementor/widgets/products-carousel-tabs.php:158
#: modules/elementor/widgets/products-carousel-with-timer.php:192
#: modules/elementor/widgets/products-carousel.php:142
#: modules/elementor/widgets/products-categories-1-6.php:113
#: modules/elementor/widgets/products-category-with-image.php:162
#: modules/elementor/widgets/products-category-with-image.php:267
#: modules/elementor/widgets/products-list-block.php:160
#: modules/elementor/widgets/products-list-block.php:284
#: modules/elementor/widgets/products-one-two-block.php:131
#: modules/elementor/widgets/products-tabs-element.php:173
#: modules/elementor/widgets/products-with-category-image.php:162
#: modules/elementor/widgets/products-with-category-image.php:267
#: modules/elementor/widgets/products-with-category-image.php:330
#: modules/elementor/widgets/sidebar-with-products.php:196
#: modules/elementor/widgets/two-row-products.php:179
#: modules/js_composer/config/map.php:261
#: modules/js_composer/config/map.php:781
#: modules/js_composer/config/map.php:1113
#: modules/js_composer/config/map.php:1299
#: modules/js_composer/config/map.php:1396
#: modules/js_composer/config/map.php:1475
#: modules/js_composer/config/map.php:1723
#: modules/js_composer/config/map.php:1965
#: modules/js_composer/config/map.php:2134
#: modules/js_composer/config/map.php:2241
#: modules/js_composer/config/map.php:2318
#: modules/js_composer/config/map.php:2402
#: modules/js_composer/config/map.php:2487
#: modules/js_composer/config/map.php:2701
#: modules/js_composer/config/map.php:2856
#: modules/js_composer/config/map.php:2933
#: modules/js_composer/config/map.php:3073
#: modules/js_composer/config/map.php:3227
#: modules/js_composer/config/map.php:3315
#: modules/js_composer/config/map.php:3403
#: modules/js_composer/config/map.php:3474
#: modules/js_composer/config/map.php:3562
#: modules/js_composer/config/map.php:3633
#: modules/js_composer/config/map.php:3772
#: modules/js_composer/config/map.php:3819
#: modules/js_composer/config/map.php:3911
#: modules/js_composer/config/map.php:4016
#: modules/js_composer/config/map.php:4063
#: modules/js_composer/config/map.php:4367
#: modules/js_composer/config/map.php:4480
#: modules/js_composer/config/map.php:4627
#: modules/js_composer/config/map.php:4740
#: modules/js_composer/config/map.php:4845
#: modules/js_composer/config/map.php:5030
#: modules/js_composer/config/map.php:5276
#: modules/js_composer/config/map.php:5393
#: modules/js_composer/config/map.php:5574
#: modules/js_composer/config/map.php:5644
#: modules/js_composer/config/map.php:5835
#: modules/js_composer/config/map.php:5895
#: modules/js_composer/config/map.php:6114
#: modules/js_composer/config/map.php:6343
#: modules/js_composer/config/map.php:6503
#: modules/js_composer/config/map.php:6614
#: modules/js_composer/config/map.php:6677
#: modules/js_composer/config/map.php:7135
#: modules/js_composer/config/map.php:7505
#: modules/js_composer/config/map.php:7635
#: modules/js_composer/config/map.php:7711
msgid "Order"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:221
#: modules/elementor/widgets/brands-block.php:147
#: modules/elementor/widgets/brands-with-category-block.php:148
#: modules/elementor/widgets/brands-with-category-block.php:206
#: modules/elementor/widgets/product-categories-block-v2.php:138
#: modules/elementor/widgets/product-categories-block.php:147
#: modules/elementor/widgets/product-categories-list-with-header.php:175
#: modules/elementor/widgets/product-categories-list.php:139
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:163
#: modules/elementor/widgets/product-category-tags.php:139
#: modules/elementor/widgets/product-list-categories.php:138
#: modules/elementor/widgets/sidebar-with-products.php:198
#: modules/js_composer/config/map.php:2243
#: modules/js_composer/config/map.php:2320
#: modules/js_composer/config/map.php:2404
#: modules/js_composer/config/map.php:2489
#: modules/js_composer/config/map.php:2858
#: modules/js_composer/config/map.php:3229
#: modules/js_composer/config/map.php:6616
#: modules/js_composer/config/map.php:6679
#: modules/js_composer/config/map.php:7137
#: modules/js_composer/config/map.php:7299
#: modules/js_composer/config/map.php:7335
#: modules/js_composer/config/map.php:7507
#: modules/js_composer/config/map.php:7713
msgid ""
"Designates the ascending or descending order of the 'orderby' parameter. "
"Defaults to 'ASC'."
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:223
#: modules/elementor/widgets/blog-posts-block.php:147
#: modules/elementor/widgets/brands-block.php:149
#: modules/elementor/widgets/brands-with-category-block.php:150
#: modules/elementor/widgets/brands-with-category-block.php:208
#: modules/elementor/widgets/product-categories-block-v2.php:140
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:165
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:240
#: modules/elementor/widgets/product-category-tags.php:141
#: modules/elementor/widgets/products-categories-1-6.php:117
#: modules/elementor/widgets/sidebar-with-products.php:200
#: modules/js_composer/config/map.php:2408
#: modules/js_composer/config/map.php:7141
#: modules/js_composer/config/map.php:7303
#: modules/js_composer/config/map.php:7339
#: modules/js_composer/config/map.php:7511
#: modules/js_composer/config/map.php:7641
#: modules/js_composer/config/map.php:7717
msgid "DESC"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:224
#: modules/elementor/widgets/blog-posts-block.php:148
#: modules/elementor/widgets/brands-block.php:150
#: modules/elementor/widgets/brands-with-category-block.php:151
#: modules/elementor/widgets/brands-with-category-block.php:209
#: modules/elementor/widgets/product-categories-block-v2.php:141
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:166
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:241
#: modules/elementor/widgets/product-category-tags.php:142
#: modules/elementor/widgets/products-categories-1-6.php:118
#: modules/elementor/widgets/sidebar-with-products.php:201
#: modules/js_composer/config/map.php:2407
#: modules/js_composer/config/map.php:7140
#: modules/js_composer/config/map.php:7302
#: modules/js_composer/config/map.php:7338
#: modules/js_composer/config/map.php:7510
#: modules/js_composer/config/map.php:7640
#: modules/js_composer/config/map.php:7716
msgid "ASC"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:233
#: modules/elementor/widgets/deal-and-product-tabs.php:275
#: modules/elementor/widgets/deal-products-block.php:183
#: modules/elementor/widgets/deals-products-carousel.php:212
#: modules/elementor/widgets/mobile-product-deals-block.php:200
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:170
#: modules/elementor/widgets/products-2-1-2-block.php:167
#: modules/elementor/widgets/products-6-1-block.php:167
#: modules/elementor/widgets/products-6-1-with-categories.php:167
#: modules/elementor/widgets/products-cards-carousel.php:232
#: modules/elementor/widgets/products-carousel-1.php:193
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:161
#: modules/elementor/widgets/products-carousel-category-with-image.php:186
#: modules/elementor/widgets/products-carousel-tabs-1.php:200
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:265
#: modules/elementor/widgets/products-carousel-tabs.php:190
#: modules/elementor/widgets/products-carousel-with-timer.php:226
#: modules/elementor/widgets/products-carousel.php:170
#: modules/elementor/widgets/products-category-with-image.php:194
#: modules/elementor/widgets/products-list-block.php:213
#: modules/elementor/widgets/products-one-two-block.php:163
#: modules/elementor/widgets/products-tabs-element.php:154
#: modules/elementor/widgets/products-with-category-image.php:194
#: modules/elementor/widgets/sidebar-with-products.php:238
#: modules/elementor/widgets/two-row-products.php:214
#: modules/js_composer/config/map.php:287
#: modules/js_composer/config/map.php:807
#: modules/js_composer/config/map.php:1139
#: modules/js_composer/config/map.php:1325
#: modules/js_composer/config/map.php:1501
#: modules/js_composer/config/map.php:1749
#: modules/js_composer/config/map.php:1991
#: modules/js_composer/config/map.php:2727
#: modules/js_composer/config/map.php:2975
#: modules/js_composer/config/map.php:3099
#: modules/js_composer/config/map.php:3341
#: modules/js_composer/config/map.php:3500
#: modules/js_composer/config/map.php:3659
#: modules/js_composer/config/map.php:3937
#: modules/js_composer/config/map.php:4401
#: modules/js_composer/config/map.php:4661
#: modules/js_composer/config/map.php:4871
#: modules/js_composer/config/map.php:5056
#: modules/js_composer/config/map.php:5302
#: modules/js_composer/config/map.php:5670
#: modules/js_composer/config/map.php:5942
#: modules/js_composer/config/map.php:6163
#: modules/js_composer/config/map.php:6529
#: modules/js_composer/config/map.php:7189
#: modules/js_composer/config/map.php:7517
msgid "Category"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:235
#: modules/elementor/widgets/deal-and-product-tabs.php:277
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:264
#: modules/elementor/widgets/products-6-1-block.php:169
#: modules/elementor/widgets/products-6-1-with-categories.php:169
#: modules/elementor/widgets/products-cards-carousel.php:234
#: modules/elementor/widgets/products-cards-carousel.php:314
#: modules/elementor/widgets/products-carousel-1.php:195
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:163
#: modules/elementor/widgets/products-carousel-category-with-image.php:188
#: modules/elementor/widgets/products-carousel-category-with-image.php:282
#: modules/elementor/widgets/products-carousel-tabs-1.php:202
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:267
#: modules/elementor/widgets/products-carousel-tabs.php:192
#: modules/elementor/widgets/products-carousel-with-timer.php:228
#: modules/elementor/widgets/products-carousel.php:172
#: modules/elementor/widgets/products-category-with-image.php:196
#: modules/elementor/widgets/products-category-with-image.php:290
#: modules/elementor/widgets/products-list-block.php:215
#: modules/elementor/widgets/products-one-two-block.php:165
#: modules/elementor/widgets/products-tabs-element.php:156
#: modules/elementor/widgets/products-with-category-image.php:196
#: modules/elementor/widgets/products-with-category-image.php:290
#: modules/elementor/widgets/products-with-category-image.php:353
#: modules/elementor/widgets/sidebar-with-products.php:240
#: modules/elementor/widgets/two-row-products.php:216
msgid "Enter slug separate by comma(,)."
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:245
#: modules/elementor/widgets/deal-and-product-tabs.php:284
#: modules/elementor/widgets/deals-products-carousel.php:221
#: modules/elementor/widgets/mobile-product-deals-block.php:209
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:179
#: modules/elementor/widgets/products-2-1-2-block.php:176
#: modules/elementor/widgets/products-6-1-block.php:176
#: modules/elementor/widgets/products-6-1-with-categories.php:176
#: modules/elementor/widgets/products-cards-carousel.php:241
#: modules/elementor/widgets/products-carousel-1.php:202
#: modules/elementor/widgets/products-carousel-category-with-image.php:195
#: modules/elementor/widgets/products-carousel-tabs-1.php:209
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:277
#: modules/elementor/widgets/products-carousel-tabs.php:199
#: modules/elementor/widgets/products-carousel-with-timer.php:235
#: modules/elementor/widgets/products-category-with-image.php:203
#: modules/elementor/widgets/products-list-block.php:222
#: modules/elementor/widgets/products-with-category-image.php:203
#: modules/elementor/widgets/sidebar-with-products.php:250
#: modules/elementor/widgets/two-row-products.php:223
#: modules/js_composer/config/map.php:294
#: modules/js_composer/config/map.php:814
#: modules/js_composer/config/map.php:1146
#: modules/js_composer/config/map.php:1332
#: modules/js_composer/config/map.php:1508
#: modules/js_composer/config/map.php:1756
#: modules/js_composer/config/map.php:1998
#: modules/js_composer/config/map.php:2734
#: modules/js_composer/config/map.php:2982
#: modules/js_composer/config/map.php:3106
#: modules/js_composer/config/map.php:3348
#: modules/js_composer/config/map.php:3507
#: modules/js_composer/config/map.php:3666
#: modules/js_composer/config/map.php:3944
#: modules/js_composer/config/map.php:4408
#: modules/js_composer/config/map.php:4668
#: modules/js_composer/config/map.php:4878
#: modules/js_composer/config/map.php:5063
#: modules/js_composer/config/map.php:5309
#: modules/js_composer/config/map.php:5677
#: modules/js_composer/config/map.php:5949
#: modules/js_composer/config/map.php:6170
#: modules/js_composer/config/map.php:6536
#: modules/js_composer/config/map.php:7196
#: modules/js_composer/config/map.php:7524
msgid "Category Operator"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:247
#: modules/elementor/widgets/banner-with-products-carousel.php:284
#: modules/elementor/widgets/deal-and-product-tabs.php:286
#: modules/elementor/widgets/deal-products-block.php:194
#: modules/elementor/widgets/deals-products-carousel.php:223
#: modules/elementor/widgets/mobile-product-deals-block.php:211
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:181
#: modules/elementor/widgets/products-cards-carousel.php:243
#: modules/elementor/widgets/products-carousel-1.php:204
#: modules/elementor/widgets/products-carousel-category-with-image.php:197
#: modules/elementor/widgets/products-carousel-tabs-1.php:211
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:279
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:316
#: modules/elementor/widgets/products-carousel-tabs.php:201
#: modules/elementor/widgets/products-carousel-with-timer.php:237
#: modules/elementor/widgets/products-category-with-image.php:205
#: modules/elementor/widgets/products-list-block.php:224
#: modules/elementor/widgets/products-one-two-block.php:174
#: modules/elementor/widgets/products-with-category-image.php:205
#: modules/elementor/widgets/sidebar-with-products.php:252
#: modules/elementor/widgets/sidebar-with-products.php:289
#: modules/elementor/widgets/two-row-products.php:225
#: modules/js_composer/config/map.php:296
#: modules/js_composer/config/map.php:816
#: modules/js_composer/config/map.php:1148
#: modules/js_composer/config/map.php:1334
#: modules/js_composer/config/map.php:1510
#: modules/js_composer/config/map.php:1758
#: modules/js_composer/config/map.php:2000
#: modules/js_composer/config/map.php:2736
#: modules/js_composer/config/map.php:2984
#: modules/js_composer/config/map.php:3108
#: modules/js_composer/config/map.php:3350
#: modules/js_composer/config/map.php:3509
#: modules/js_composer/config/map.php:3668
#: modules/js_composer/config/map.php:3946
#: modules/js_composer/config/map.php:4410
#: modules/js_composer/config/map.php:4670
#: modules/js_composer/config/map.php:4880
#: modules/js_composer/config/map.php:5065
#: modules/js_composer/config/map.php:5311
#: modules/js_composer/config/map.php:5679
#: modules/js_composer/config/map.php:5951
#: modules/js_composer/config/map.php:6172
#: modules/js_composer/config/map.php:6538
#: modules/js_composer/config/map.php:7198
#: modules/js_composer/config/map.php:7526
msgid "Operator to compare categories. Possible values are 'IN', 'NOT IN', 'AND'."
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:258
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:290
#: modules/elementor/widgets/sidebar-with-products.php:263
#: modules/js_composer/config/map.php:302
#: modules/js_composer/config/map.php:822
#: modules/js_composer/config/map.php:1154
#: modules/js_composer/config/map.php:1340
#: modules/js_composer/config/map.php:1516
#: modules/js_composer/config/map.php:1764
#: modules/js_composer/config/map.php:2006
#: modules/js_composer/config/map.php:2742
#: modules/js_composer/config/map.php:2990
#: modules/js_composer/config/map.php:3114
#: modules/js_composer/config/map.php:3356
#: modules/js_composer/config/map.php:3515
#: modules/js_composer/config/map.php:3674
#: modules/js_composer/config/map.php:3952
#: modules/js_composer/config/map.php:4416
#: modules/js_composer/config/map.php:4676
#: modules/js_composer/config/map.php:4886
#: modules/js_composer/config/map.php:5071
#: modules/js_composer/config/map.php:5317
#: modules/js_composer/config/map.php:5685
#: modules/js_composer/config/map.php:5957
#: modules/js_composer/config/map.php:6178
#: modules/js_composer/config/map.php:6544
#: modules/js_composer/config/map.php:7204
#: modules/js_composer/config/map.php:7532
msgid "Attribute"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:260
#: modules/elementor/widgets/banner-with-products-carousel.php:272
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:292
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:304
#: modules/elementor/widgets/sidebar-with-products.php:265
#: modules/elementor/widgets/sidebar-with-products.php:277
#: modules/js_composer/config/map.php:304
#: modules/js_composer/config/map.php:824
#: modules/js_composer/config/map.php:1156
#: modules/js_composer/config/map.php:1342
#: modules/js_composer/config/map.php:1518
#: modules/js_composer/config/map.php:1766
#: modules/js_composer/config/map.php:2008
#: modules/js_composer/config/map.php:2744
#: modules/js_composer/config/map.php:2992
#: modules/js_composer/config/map.php:3116
#: modules/js_composer/config/map.php:3358
#: modules/js_composer/config/map.php:3517
#: modules/js_composer/config/map.php:3676
#: modules/js_composer/config/map.php:3954
#: modules/js_composer/config/map.php:4418
#: modules/js_composer/config/map.php:4678
#: modules/js_composer/config/map.php:4888
#: modules/js_composer/config/map.php:5073
#: modules/js_composer/config/map.php:5319
#: modules/js_composer/config/map.php:5687
#: modules/js_composer/config/map.php:5959
#: modules/js_composer/config/map.php:6180
#: modules/js_composer/config/map.php:6546
#: modules/js_composer/config/map.php:7206
#: modules/js_composer/config/map.php:7534
msgid "Enter single attribute slug."
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:270
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:302
#: modules/elementor/widgets/sidebar-with-products.php:275
#: modules/js_composer/config/map.php:309
#: modules/js_composer/config/map.php:829
#: modules/js_composer/config/map.php:1161
#: modules/js_composer/config/map.php:1347
#: modules/js_composer/config/map.php:1523
#: modules/js_composer/config/map.php:1771
#: modules/js_composer/config/map.php:2013
#: modules/js_composer/config/map.php:2749
#: modules/js_composer/config/map.php:2997
#: modules/js_composer/config/map.php:3121
#: modules/js_composer/config/map.php:3363
#: modules/js_composer/config/map.php:3522
#: modules/js_composer/config/map.php:3681
#: modules/js_composer/config/map.php:3959
#: modules/js_composer/config/map.php:4423
#: modules/js_composer/config/map.php:4683
#: modules/js_composer/config/map.php:4893
#: modules/js_composer/config/map.php:5078
#: modules/js_composer/config/map.php:5324
#: modules/js_composer/config/map.php:5692
#: modules/js_composer/config/map.php:5964
#: modules/js_composer/config/map.php:6185
#: modules/js_composer/config/map.php:6551
#: modules/js_composer/config/map.php:7211
#: modules/js_composer/config/map.php:7539
msgid "Terms"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:282
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:314
#: modules/elementor/widgets/sidebar-with-products.php:287
#: modules/js_composer/config/map.php:316
#: modules/js_composer/config/map.php:836
#: modules/js_composer/config/map.php:1168
#: modules/js_composer/config/map.php:1354
#: modules/js_composer/config/map.php:1530
#: modules/js_composer/config/map.php:1778
#: modules/js_composer/config/map.php:2020
#: modules/js_composer/config/map.php:2756
#: modules/js_composer/config/map.php:3004
#: modules/js_composer/config/map.php:3128
#: modules/js_composer/config/map.php:3370
#: modules/js_composer/config/map.php:3529
#: modules/js_composer/config/map.php:3688
#: modules/js_composer/config/map.php:3966
#: modules/js_composer/config/map.php:4430
#: modules/js_composer/config/map.php:4690
#: modules/js_composer/config/map.php:4900
#: modules/js_composer/config/map.php:5085
#: modules/js_composer/config/map.php:5331
#: modules/js_composer/config/map.php:5699
#: modules/js_composer/config/map.php:5971
#: modules/js_composer/config/map.php:6192
#: modules/js_composer/config/map.php:6558
#: modules/js_composer/config/map.php:7218
#: modules/js_composer/config/map.php:7546
msgid "Terms Operator"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:295
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:329
msgid "Enable Autoplay"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:307
msgid "Carousel Items"
msgstr ""

#: modules/elementor/widgets/banner-with-products-carousel.php:309
msgid "Enter carousel items limit"
msgstr ""

#: modules/elementor/widgets/blog-posts-block.php:43
#: modules/js_composer/config/map.php:7586
msgid "Blog Posts Block"
msgstr ""

#: modules/elementor/widgets/blog-posts-block.php:98
#: modules/js_composer/config/map.php:7599
#: modules/js_composer/includes/elements/vc-blog-posts-block.php:8
msgid "Tips & Inspirations"
msgstr ""

#: modules/elementor/widgets/blog-posts-block.php:105
#: modules/elementor/widgets/brands-block.php:107
#: modules/elementor/widgets/product-categories-block-v2.php:95
#: modules/elementor/widgets/product-categories-block.php:104
#: modules/elementor/widgets/product-categories-list-with-header.php:133
#: modules/elementor/widgets/product-categories-list.php:95
#: modules/js_composer/config/map.php:2286
#: modules/js_composer/config/map.php:2368
#: modules/js_composer/config/map.php:2824
#: modules/js_composer/config/map.php:3195
#: modules/js_composer/config/map.php:7604
#: modules/js_composer/config/map.php:7680
msgid "Enter Columns"
msgstr ""

#: modules/elementor/widgets/blog-posts-block.php:107
#: modules/elementor/widgets/brands-block.php:109
msgid "Number of columns"
msgstr ""

#: modules/elementor/widgets/blog-posts-block.php:115
#: modules/js_composer/config/map.php:7612
msgid "Number of Posts to display"
msgstr ""

#: modules/elementor/widgets/blog-posts-block.php:124
#: modules/elementor/widgets/brands-block.php:126
#: modules/elementor/widgets/brands-carousel.php:147
#: modules/elementor/widgets/brands-with-category-block.php:231
#: modules/elementor/widgets/category-icons-carousel.php:145
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:252
#: modules/elementor/widgets/product-categories-block-v2.php:158
#: modules/elementor/widgets/product-categories-block.php:163
#: modules/elementor/widgets/product-categories-list-with-header.php:191
#: modules/elementor/widgets/product-categories-list.php:155
#: modules/elementor/widgets/product-categories-menu-list.php:167
#: modules/elementor/widgets/product-category-tags.php:160
#: modules/elementor/widgets/product-list-categories.php:155
#: modules/elementor/widgets/products-2-1-2-block.php:226
#: modules/elementor/widgets/products-6-1-block.php:225
#: modules/elementor/widgets/products-6-1-with-categories.php:286
#: modules/elementor/widgets/products-6-1-with-categories.php:348
#: modules/elementor/widgets/products-cards-carousel.php:303
#: modules/elementor/widgets/products-carousel-category-with-image.php:270
#: modules/elementor/widgets/products-categories-1-6.php:136
#: modules/elementor/widgets/products-category-with-image.php:278
#: modules/elementor/widgets/products-list-block.php:294
#: modules/elementor/widgets/products-with-category-image.php:278
#: modules/elementor/widgets/products-with-category-image.php:341
#: modules/js_composer/config/map.php:1404
#: modules/js_composer/config/map.php:2142
#: modules/js_composer/config/map.php:2256
#: modules/js_composer/config/map.php:2333
#: modules/js_composer/config/map.php:2421
#: modules/js_composer/config/map.php:2502
#: modules/js_composer/config/map.php:2871
#: modules/js_composer/config/map.php:3242
#: modules/js_composer/config/map.php:3411
#: modules/js_composer/config/map.php:3570
#: modules/js_composer/config/map.php:3780
#: modules/js_composer/config/map.php:3827
#: modules/js_composer/config/map.php:4024
#: modules/js_composer/config/map.php:4071
#: modules/js_composer/config/map.php:4488
#: modules/js_composer/config/map.php:4748
#: modules/js_composer/config/map.php:5582
#: modules/js_composer/config/map.php:5843
#: modules/js_composer/config/map.php:6629
#: modules/js_composer/config/map.php:6693
#: modules/js_composer/config/map.php:7620
#: modules/js_composer/config/map.php:7696
msgid "Include ID's"
msgstr ""

#: modules/elementor/widgets/blog-posts-block.php:145
#: modules/elementor/widgets/brands-carousel.php:139
#: modules/elementor/widgets/deal-products-block.php:133
#: modules/elementor/widgets/deals-products-carousel.php:186
#: modules/elementor/widgets/mobile-product-deals-block.php:173
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:144
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:243
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:238
#: modules/elementor/widgets/products-2-1-2-block.php:134
#: modules/elementor/widgets/products-2-1-2-block.php:209
#: modules/elementor/widgets/products-6-1-block.php:134
#: modules/elementor/widgets/products-6-1-block.php:208
#: modules/elementor/widgets/products-6-1-with-categories.php:134
#: modules/elementor/widgets/products-6-1-with-categories.php:269
#: modules/elementor/widgets/products-6-1-with-categories.php:340
#: modules/elementor/widgets/products-cards-carousel.php:199
#: modules/elementor/widgets/products-carousel-1.php:160
#: modules/elementor/widgets/products-carousel-category-with-image.php:156
#: modules/elementor/widgets/products-carousel-category-with-image.php:261
#: modules/elementor/widgets/products-carousel-with-timer.php:194
#: modules/elementor/widgets/products-categories-1-6.php:115
#: modules/elementor/widgets/products-category-with-image.php:164
#: modules/elementor/widgets/products-category-with-image.php:269
#: modules/elementor/widgets/products-list-block.php:162
#: modules/elementor/widgets/products-list-block.php:286
#: modules/elementor/widgets/products-one-two-block.php:123
#: modules/elementor/widgets/products-one-two-block.php:133
#: modules/elementor/widgets/products-with-category-image.php:164
#: modules/elementor/widgets/products-with-category-image.php:269
#: modules/elementor/widgets/products-with-category-image.php:332
#: modules/elementor/widgets/two-row-products.php:181
#: modules/js_composer/config/map.php:263
#: modules/js_composer/config/map.php:1301
#: modules/js_composer/config/map.php:1398
#: modules/js_composer/config/map.php:1477
#: modules/js_composer/config/map.php:1725
#: modules/js_composer/config/map.php:1967
#: modules/js_composer/config/map.php:2136
#: modules/js_composer/config/map.php:2935
#: modules/js_composer/config/map.php:3075
#: modules/js_composer/config/map.php:3317
#: modules/js_composer/config/map.php:3405
#: modules/js_composer/config/map.php:3476
#: modules/js_composer/config/map.php:3564
#: modules/js_composer/config/map.php:3635
#: modules/js_composer/config/map.php:3774
#: modules/js_composer/config/map.php:3821
#: modules/js_composer/config/map.php:3913
#: modules/js_composer/config/map.php:4018
#: modules/js_composer/config/map.php:4065
#: modules/js_composer/config/map.php:4369
#: modules/js_composer/config/map.php:4482
#: modules/js_composer/config/map.php:4629
#: modules/js_composer/config/map.php:4742
#: modules/js_composer/config/map.php:5032
#: modules/js_composer/config/map.php:5395
#: modules/js_composer/config/map.php:5576
#: modules/js_composer/config/map.php:5646
#: modules/js_composer/config/map.php:5837
#: modules/js_composer/config/map.php:5897
#: modules/js_composer/config/map.php:6116
#: modules/js_composer/config/map.php:6505
#: modules/js_composer/config/map.php:6872
#: modules/js_composer/config/map.php:6928
#: modules/js_composer/config/map.php:7637
msgid ""
"Designates the ascending or descending order of the 'orderby' parameter. "
"Defaults to 'DESC'."
msgstr ""

#: modules/elementor/widgets/brands-block.php:45
#: modules/js_composer/config/map.php:7662
msgid "Brands Block"
msgstr ""

#: modules/elementor/widgets/brands-block.php:100
#: modules/js_composer/config/map.php:7675
#: modules/js_composer/includes/elements/vc-brands-block.php:8
msgid "Known Brands"
msgstr ""

#: modules/elementor/widgets/brands-block.php:117
#: modules/elementor/widgets/brands-carousel.php:104
#: modules/js_composer/config/map.php:2109
#: modules/js_composer/config/map.php:7688
msgid "Number of Brands to display"
msgstr ""

#: modules/elementor/widgets/brands-block.php:137
#: modules/elementor/widgets/brands-with-category-block.php:138
#: modules/elementor/widgets/brands-with-category-block.php:196
#: modules/elementor/widgets/product-categories-block-v2.php:128
#: modules/elementor/widgets/product-categories-block.php:137
#: modules/elementor/widgets/product-categories-list-with-header.php:165
#: modules/elementor/widgets/product-categories-list.php:129
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:154
#: modules/elementor/widgets/product-category-tags.php:129
#: modules/elementor/widgets/product-list-categories.php:128
#: modules/js_composer/config/map.php:2235
#: modules/js_composer/config/map.php:2312
#: modules/js_composer/config/map.php:2481
#: modules/js_composer/config/map.php:2850
#: modules/js_composer/config/map.php:3221
#: modules/js_composer/config/map.php:6608
#: modules/js_composer/config/map.php:6671
#: modules/js_composer/config/map.php:7291
#: modules/js_composer/config/map.php:7629
#: modules/js_composer/config/map.php:7705
msgid ""
" Sort retrieved posts by parameter. Defaults to 'name'. One or more options "
"can be passed"
msgstr ""

#: modules/elementor/widgets/brands-block.php:159
msgid "Hide Empty Brands"
msgstr ""

#: modules/elementor/widgets/brands-block.php:161
#: modules/elementor/widgets/brands-with-category-block.php:163
#: modules/elementor/widgets/brands-with-category-block.php:221
#: modules/elementor/widgets/product-categories-block-v2.php:115
#: modules/elementor/widgets/product-category-tags.php:117
#: modules/elementor/widgets/products-categories-1-6.php:83
msgid "Hidden"
msgstr ""

#: modules/elementor/widgets/brands-block.php:162
#: modules/elementor/widgets/brands-carousel.php:115
#: modules/elementor/widgets/brands-carousel.php:157
#: modules/elementor/widgets/brands-with-category-block.php:164
#: modules/elementor/widgets/brands-with-category-block.php:222
#: modules/elementor/widgets/product-categories-block-v2.php:116
#: modules/elementor/widgets/product-category-tags.php:118
#: modules/elementor/widgets/products-categories-1-6.php:84
msgid "Hide"
msgstr ""

#: modules/elementor/widgets/brands-block.php:165
msgid "Hide Brands does not have products"
msgstr ""

#: modules/elementor/widgets/brands-carousel.php:43
msgid "Brands Carousel"
msgstr ""

#: modules/elementor/widgets/brands-carousel.php:97
msgid " Enter title"
msgstr ""

#: modules/elementor/widgets/brands-carousel.php:113
#: modules/elementor/widgets/category-icons-carousel.php:105
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:218
#: modules/elementor/widgets/product-categories-block.php:122
#: modules/elementor/widgets/product-categories-list-with-header.php:150
#: modules/elementor/widgets/product-categories-list.php:114
#: modules/elementor/widgets/product-categories-menu-list.php:125
#: modules/elementor/widgets/product-list-categories.php:113
#: modules/elementor/widgets/products-2-1-2-block.php:194
#: modules/elementor/widgets/products-6-1-block.php:193
#: modules/elementor/widgets/products-6-1-with-categories.php:254
#: modules/elementor/widgets/products-6-1-with-categories.php:314
#: modules/elementor/widgets/products-cards-carousel.php:271
#: modules/elementor/widgets/products-carousel-category-with-image.php:236
#: modules/elementor/widgets/products-category-with-image.php:244
#: modules/elementor/widgets/products-list-block.php:261
#: modules/elementor/widgets/products-with-category-image.php:244
#: modules/elementor/widgets/products-with-category-image.php:308
#: modules/js_composer/config/map.php:1379
#: modules/js_composer/config/map.php:2117
#: modules/js_composer/config/map.php:2224
#: modules/js_composer/config/map.php:2301
#: modules/js_composer/config/map.php:2470
#: modules/js_composer/config/map.php:2839
#: modules/js_composer/config/map.php:3210
#: modules/js_composer/config/map.php:3386
#: modules/js_composer/config/map.php:3545
#: modules/js_composer/config/map.php:3755
#: modules/js_composer/config/map.php:3802
#: modules/js_composer/config/map.php:3999
#: modules/js_composer/config/map.php:4046
#: modules/js_composer/config/map.php:4463
#: modules/js_composer/config/map.php:4723
#: modules/js_composer/config/map.php:5409
#: modules/js_composer/config/map.php:5557
#: modules/js_composer/config/map.php:5818
#: modules/js_composer/config/map.php:6597
#: modules/js_composer/config/map.php:6660
msgid "Have no products"
msgstr ""

#: modules/elementor/widgets/brands-carousel.php:116
#: modules/elementor/widgets/brands-carousel.php:158
msgid "Show"
msgstr ""

#: modules/elementor/widgets/brands-carousel.php:119
#: modules/js_composer/config/map.php:2118
msgid "Show Brands does not have products"
msgstr ""

#: modules/elementor/widgets/brands-carousel.php:126
#: modules/elementor/widgets/deal-and-product-tabs.php:233
#: modules/elementor/widgets/deal-products-block.php:121
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:231
#: modules/elementor/widgets/product-categories-block.php:135
#: modules/elementor/widgets/product-categories-list-with-header.php:163
#: modules/elementor/widgets/product-categories-list.php:127
#: modules/elementor/widgets/product-categories-menu-list.php:138
#: modules/elementor/widgets/product-list-categories.php:126
#: modules/elementor/widgets/products-2-1-2-block.php:122
#: modules/elementor/widgets/products-2-1-2-block.php:207
#: modules/elementor/widgets/products-6-1-block.php:122
#: modules/elementor/widgets/products-6-1-block.php:206
#: modules/elementor/widgets/products-6-1-with-categories.php:122
#: modules/elementor/widgets/products-6-1-with-categories.php:267
#: modules/elementor/widgets/products-6-1-with-categories.php:328
#: modules/elementor/widgets/products-cards-carousel.php:187
#: modules/elementor/widgets/products-cards-carousel.php:284
#: modules/elementor/widgets/products-carousel-category-with-image.php:249
#: modules/elementor/widgets/products-category-with-image.php:257
#: modules/elementor/widgets/products-list-block.php:274
#: modules/elementor/widgets/products-one-two-block.php:121
#: modules/elementor/widgets/products-with-category-image.php:257
#: modules/elementor/widgets/products-with-category-image.php:320
#: modules/js_composer/config/map.php:253
#: modules/js_composer/config/map.php:1291
#: modules/js_composer/config/map.php:1388
#: modules/js_composer/config/map.php:1467
#: modules/js_composer/config/map.php:1715
#: modules/js_composer/config/map.php:1957
#: modules/js_composer/config/map.php:2126
#: modules/js_composer/config/map.php:2233
#: modules/js_composer/config/map.php:2310
#: modules/js_composer/config/map.php:2394
#: modules/js_composer/config/map.php:2479
#: modules/js_composer/config/map.php:2848
#: modules/js_composer/config/map.php:2925
#: modules/js_composer/config/map.php:3065
#: modules/js_composer/config/map.php:3219
#: modules/js_composer/config/map.php:3307
#: modules/js_composer/config/map.php:3395
#: modules/js_composer/config/map.php:3466
#: modules/js_composer/config/map.php:3554
#: modules/js_composer/config/map.php:3625
#: modules/js_composer/config/map.php:3764
#: modules/js_composer/config/map.php:3811
#: modules/js_composer/config/map.php:3903
#: modules/js_composer/config/map.php:4008
#: modules/js_composer/config/map.php:4055
#: modules/js_composer/config/map.php:4359
#: modules/js_composer/config/map.php:4472
#: modules/js_composer/config/map.php:4619
#: modules/js_composer/config/map.php:4732
#: modules/js_composer/config/map.php:5022
#: modules/js_composer/config/map.php:5385
#: modules/js_composer/config/map.php:5566
#: modules/js_composer/config/map.php:5636
#: modules/js_composer/config/map.php:5827
#: modules/js_composer/config/map.php:5887
#: modules/js_composer/config/map.php:6106
#: modules/js_composer/config/map.php:6495
#: modules/js_composer/config/map.php:6606
#: modules/js_composer/config/map.php:6669
#: modules/js_composer/config/map.php:7127
#: modules/js_composer/config/map.php:7497
#: modules/js_composer/config/map.php:7627
#: modules/js_composer/config/map.php:7703
msgid "Order by"
msgstr ""

#: modules/elementor/widgets/brands-carousel.php:155
#: modules/elementor/widgets/category-icons-carousel.php:215
#: modules/elementor/widgets/deals-products-carousel.php:291
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:342
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:317
#: modules/elementor/widgets/product-onsale-carousel-2.php:153
#: modules/elementor/widgets/product-onsale-carousel.php:233
#: modules/elementor/widgets/products-carousel-1.php:295
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:325
#: modules/elementor/widgets/products-carousel-category-with-image.php:366
#: modules/elementor/widgets/products-carousel-with-timer.php:324
#: modules/elementor/widgets/products-carousel.php:279
#: modules/elementor/widgets/recently-viewed-products-carousel.php:182
#: modules/js_composer/config/map.php:385
#: modules/js_composer/config/map.php:1615
#: modules/js_composer/config/map.php:1847
#: modules/js_composer/config/map.php:2061
#: modules/js_composer/config/map.php:2150
#: modules/js_composer/config/map.php:2553
#: modules/js_composer/config/map.php:4280
#: modules/js_composer/config/map.php:4537
#: modules/js_composer/config/map.php:5119
#: modules/js_composer/config/map.php:5752
#: modules/js_composer/config/map.php:6028
#: modules/js_composer/config/map.php:6768
#: modules/js_composer/config/map.php:6981
msgid "Carousel: Enable Touch Drag"
msgstr ""

#: modules/elementor/widgets/brands-carousel.php:167
#: modules/elementor/widgets/category-icons-carousel.php:239
#: modules/elementor/widgets/deals-products-carousel.php:312
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:354
#: modules/elementor/widgets/product-onsale-carousel-2.php:165
#: modules/elementor/widgets/product-onsale-carousel.php:271
#: modules/elementor/widgets/products-cards-carousel.php:251
#: modules/elementor/widgets/products-carousel-1.php:307
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:337
#: modules/elementor/widgets/products-carousel-category-with-image.php:378
#: modules/elementor/widgets/products-carousel-tabs-1.php:317
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:377
#: modules/elementor/widgets/products-carousel-tabs.php:288
#: modules/elementor/widgets/products-carousel-with-timer.php:336
#: modules/elementor/widgets/recently-viewed-products-carousel.php:194
#: modules/js_composer/config/map.php:394
#: modules/js_composer/config/map.php:1036
#: modules/js_composer/config/map.php:1186
#: modules/js_composer/config/map.php:1363
#: modules/js_composer/config/map.php:1645
#: modules/js_composer/config/map.php:1856
#: modules/js_composer/config/map.php:2070
#: modules/js_composer/config/map.php:2159
#: modules/js_composer/config/map.php:2562
#: modules/js_composer/config/map.php:2800
#: modules/js_composer/config/map.php:4310
#: modules/js_composer/config/map.php:4546
#: modules/js_composer/config/map.php:4936
#: modules/js_composer/config/map.php:5149
#: modules/js_composer/config/map.php:5761
#: modules/js_composer/config/map.php:6037
#: modules/js_composer/config/map.php:6777
#: modules/js_composer/config/map.php:6990
#: modules/js_composer/config/map.php:7555
msgid "Carousel: Autoplay"
msgstr ""

#: modules/elementor/widgets/brands-carousel.php:179
#: modules/js_composer/config/map.php:2185
msgid "Carousel: Loop"
msgstr ""

#: modules/elementor/widgets/brands-with-category-block.php:45
#: modules/js_composer/config/map.php:7250
msgid "Brands With Category Block"
msgstr ""

#: modules/elementor/widgets/brands-with-category-block.php:89
msgid "Brands"
msgstr ""

#: modules/elementor/widgets/brands-with-category-block.php:109
#: modules/elementor/widgets/brands-with-category-block.php:251
msgid "Enter Action Text"
msgstr ""

#: modules/elementor/widgets/brands-with-category-block.php:119
msgid "Enter Action Link"
msgstr ""

#: modules/elementor/widgets/brands-with-category-block.php:160
msgid "Hide Empty Brands "
msgstr ""

#: modules/elementor/widgets/brands-with-category-block.php:162
msgid "Hide Brands does not have products "
msgstr ""

#: modules/elementor/widgets/brands-with-category-block.php:175
msgid "Categories"
msgstr ""

#: modules/elementor/widgets/brands-with-category-block.php:183
#: modules/js_composer/config/map.php:7325
msgid "Category Child Limit"
msgstr ""

#: modules/elementor/widgets/brands-with-category-block.php:185
msgid "Enter limit in numbers of child category"
msgstr ""

#: modules/elementor/widgets/brands-with-category-block.php:186
msgid "Enter category child limit"
msgstr ""

#: modules/elementor/widgets/brands-with-category-block.php:218
msgid "Hide Empty Category "
msgstr ""

#: modules/elementor/widgets/brands-with-category-block.php:220
msgid "Hide categories does not have products "
msgstr ""

#: modules/elementor/widgets/brands-with-category-block.php:233
#: modules/elementor/widgets/products-carousel-category-with-image.php:272
#: modules/elementor/widgets/products-category-with-image.php:280
#: modules/elementor/widgets/products-with-category-image.php:280
#: modules/elementor/widgets/products-with-category-image.php:343
msgid "Enter id separate by comma(,)."
msgstr ""

#: modules/elementor/widgets/brands-with-category-block.php:240
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:210
#: modules/elementor/widgets/product-categories-block-v2.php:104
#: modules/elementor/widgets/product-categories-block.php:113
#: modules/elementor/widgets/product-categories-list-with-header.php:141
#: modules/elementor/widgets/product-categories-list.php:104
#: modules/elementor/widgets/product-categories-menu-list.php:116
#: modules/elementor/widgets/product-list-categories.php:104
#: modules/elementor/widgets/products-2-1-2-block.php:185
#: modules/elementor/widgets/products-6-1-block.php:184
#: modules/elementor/widgets/products-6-1-with-categories.php:245
#: modules/elementor/widgets/products-cards-carousel.php:263
#: modules/elementor/widgets/products-carousel-category-with-image.php:227
#: modules/elementor/widgets/products-category-with-image.php:235
#: modules/elementor/widgets/products-with-category-image.php:235
#: modules/js_composer/config/map.php:1371
#: modules/js_composer/config/map.php:2216
#: modules/js_composer/config/map.php:2293
#: modules/js_composer/config/map.php:2376
#: modules/js_composer/config/map.php:2462
#: modules/js_composer/config/map.php:2831
#: modules/js_composer/config/map.php:3202
#: modules/js_composer/config/map.php:3378
#: modules/js_composer/config/map.php:3537
#: modules/js_composer/config/map.php:3747
#: modules/js_composer/config/map.php:3991
#: modules/js_composer/config/map.php:4455
#: modules/js_composer/config/map.php:4715
#: modules/js_composer/config/map.php:5549
#: modules/js_composer/config/map.php:5810
#: modules/js_composer/config/map.php:6589
msgid "Number of Categories to display"
msgstr ""

#: modules/elementor/widgets/brands-with-category-block.php:259
msgid "Icon class"
msgstr ""

#: modules/elementor/widgets/brands-with-category-block.php:261
msgid "Enter icon class"
msgstr ""

#: modules/elementor/widgets/category-icons-carousel.php:43
msgid "Category Icon Carousel"
msgstr ""

#: modules/elementor/widgets/category-icons-carousel.php:98
msgid "Enter Number of Categories to display"
msgstr ""

#: modules/elementor/widgets/category-icons-carousel.php:117
#: modules/elementor/widgets/products-carousel-tabs-1.php:158
#: modules/elementor/widgets/products-carousel-tabs.php:148
#: modules/elementor/widgets/products-tabs-element.php:163
#: modules/js_composer/config/map.php:773
#: modules/js_composer/config/map.php:1105
#: modules/js_composer/config/map.php:2693
#: modules/js_composer/config/map.php:4837
#: modules/js_composer/config/map.php:5268
#: modules/js_composer/config/map.php:6336
msgid "Order By"
msgstr ""

#: modules/elementor/widgets/category-icons-carousel.php:135
msgid "Include Slugs"
msgstr ""

#: modules/elementor/widgets/category-icons-carousel.php:137
msgid "Enter the slug seperate by comma(,)"
msgstr ""

#: modules/elementor/widgets/category-icons-carousel.php:147
msgid "Enter the ID 's seperate by comma(,)"
msgstr ""

#: modules/elementor/widgets/category-icons-carousel.php:155
#: modules/elementor/widgets/deals-products-carousel.php:231
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:272
#: modules/elementor/widgets/products-carousel-1.php:212
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:255
#: modules/elementor/widgets/products-carousel-category-with-image.php:305
#: modules/elementor/widgets/products-carousel-tabs-1.php:229
#: modules/elementor/widgets/products-carousel-tabs.php:219
#: modules/elementor/widgets/products-carousel-with-timer.php:245
#: modules/elementor/widgets/products-carousel.php:204
#: modules/elementor/widgets/recently-viewed-products-carousel.php:112
#: modules/js_composer/config/map.php:324
#: modules/js_composer/config/map.php:1178
#: modules/js_composer/config/map.php:1554
#: modules/js_composer/config/map.php:1786
#: modules/js_composer/config/map.php:2028
#: modules/js_composer/config/map.php:2508
#: modules/js_composer/config/map.php:4504
#: modules/js_composer/config/map.php:4910
#: modules/js_composer/config/map.php:5093
#: modules/js_composer/config/map.php:5707
#: modules/js_composer/config/map.php:6011
#: modules/js_composer/config/map.php:7563
msgid "Carousel: Items"
msgstr ""

#: modules/elementor/widgets/category-icons-carousel.php:164
#: modules/elementor/widgets/deals-products-carousel.php:240
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:281
#: modules/elementor/widgets/products-carousel-1.php:221
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:265
#: modules/elementor/widgets/products-carousel-category-with-image.php:314
#: modules/elementor/widgets/products-carousel-tabs-1.php:238
#: modules/elementor/widgets/products-carousel-tabs.php:228
#: modules/elementor/widgets/products-carousel-with-timer.php:254
#: modules/elementor/widgets/products-carousel.php:214
#: modules/elementor/widgets/recently-viewed-products-carousel.php:122
#: modules/js_composer/config/map.php:331
#: modules/js_composer/config/map.php:1000
#: modules/js_composer/config/map.php:1561
#: modules/js_composer/config/map.php:2515
#: modules/js_composer/config/map.php:5714
msgid "Carousel: Items(0 - 479)"
msgstr ""

#: modules/elementor/widgets/category-icons-carousel.php:166
#: modules/elementor/widgets/category-icons-carousel.php:176
#: modules/elementor/widgets/category-icons-carousel.php:186
#: modules/elementor/widgets/category-icons-carousel.php:196
#: modules/elementor/widgets/deals-products-carousel.php:242
#: modules/elementor/widgets/deals-products-carousel.php:252
#: modules/elementor/widgets/deals-products-carousel.php:262
#: modules/elementor/widgets/deals-products-carousel.php:272
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:283
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:293
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:303
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:313
#: modules/elementor/widgets/products-carousel-1.php:223
#: modules/elementor/widgets/products-carousel-1.php:233
#: modules/elementor/widgets/products-carousel-1.php:243
#: modules/elementor/widgets/products-carousel-1.php:253
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:257
#: modules/elementor/widgets/products-carousel-category-with-image.php:316
#: modules/elementor/widgets/products-carousel-category-with-image.php:326
#: modules/elementor/widgets/products-carousel-category-with-image.php:336
#: modules/elementor/widgets/products-carousel-category-with-image.php:346
#: modules/elementor/widgets/products-carousel-tabs-1.php:240
#: modules/elementor/widgets/products-carousel-tabs-1.php:250
#: modules/elementor/widgets/products-carousel-tabs-1.php:260
#: modules/elementor/widgets/products-carousel-tabs-1.php:270
#: modules/elementor/widgets/products-carousel-tabs.php:230
#: modules/elementor/widgets/products-carousel-tabs.php:240
#: modules/elementor/widgets/products-carousel-tabs.php:250
#: modules/elementor/widgets/products-carousel-tabs.php:260
#: modules/elementor/widgets/products-carousel-tabs.php:269
#: modules/elementor/widgets/products-carousel-with-timer.php:256
#: modules/elementor/widgets/products-carousel-with-timer.php:265
#: modules/elementor/widgets/products-carousel-with-timer.php:274
#: modules/elementor/widgets/products-carousel-with-timer.php:283
#: modules/elementor/widgets/products-carousel.php:206
#: modules/elementor/widgets/products-carousel.php:216
#: modules/elementor/widgets/products-carousel.php:227
#: modules/elementor/widgets/products-carousel.php:238
#: modules/elementor/widgets/products-carousel.php:249
#: modules/elementor/widgets/products-carousel.php:259
#: modules/elementor/widgets/recently-viewed-products-carousel.php:114
msgid "Enter the number of items to display."
msgstr ""

#: modules/elementor/widgets/category-icons-carousel.php:174
#: modules/elementor/widgets/deals-products-carousel.php:250
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:291
#: modules/elementor/widgets/products-carousel-1.php:231
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:275
#: modules/elementor/widgets/products-carousel-category-with-image.php:324
#: modules/elementor/widgets/products-carousel-tabs-1.php:248
#: modules/elementor/widgets/products-carousel-tabs.php:238
#: modules/elementor/widgets/products-carousel-with-timer.php:263
#: modules/elementor/widgets/products-carousel.php:225
#: modules/elementor/widgets/recently-viewed-products-carousel.php:132
#: modules/js_composer/config/map.php:338
#: modules/js_composer/config/map.php:1007
#: modules/js_composer/config/map.php:1568
#: modules/js_composer/config/map.php:2522
#: modules/js_composer/config/map.php:5721
msgid "Carousel: Items(480 - 767)"
msgstr ""

#: modules/elementor/widgets/category-icons-carousel.php:184
#: modules/elementor/widgets/deals-products-carousel.php:260
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:301
#: modules/elementor/widgets/products-carousel-1.php:241
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:285
#: modules/elementor/widgets/products-carousel-category-with-image.php:334
#: modules/elementor/widgets/products-carousel-tabs-1.php:258
#: modules/elementor/widgets/products-carousel-tabs.php:248
#: modules/elementor/widgets/products-carousel-with-timer.php:272
#: modules/elementor/widgets/products-carousel.php:236
#: modules/elementor/widgets/recently-viewed-products-carousel.php:142
#: modules/js_composer/config/map.php:345
#: modules/js_composer/config/map.php:1014
#: modules/js_composer/config/map.php:1575
#: modules/js_composer/config/map.php:2529
#: modules/js_composer/config/map.php:5728
msgid "Carousel: Items(768 - 991)"
msgstr ""

#: modules/elementor/widgets/category-icons-carousel.php:194
#: modules/elementor/widgets/deals-products-carousel.php:270
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:311
#: modules/elementor/widgets/products-carousel-1.php:251
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:295
#: modules/elementor/widgets/products-carousel-category-with-image.php:344
#: modules/elementor/widgets/products-carousel-tabs-1.php:268
#: modules/elementor/widgets/products-carousel-tabs.php:258
#: modules/elementor/widgets/products-carousel-with-timer.php:281
#: modules/elementor/widgets/products-carousel.php:247
#: modules/elementor/widgets/recently-viewed-products-carousel.php:152
#: modules/js_composer/config/map.php:352
#: modules/js_composer/config/map.php:359
#: modules/js_composer/config/map.php:1021
#: modules/js_composer/config/map.php:1582
#: modules/js_composer/config/map.php:2536
#: modules/js_composer/config/map.php:5735
msgid "Carousel: Items(992 - 1199)"
msgstr ""

#: modules/elementor/widgets/category-icons-carousel.php:203
#: modules/elementor/widgets/deals-products-carousel.php:279
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:329
#: modules/elementor/widgets/product-onsale-carousel.php:221
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:313
#: modules/elementor/widgets/products-carousel-tabs-1.php:305
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:365
#: modules/elementor/widgets/products-carousel-tabs.php:276
#: modules/elementor/widgets/recently-viewed-products-carousel.php:170
#: modules/js_composer/config/map.php:376
#: modules/js_composer/config/map.php:1606
#: modules/js_composer/config/map.php:1838
#: modules/js_composer/config/map.php:2052
#: modules/js_composer/config/map.php:2544
#: modules/js_composer/config/map.php:2791
#: modules/js_composer/config/map.php:4271
#: modules/js_composer/config/map.php:4528
#: modules/js_composer/config/map.php:4927
#: modules/js_composer/config/map.php:5110
#: modules/js_composer/config/map.php:5743
#: modules/js_composer/config/map.php:6019
msgid "Carousel: Show Dots"
msgstr ""

#: modules/elementor/widgets/category-icons-carousel.php:227
msgid "Carousel: Enable Navigation"
msgstr ""

#: modules/elementor/widgets/category-icons-carousel.php:251
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:349
#: modules/elementor/widgets/recently-viewed-products-carousel.php:206
#: modules/js_composer/config/map.php:61
#: modules/js_composer/config/map.php:6305
msgid "Extra Class"
msgstr ""

#: modules/elementor/widgets/deal-and-product-tabs.php:43
msgid "Deals and Products Tabs"
msgstr ""

#: modules/elementor/widgets/deal-and-product-tabs.php:105
#: modules/elementor/widgets/product-onsale-carousel.php:105
#: modules/elementor/widgets/product-onsale.php:105
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:129
#: modules/js_composer/config/map.php:2620
#: modules/js_composer/config/map.php:4121
#: modules/js_composer/config/map.php:4184
#: modules/js_composer/config/map.php:5180
msgid "Show Savings Details"
msgstr ""

#: modules/elementor/widgets/deal-and-product-tabs.php:111
msgid "Deals savings text"
msgstr ""

#: modules/elementor/widgets/deal-and-product-tabs.php:118
#: modules/elementor/widgets/product-onsale-carousel.php:117
#: modules/elementor/widgets/product-onsale.php:117
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:141
#: modules/js_composer/config/map.php:2628
#: modules/js_composer/config/map.php:4128
#: modules/js_composer/config/map.php:4192
#: modules/js_composer/config/map.php:5187
msgid "Savings in"
msgstr ""

#: modules/elementor/widgets/deal-and-product-tabs.php:122
#: modules/elementor/widgets/product-onsale-carousel.php:121
#: modules/elementor/widgets/product-onsale.php:121
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:144
#: modules/js_composer/config/map.php:2630
#: modules/js_composer/config/map.php:4130
#: modules/js_composer/config/map.php:4194
#: modules/js_composer/config/map.php:5189
msgid "Amount"
msgstr ""

#: modules/elementor/widgets/deal-and-product-tabs.php:123
#: modules/elementor/widgets/product-onsale-carousel.php:122
#: modules/elementor/widgets/product-onsale.php:122
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:145
#: modules/js_composer/config/map.php:2631
#: modules/js_composer/config/map.php:4131
#: modules/js_composer/config/map.php:4195
#: modules/js_composer/config/map.php:5190
msgid "Percentage"
msgstr ""

#: modules/elementor/widgets/deal-and-product-tabs.php:132
#: modules/elementor/widgets/product-onsale-carousel.php:131
#: modules/elementor/widgets/product-onsale.php:131
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:154
#: modules/js_composer/config/map.php:2638
#: modules/js_composer/config/map.php:4137
#: modules/js_composer/config/map.php:4202
#: modules/js_composer/config/map.php:5196
msgid "Savings Text"
msgstr ""

#: modules/elementor/widgets/deal-and-product-tabs.php:144
#: modules/elementor/widgets/product-onsale-carousel-2.php:109
#: modules/elementor/widgets/product-onsale-carousel.php:152
#: modules/elementor/widgets/product-onsale.php:152
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:165
#: modules/js_composer/config/map.php:2647
#: modules/js_composer/config/map.php:4145
#: modules/js_composer/config/map.php:4218
#: modules/js_composer/config/map.php:5204
#: modules/js_composer/config/map.php:6733
msgid "Recent"
msgstr ""

#: modules/elementor/widgets/deal-and-product-tabs.php:145
#: modules/elementor/widgets/product-onsale-carousel-2.php:110
#: modules/elementor/widgets/product-onsale-carousel.php:153
#: modules/elementor/widgets/product-onsale.php:153
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:166
#: modules/js_composer/config/map.php:2648
#: modules/js_composer/config/map.php:4146
#: modules/js_composer/config/map.php:4219
#: modules/js_composer/config/map.php:5205
#: modules/js_composer/config/map.php:6734
msgid "Random"
msgstr ""

#: modules/elementor/widgets/deal-and-product-tabs.php:146
#: modules/elementor/widgets/product-onsale-carousel-2.php:111
#: modules/elementor/widgets/product-onsale-carousel.php:154
#: modules/elementor/widgets/product-onsale.php:154
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:167
#: modules/js_composer/config/map.php:2649
#: modules/js_composer/config/map.php:4147
#: modules/js_composer/config/map.php:4220
#: modules/js_composer/config/map.php:5206
#: modules/js_composer/config/map.php:6735
msgid "Specific"
msgstr ""

#: modules/elementor/widgets/deal-and-product-tabs.php:156
#: modules/elementor/widgets/product-onsale-carousel-2.php:120
#: modules/elementor/widgets/product-onsale-carousel.php:164
#: modules/elementor/widgets/product-onsale.php:164
#: modules/elementor/widgets/products-cards-carousel.php:222
#: modules/elementor/widgets/products-list-block.php:204
#: modules/js_composer/config/map.php:2656
#: modules/js_composer/config/map.php:4153
#: modules/js_composer/config/map.php:4227
#: modules/js_composer/config/map.php:5212
#: modules/js_composer/config/map.php:6742
msgid "Product ID"
msgstr ""

#: modules/elementor/widgets/deal-and-product-tabs.php:168
#: modules/elementor/widgets/product-categories-menu-list.php:109
#: modules/elementor/widgets/product-list-categories.php:97
#: modules/elementor/widgets/products-cards-carousel.php:97
#: modules/elementor/widgets/products-carousel-tabs-1.php:123
#: modules/elementor/widgets/products-carousel-tabs.php:113
#: modules/elementor/widgets/products-tabs-element.php:99
#: modules/js_composer/config/map.php:730
#: modules/js_composer/config/map.php:1076
#: modules/js_composer/config/map.php:2671
#: modules/js_composer/config/map.php:4808
#: modules/js_composer/config/map.php:5226
msgid "Enter title."
msgstr ""

#: modules/elementor/widgets/deal-and-product-tabs.php:175
#: modules/elementor/widgets/deal-products-block.php:103
#: modules/elementor/widgets/products-cards-carousel.php:168
#: modules/elementor/widgets/products-carousel-tabs-1.php:130
#: modules/elementor/widgets/products-carousel-tabs.php:120
#: modules/elementor/widgets/products-one-two-block.php:103
#: modules/elementor/widgets/products-tabs-element.php:106
#: modules/js_composer/config/map.php:228
#: modules/js_composer/config/map.php:735
#: modules/js_composer/config/map.php:1081
#: modules/js_composer/config/map.php:1274
#: modules/js_composer/config/map.php:1443
#: modules/js_composer/config/map.php:1691
#: modules/js_composer/config/map.php:1933
#: modules/js_composer/config/map.php:2676
#: modules/js_composer/config/map.php:2908
#: modules/js_composer/config/map.php:3048
#: modules/js_composer/config/map.php:3290
#: modules/js_composer/config/map.php:3449
#: modules/js_composer/config/map.php:3608
#: modules/js_composer/config/map.php:3865
#: modules/js_composer/config/map.php:4342
#: modules/js_composer/config/map.php:4602
#: modules/js_composer/config/map.php:4813
#: modules/js_composer/config/map.php:4998
#: modules/js_composer/config/map.php:5231
#: modules/js_composer/config/map.php:5609
#: modules/js_composer/config/map.php:5870
#: modules/js_composer/config/map.php:6089
#: modules/js_composer/config/map.php:6471
#: modules/js_composer/config/map.php:7110
msgid "Shortcode"
msgstr ""

#: modules/elementor/widgets/deal-and-product-tabs.php:195
#: modules/elementor/widgets/deal-products-block.php:143
#: modules/elementor/widgets/product-categories-list.php:106
#: modules/elementor/widgets/product-onsale-carousel.php:141
#: modules/elementor/widgets/products-carousel-tabs-1.php:150
#: modules/elementor/widgets/products-carousel-tabs.php:140
#: modules/elementor/widgets/products-list-block.php:172
#: modules/elementor/widgets/products-tabs-element.php:126
#: modules/elementor/widgets/recent-viewed-products.php:105
#: modules/js_composer/config/map.php:754
#: modules/js_composer/config/map.php:1100
#: modules/js_composer/config/map.php:2768
#: modules/js_composer/config/map.php:2943
#: modules/js_composer/config/map.php:3884
#: modules/js_composer/config/map.php:4377
#: modules/js_composer/config/map.php:4637
#: modules/js_composer/config/map.php:4832
#: modules/js_composer/config/map.php:5250
#: modules/js_composer/config/map.php:5264
#: modules/js_composer/config/map.php:5489
#: modules/js_composer/config/map.php:5905
#: modules/js_composer/config/map.php:6124
#: modules/js_composer/config/map.php:7149
msgid "Enter the number of products to display."
msgstr ""

#: modules/elementor/widgets/deal-and-product-tabs.php:203
#: modules/elementor/widgets/deal-products-block.php:151
#: modules/elementor/widgets/products-cards-carousel.php:115
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:356
#: modules/elementor/widgets/products-category-with-image.php:132
#: modules/elementor/widgets/products-list-block.php:180
#: modules/elementor/widgets/products-tabs-element.php:134
#: modules/elementor/widgets/products-with-category-image.php:132
#: modules/elementor/widgets/recent-viewed-products.php:113
#: modules/elementor/widgets/sidebar-with-products.php:156
#: modules/js_composer/config/map.php:759
#: modules/js_composer/config/map.php:1225
#: modules/js_composer/config/map.php:2782
#: modules/js_composer/config/map.php:2949
#: modules/js_composer/config/map.php:3889
#: modules/js_composer/config/map.php:5255
#: modules/js_composer/config/map.php:5494
#: modules/js_composer/config/map.php:5910
#: modules/js_composer/config/map.php:6130
#: modules/js_composer/config/map.php:7155
msgid "Columns"
msgstr ""

#: modules/elementor/widgets/deal-and-product-tabs.php:205
#: modules/elementor/widgets/deal-products-block.php:153
#: modules/elementor/widgets/product-categories-list.php:97
#: modules/elementor/widgets/products-list-block.php:182
#: modules/elementor/widgets/products-tabs-element.php:136
#: modules/elementor/widgets/recent-viewed-products.php:115
#: modules/js_composer/config/map.php:761
#: modules/js_composer/config/map.php:2951
#: modules/js_composer/config/map.php:3891
#: modules/js_composer/config/map.php:5257
#: modules/js_composer/config/map.php:5496
#: modules/js_composer/config/map.php:5912
#: modules/js_composer/config/map.php:6132
#: modules/js_composer/config/map.php:7157
#: modules/js_composer/config/map.php:7165
msgid "Enter the number of columns to display."
msgstr ""

#: modules/elementor/widgets/deal-and-product-tabs.php:213
#: modules/js_composer/config/map.php:5262
msgid "Wide Layout Limit"
msgstr ""

#: modules/elementor/widgets/deal-and-product-tabs.php:215
msgid "Enter the number of wide products to display."
msgstr ""

#: modules/elementor/widgets/deal-and-product-tabs.php:223
#: modules/js_composer/config/map.php:5340
msgid "Tab Products Wide Layout Columns"
msgstr ""

#: modules/elementor/widgets/deal-and-product-tabs.php:225
#: modules/js_composer/config/map.php:5342
msgid "Enter the number of tap products wide layout columns to display."
msgstr ""

#: modules/elementor/widgets/deal-and-product-tabs.php:235
#: modules/elementor/widgets/product-categories-menu-list.php:140
#: modules/elementor/widgets/products-carousel-tabs-1.php:160
#: modules/elementor/widgets/products-carousel-tabs.php:150
#: modules/elementor/widgets/products-tabs-element.php:165
#: modules/js_composer/config/map.php:775
#: modules/js_composer/config/map.php:1107
#: modules/js_composer/config/map.php:2695
#: modules/js_composer/config/map.php:4839
#: modules/js_composer/config/map.php:5270
msgid "Enter orderby."
msgstr ""

#: modules/elementor/widgets/deal-and-product-tabs.php:245
#: modules/elementor/widgets/product-categories-menu-list.php:150
msgid "Enter order"
msgstr ""

#: modules/elementor/widgets/deal-and-product-tabs.php:266
#: modules/elementor/widgets/deal-products-block.php:174
#: modules/elementor/widgets/products-2-1-2-block.php:157
#: modules/elementor/widgets/products-6-1-block.php:157
#: modules/elementor/widgets/products-6-1-with-categories.php:157
#: modules/elementor/widgets/products-carousel-tabs-1.php:191
#: modules/elementor/widgets/products-carousel-tabs.php:181
#: modules/elementor/widgets/products-one-two-block.php:154
#: modules/js_composer/config/map.php:280
#: modules/js_composer/config/map.php:800
#: modules/js_composer/config/map.php:1132
#: modules/js_composer/config/map.php:1318
#: modules/js_composer/config/map.php:1494
#: modules/js_composer/config/map.php:1742
#: modules/js_composer/config/map.php:1984
#: modules/js_composer/config/map.php:2720
#: modules/js_composer/config/map.php:2968
#: modules/js_composer/config/map.php:3092
#: modules/js_composer/config/map.php:3334
#: modules/js_composer/config/map.php:3493
#: modules/js_composer/config/map.php:3652
#: modules/js_composer/config/map.php:3930
#: modules/js_composer/config/map.php:4394
#: modules/js_composer/config/map.php:4654
#: modules/js_composer/config/map.php:4864
#: modules/js_composer/config/map.php:5049
#: modules/js_composer/config/map.php:5295
#: modules/js_composer/config/map.php:5663
#: modules/js_composer/config/map.php:5935
#: modules/js_composer/config/map.php:6156
#: modules/js_composer/config/map.php:6522
#: modules/js_composer/config/map.php:7182
msgid "Product IDs or SKUs"
msgstr ""

#: modules/elementor/widgets/deal-and-product-tabs.php:294
#: modules/elementor/widgets/products-carousel-tabs.php:209
#: modules/elementor/widgets/products-tabs-element.php:183
#: modules/elementor/widgets/slider-with-ads-block-v2.php:169
#: modules/elementor/widgets/slider-with-ads-block.php:158
msgid "Products Tabs Element"
msgstr ""

#: modules/elementor/widgets/deal-products-block.php:43
#: modules/js_composer/config/map.php:2891
msgid "Deals Products Block"
msgstr ""

#: modules/elementor/widgets/deal-products-block.php:95
#: modules/elementor/widgets/product-categories-block.php:95
#: modules/elementor/widgets/product-categories-list-with-header.php:95
#: modules/elementor/widgets/product-list-categories.php:95
#: modules/elementor/widgets/products-6-1-block.php:95
#: modules/elementor/widgets/products-6-1-with-categories.php:95
#: modules/elementor/widgets/products-cards-carousel.php:95
#: modules/elementor/widgets/products-list-block.php:95
#: modules/elementor/widgets/products-one-two-block.php:95
#: modules/js_composer/config/map.php:2591
msgid "Enter Title"
msgstr ""

#: modules/elementor/widgets/deal-products-block.php:161
#: modules/elementor/widgets/products-carousel-category-with-image.php:164
#: modules/elementor/widgets/products-category-with-image.php:172
#: modules/elementor/widgets/products-one-two-block.php:141
#: modules/elementor/widgets/products-with-category-image.php:172
msgid "Products Choice"
msgstr ""

#: modules/elementor/widgets/deal-products-block.php:185
msgid "Enter the category."
msgstr ""

#: modules/elementor/widgets/deal-products-block.php:192
#: modules/elementor/widgets/products-one-two-block.php:172
msgid "Category Opeartor"
msgstr ""

#: modules/elementor/widgets/deals-products-carousel.php:42
msgid "Deal Products Carousel"
msgstr ""

#: modules/elementor/widgets/deals-products-carousel.php:94
#: modules/js_composer/config/map.php:4961
msgid "Enter deal header title"
msgstr ""

#: modules/elementor/widgets/deals-products-carousel.php:97
#: modules/elementor/widgets/product-categories-menu-list.php:98
#: modules/elementor/widgets/products-carousel-1.php:98
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:192
#: modules/elementor/widgets/products-carousel-with-timer.php:98
#: modules/elementor/widgets/products-carousel.php:98
#: modules/elementor/widgets/two-row-products.php:98
msgid "Enter your title here"
msgstr ""

#: modules/elementor/widgets/deals-products-carousel.php:105
#: modules/elementor/widgets/mobile-product-deals-block.php:104
#: modules/js_composer/config/map.php:1897
#: modules/js_composer/config/map.php:4969
#: modules/js_composer/config/map.php:6449
msgid "Show Header Timer"
msgstr ""

#: modules/elementor/widgets/deals-products-carousel.php:117
#: modules/elementor/widgets/mobile-product-deals-block.php:116
#: modules/elementor/widgets/products-carousel-with-timer.php:128
#: modules/js_composer/config/map.php:1913
#: modules/js_composer/config/map.php:4978
#: modules/js_composer/config/map.php:6458
msgid "Timer Value"
msgstr ""

#: modules/elementor/widgets/deals-products-carousel.php:126
#: modules/elementor/widgets/mobile-product-deals-block.php:125
#: modules/elementor/widgets/products-carousel-with-timer.php:117
#: modules/js_composer/config/map.php:1906
#: modules/js_composer/config/map.php:4985
#: modules/js_composer/config/map.php:6465
msgid "Timer Title"
msgstr ""

#: modules/elementor/widgets/deals-products-carousel.php:137
#: modules/js_composer/config/map.php:4992
msgid "Deal Percentage value"
msgstr ""

#: modules/elementor/widgets/deals-products-carousel.php:165
#: modules/elementor/widgets/mobile-product-deals-block.php:152
#: modules/elementor/widgets/products-carousel-1.php:139
#: modules/elementor/widgets/products-carousel-with-timer.php:173
#: modules/elementor/widgets/two-row-products.php:140
#: modules/js_composer/config/map.php:245
#: modules/js_composer/config/map.php:1460
#: modules/js_composer/config/map.php:1708
#: modules/js_composer/config/map.php:1950
#: modules/js_composer/config/map.php:5015
#: modules/js_composer/config/map.php:6488
msgid "Number of products to display"
msgstr ""

#: modules/elementor/widgets/deals-products-carousel.php:196
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:154
msgid "Enter the product choice."
msgstr ""

#: modules/elementor/widgets/deals-products-carousel.php:214
#: modules/elementor/widgets/mobile-product-deals-block.php:202
#: modules/elementor/widgets/products-2-1-2-block.php:169
msgid "Enter category separate by comma(,)."
msgstr ""

#: modules/elementor/widgets/deals-products-carousel.php:303
#: modules/elementor/widgets/product-carousel-with-category-tabs.php:321
#: modules/elementor/widgets/product-onsale-carousel.php:262
#: modules/elementor/widgets/products-carousel-1.php:260
#: modules/elementor/widgets/products-carousel-with-timer.php:290
#: modules/elementor/widgets/products-carousel.php:304
#: modules/js_composer/config/map.php:1637
#: modules/js_composer/config/map.php:1821
#: modules/js_composer/config/map.php:2035
#: modules/js_composer/config/map.php:4302
#: modules/js_composer/config/map.php:4511
#: modules/js_composer/config/map.php:5141
msgid "Carousel: Margin"
msgstr ""

#: modules/elementor/widgets/feature-block.php:43
msgid "Features Block"
msgstr ""

#: modules/elementor/widgets/feature-block.php:97
msgid "Icon"
msgstr ""

#: modules/elementor/widgets/feature-block.php:105
msgid "Text"
msgstr ""

#: modules/elementor/widgets/feature-block.php:113
#: modules/js_composer/config/map.php:479
msgid "Feature Block"
msgstr ""

#: modules/elementor/widgets/jumbotron.php:43
#: modules/js_composer/config/map.php:544
msgid "Jumbotron"
msgstr ""

#: modules/elementor/widgets/jumbotron.php:103
#: modules/js_composer/config/map.php:557
msgid "Sub Title"
msgstr ""

#: modules/elementor/widgets/mobile-product-deals-block.php:43
#: modules/js_composer/config/map.php:6430
msgid "Electro Mobile Deals Product"
msgstr ""

#: modules/elementor/widgets/mobile-product-deals-block.php:95
#: modules/js_composer/config/map.php:6441
msgid "Enter header title"
msgstr ""

#: modules/elementor/widgets/mobile-product-deals-block.php:184
msgid ""
"Enter product choice separate by comma(,) Note: Only works with Category "
"Shortcode."
msgstr ""

#: modules/elementor/widgets/nav-menu.php:43
msgid "Nav menu"
msgstr ""

#: modules/elementor/widgets/nav-menu.php:87
#: modules/elementor/widgets/vertical-nav-menu.php:87
#: modules/js_composer/config/map.php:14
msgid "Select a menu"
msgstr ""

#: modules/elementor/widgets/nav-menu.php:106
#: modules/elementor/widgets/vertical-nav-menu.php:106
#: modules/js_composer/config/map.php:5456
#: modules/js_composer/config/map.php:6232
msgid "Enter the title of menu."
msgstr ""

#: modules/elementor/widgets/nav-menu.php:116
#: modules/elementor/widgets/vertical-nav-menu.php:136
#: modules/js_composer/config/map.php:5461
#: modules/js_composer/config/map.php:6247
msgid "Menu"
msgstr ""

#: modules/elementor/widgets/product-carousel-with-category-tabs.php:43
msgid "Products Carousel with Category Tabs"
msgstr ""

#: modules/elementor/widgets/product-carousel-with-category-tabs.php:134
#: modules/elementor/widgets/products-carousel-tabs-1.php:170
#: modules/elementor/widgets/products-carousel-tabs.php:160
#: modules/elementor/widgets/products-tabs-element.php:175
#: modules/js_composer/config/map.php:783
#: modules/js_composer/config/map.php:1115
#: modules/js_composer/config/map.php:2703
#: modules/js_composer/config/map.php:4847
#: modules/js_composer/config/map.php:5278
msgid "Enter order."
msgstr ""

#: modules/elementor/widgets/product-carousel-with-category-tabs.php:163
msgid "Enter the product id."
msgstr ""

#: modules/elementor/widgets/product-carousel-with-category-tabs.php:172
msgid "Enter category."
msgstr ""

#: modules/elementor/widgets/product-carousel-with-category-tabs.php:189
#: modules/elementor/widgets/products-6-1-with-categories.php:233
#: modules/elementor/widgets/products-carousel-category-with-image.php:214
#: modules/elementor/widgets/products-category-with-image.php:222
#: modules/elementor/widgets/products-list-block.php:232
#: modules/elementor/widgets/products-with-category-image.php:222
#: modules/js_composer/config/map.php:3738
#: modules/js_composer/config/map.php:3982
#: modules/js_composer/config/map.php:4439
#: modules/js_composer/config/map.php:4699
#: modules/js_composer/config/map.php:5533
#: modules/js_composer/config/map.php:5794
msgid "Enable Header Categories"
msgstr ""

#: modules/elementor/widgets/product-carousel-with-category-tabs.php:191
#: modules/elementor/widgets/products-carousel-category-with-image.php:216
#: modules/elementor/widgets/products-category-with-image.php:224
#: modules/elementor/widgets/products-list-block.php:234
#: modules/elementor/widgets/products-with-category-image.php:224
#: modules/js_composer/config/map.php:3739
#: modules/js_composer/config/map.php:3983
#: modules/js_composer/config/map.php:4440
#: modules/js_composer/config/map.php:4700
#: modules/js_composer/config/map.php:5534
#: modules/js_composer/config/map.php:5795
msgid "Show Categories list on header block."
msgstr ""

#: modules/elementor/widgets/product-carousel-with-category-tabs.php:202
#: modules/elementor/widgets/products-6-1-with-categories.php:225
#: modules/elementor/widgets/products-carousel-category-with-image.php:205
#: modules/elementor/widgets/products-category-with-image.php:213
#: modules/elementor/widgets/products-list-block.php:245
#: modules/elementor/widgets/products-with-category-image.php:213
#: modules/js_composer/config/map.php:3730
#: modules/js_composer/config/map.php:3974
#: modules/js_composer/config/map.php:4448
#: modules/js_composer/config/map.php:4708
#: modules/js_composer/config/map.php:5542
#: modules/js_composer/config/map.php:5803
msgid "Enter categories title"
msgstr ""

#: modules/elementor/widgets/product-carousel-with-category-tabs.php:220
#: modules/elementor/widgets/product-categories-block.php:128
#: modules/elementor/widgets/product-categories-list-with-header.php:156
#: modules/elementor/widgets/product-categories-list.php:120
#: modules/elementor/widgets/product-categories-menu-list.php:127
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:140
#: modules/elementor/widgets/product-categories-with-banner-carousel.php:215
#: modules/elementor/widgets/product-list-categories.php:119
#: modules/elementor/widgets/products-2-1-2-block.php:200
#: modules/elementor/widgets/products-6-1-block.php:199
#: modules/elementor/widgets/products-6-1-with-categories.php:260
#: modules/elementor/widgets/products-6-1-with-categories.php:320
#: modules/elementor/widgets/products-carousel-category-with-image.php:238
#: modules/elementor/widgets/products-category-with-image.php:246
#: modules/elementor/widgets/products-list-block.php:263
#: modules/elementor/widgets/products-with-category-image.php:246
#: modules/js_composer/config/map.php:1380
#: modules/js_composer/config/map.php:2225
#: modules/js_composer/config/map.php:2302
#: modules/js_composer/config/map.php:2471
#: modules/js_composer/config/map.php:2840
#: modules/js_composer/config/map.php:3211
#: modules/js_composer/config/map.php:3387
#: modules/js_composer/config/map.php:3546
#: modules/js_composer/config/map.php:3756
#: modules/js_composer/config/map.php:3803
#: modules/js_composer/config/map.php:4000
#: modules/js_composer/config/map.php:4047
#: modules/js_composer/config/map.php:4464
#: modules/js_composer/config/map.php:4724
#: modules/js_composer/config/map.php:5410
#: modules/js_composer/config/map.php:5558
#: modules/js_composer/config/map.php:5819
#: modules/js_composer/config/map.php:6598
#: modules/js_composer/config/map.php:6661
#: modules/js_composer/config/map.php:6854
#: modules/js_composer/config/map.php:6910
msgid "Show Categories does not have products"
msgstr ""

#: modules/elementor/widgets/product-carousel-with-category-tabs.php:254
msgid "Enter category include separate by comma(,)."
msgstr ""

#: modules/elementor/widgets/product-carousel-with-category-tabs.php:262
#: modules/elementor/widgets/product-categories-block-v2.php:150
#: modules/elementor/widgets/product-categories-block.php:155
#: modules/elementor/widgets/product-categories-list-with-header.php:183
#: modules/elementor/widgets/product-categories-list.php:147
#: modules/elementor/widgets/product-categories-menu-list.php:158
#: modules/elementor/widgets/product-category-tags.php:151
#: modules/elementor/widgets/product-list-categories.php:146
#: modules/elementor/widgets/products-2-1-2-block.php:235
#: modules/elementor/widgets/products-6-1-block.php:234
#: modules/elementor/widgets/products-6-1-with-categories.php:295
#: modules/elementor/widgets/products-cards-carousel.php:312
#: modules/elementor/widgets/products-carousel-category-with-image.php:280
#: modules/elementor/widgets/products-categories-1-6.php:127
#: modules/elementor/widgets/products-category-with-image.php:288
#: modules/elementor/widgets/products-with-category-image.php:288
#: modules/elementor/widgets/products-with-category-image.php:351
#: modules/js_composer/config/map.php:1411
#: modules/js_composer/config/map.php:2249
#: modules/js_composer/config/map.php:2326
#: modules/js_composer/config/map.php:2414
#: modules/js_composer/config/map.php:2495
#: modules/js_composer/config/map.php:2864
#: modules/js_composer/config/map.php:3235
#: modules/js_composer/config/map.php:3418
#: modules/js_composer/config/map.php:3577
#: modules/js_composer/config/map.php:3787
#: modules/js_composer/config/map.php:3834
#: modules/js_composer/config/map.php:4031
#: modules/js_composer/config/map.php:4078
#: modules/js_composer/config/map.php:4496
#: modules/js_composer/config/map.php:4756
#: modules/js_composer/config/map.php:5589
#: modules/js_composer/config/map.php:5850
#: modules/js_composer/config/map.php:6622
#: modules/js_composer/config/map.php:6685
msgid "Include slug's"
msgstr ""

#: modules/elementor/widgets/product-categories-block-v2.php:43
msgid "Product Categories Block v2"
msgstr ""

#: modules/elementor/widgets/product-categories-block-v2.php:113
msgid "Hide Empty Categories"
msgstr ""

#: modules/elementor/widgets/product-categories-block-v2.php:119
msgid "Hide categories does not have products"
msgstr ""

#: modules/elementor/widgets/product-categories-block.php:43
#: modules/js_composer/config/map.php:2269
msgid "Product Categories Block"
msgstr ""

#: modules/elementor/widgets/product-categories-block.php:171
#: modules/js_composer/config/map.php:2340
msgid "Enable Fullwidth"
msgstr ""

#: modules/elementor/widgets/product-categories-list-with-header.php:43
msgid "Products Categories List With Header Image"
msgstr ""

#: modules/elementor/widgets/product-categories-list-with-header.php:103
msgid "Sub title"
msgstr ""

#: modules/elementor/widgets/product-categories-list-with-header.php:111
#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:173
#: modules/elementor/widgets/slider-with-ads-block-v2.php:114
#: modules/js_composer/config/map.php:222
#: modules/js_composer/config/map.php:3179
#: modules/js_composer/config/map.php:7019
#: modules/js_composer/config/map.php:7407
msgid "Background Image"
msgstr ""

#: modules/elementor/widgets/product-categories-list-with-header.php:120
#: modules/js_composer/config/map.php:3186
msgid "Enable Header"
msgstr ""

#: modules/elementor/widgets/product-categories-list-with-header.php:126
#: modules/js_composer/config/map.php:3187
msgid "Show header block."
msgstr ""

#: modules/elementor/widgets/product-categories-list-with-header.php:199
#: modules/js_composer/config/map.php:3249
msgid "Header Version"
msgstr ""

#: modules/elementor/widgets/product-categories-list-with-header.php:202
#: modules/js_composer/config/map.php:3252
msgid "Type 1"
msgstr ""

#: modules/elementor/widgets/product-categories-list-with-header.php:203
#: modules/js_composer/config/map.php:3253
msgid "Type 2"
msgstr ""

#: modules/elementor/widgets/product-categories-list.php:43
#: modules/js_composer/config/map.php:2814
msgid "Product Categories List"
msgstr ""

#: modules/elementor/widgets/product-categories-menu-list.php:43
#: modules/elementor/widgets/product-categories-menu-list.php:177
msgid "Product List Categories Menu"
msgstr ""

#: modules/elementor/widgets/product-categories-menu-list.php:95
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:95
#: modules/elementor/widgets/recent-viewed-products.php:95
#: modules/elementor/widgets/recently-viewed-products-carousel.php:95
#: modules/js_composer/config/map.php:5361
#: modules/js_composer/config/map.php:5482
msgid "Section Title"
msgstr ""

#: modules/elementor/widgets/product-categories-menu-list.php:160
#: modules/elementor/widgets/product-list-categories.php:148
msgid "Enter the slugs seperate by comma(,)."
msgstr ""

#: modules/elementor/widgets/product-categories-menu-list.php:169
#: modules/elementor/widgets/product-list-categories.php:157
msgid "Enter the id seperate by comma(,)."
msgstr ""

#: modules/elementor/widgets/product-categories-with-banner-carousel.php:43
#: modules/js_composer/config/map.php:6807
msgid "Product Categories With Banner Carousel"
msgstr ""

#: modules/elementor/widgets/product-categories-with-banner-carousel.php:95
#: modules/js_composer/config/map.php:6816
msgid "Enter Section Title"
msgstr ""

#: modules/elementor/widgets/product-categories-with-banner-carousel.php:106
#: modules/js_composer/config/map.php:6830
msgid "Enable Categories 1"
msgstr ""

#: modules/elementor/widgets/product-categories-with-banner-carousel.php:118
#: modules/js_composer/config/map.php:6838
msgid "Categories List 1: limit"
msgstr ""

#: modules/elementor/widgets/product-categories-with-banner-carousel.php:120
msgid "Enter category limit"
msgstr ""

#: modules/elementor/widgets/product-categories-with-banner-carousel.php:128
#: modules/js_composer/config/map.php:6845
msgid "Categories List 1: Child limit"
msgstr ""

#: modules/elementor/widgets/product-categories-with-banner-carousel.php:130
msgid "Enter category child limt"
msgstr ""

#: modules/elementor/widgets/product-categories-with-banner-carousel.php:138
msgid "Categories List 1: Hide Empty products"
msgstr ""

#: modules/elementor/widgets/product-categories-with-banner-carousel.php:151
#: modules/js_composer/config/map.php:6862
msgid "Categories List 1: Order by"
msgstr ""

#: modules/elementor/widgets/product-categories-with-banner-carousel.php:175
#: modules/js_composer/config/map.php:6878
msgid "Categories List 1: Include ID's"
msgstr ""

#: modules/elementor/widgets/product-categories-with-banner-carousel.php:183
#: modules/js_composer/config/map.php:6885
msgid "Categories List 1: Include slug's"
msgstr ""

#: modules/elementor/widgets/product-categories-with-banner-carousel.php:191
#: modules/js_composer/config/map.php:6893
msgid "Enable Categories 2"
msgstr ""

#: modules/elementor/widgets/product-categories-with-banner-carousel.php:203
#: modules/js_composer/config/map.php:6901
msgid "Categories List 2: limit"
msgstr ""

#: modules/elementor/widgets/product-categories-with-banner-carousel.php:205
msgid "Enter category limt"
msgstr ""

#: modules/elementor/widgets/product-categories-with-banner-carousel.php:213
msgid "Categories List 2: Hide Empty products"
msgstr ""

#: modules/elementor/widgets/product-categories-with-banner-carousel.php:226
#: modules/js_composer/config/map.php:6918
msgid "Categories List 2: Order by"
msgstr ""

#: modules/elementor/widgets/product-categories-with-banner-carousel.php:250
#: modules/js_composer/config/map.php:6934
msgid "Categories List 2: Include ID's"
msgstr ""

#: modules/elementor/widgets/product-categories-with-banner-carousel.php:258
#: modules/js_composer/config/map.php:6941
msgid "Categories List 2: Include slug's"
msgstr ""

#: modules/elementor/widgets/product-categories-with-banner-carousel.php:266
#: modules/js_composer/config/map.php:6949
msgid "Enable Banner ?"
msgstr ""

#: modules/elementor/widgets/product-categories-with-banner-carousel.php:295
#: modules/js_composer/config/map.php:6824
msgid "Carousel Elements"
msgstr ""

#: modules/elementor/widgets/product-categories-with-banner-carousel.php:305
#: modules/elementor/widgets/products-carousel-1.php:269
#: modules/elementor/widgets/products-carousel-tabs-1.php:277
#: modules/elementor/widgets/products-carousel-with-timer.php:299
#: modules/js_composer/config/map.php:367
#: modules/js_composer/config/map.php:1597
#: modules/js_composer/config/map.php:1829
#: modules/js_composer/config/map.php:2043
#: modules/js_composer/config/map.php:4519
#: modules/js_composer/config/map.php:4918
#: modules/js_composer/config/map.php:5101
#: modules/js_composer/config/map.php:6972
msgid "Carousel: Show Navigation"
msgstr ""

#: modules/elementor/widgets/product-category-tags.php:43
#: modules/js_composer/config/map.php:6572
msgid "Product Category Tags"
msgstr ""

#: modules/elementor/widgets/product-category-tags.php:98
msgid "Popular Search"
msgstr ""

#: modules/elementor/widgets/product-category-tags.php:115
#: modules/elementor/widgets/products-categories-1-6.php:81
msgid "Hide Empty"
msgstr ""

#: modules/elementor/widgets/product-category-tags.php:153
#: modules/elementor/widgets/products-categories-1-6.php:129
#: modules/js_composer/config/map.php:6686
msgid "Enter slug spearate by comma(,). Maximum of 7."
msgstr ""

#: modules/elementor/widgets/product-category-tags.php:162
#: modules/elementor/widgets/products-categories-1-6.php:138
#: modules/js_composer/config/map.php:6694
msgid "Enter ids spearate by comma(,). Maximum of 7."
msgstr ""

#: modules/elementor/widgets/product-list-categories.php:43
msgid "Product List Categories "
msgstr ""

#: modules/elementor/widgets/product-onsale-carousel-2.php:43
msgid "Onsale Product Carousel 2"
msgstr ""

#: modules/elementor/widgets/product-onsale-carousel-2.php:95
#: modules/elementor/widgets/product-onsale-carousel.php:139
#: modules/elementor/widgets/product-onsale.php:139
#: modules/js_composer/config/map.php:4209
#: modules/js_composer/config/map.php:6724
msgid "Number of Products to display"
msgstr ""

#: modules/elementor/widgets/product-onsale-carousel-2.php:98
msgid "Enter number of products to display"
msgstr ""

#: modules/elementor/widgets/product-onsale-carousel-2.php:122
#: modules/elementor/widgets/product-onsale-carousel.php:166
#: modules/elementor/widgets/product-onsale.php:166
msgid "Enter the product id seperate by comma(,)."
msgstr ""

#: modules/elementor/widgets/product-onsale-carousel-2.php:129
#: modules/elementor/widgets/product-onsale-carousel.php:197
#: modules/elementor/widgets/products-carousel-with-timer.php:105
#: modules/js_composer/config/map.php:4253
#: modules/js_composer/config/map.php:6750
msgid "Show Timer"
msgstr ""

#: modules/elementor/widgets/product-onsale-carousel-2.php:141
#: modules/js_composer/config/map.php:6759
msgid "Show Navigation"
msgstr ""

#: modules/elementor/widgets/product-onsale-carousel.php:43
msgid "Onsale Product Carousel"
msgstr ""

#: modules/elementor/widgets/product-onsale-carousel.php:173
#: modules/elementor/widgets/products-carousel.php:179
#: modules/js_composer/config/map.php:1539
#: modules/js_composer/config/map.php:4235
msgid "Show Custom Navigation"
msgstr ""

#: modules/elementor/widgets/product-onsale-carousel.php:185
#: modules/js_composer/config/map.php:4244
msgid "Show Progress"
msgstr ""

#: modules/elementor/widgets/product-onsale-carousel.php:209
#: modules/js_composer/config/map.php:4262
msgid "Show Cart Button"
msgstr ""

#: modules/elementor/widgets/product-onsale-carousel.php:245
#: modules/elementor/widgets/products-carousel-tabs-1.php:289
#: modules/js_composer/config/map.php:1623
#: modules/js_composer/config/map.php:4288
#: modules/js_composer/config/map.php:5127
msgid "Carousel: Nav Next Text"
msgstr ""

#: modules/elementor/widgets/product-onsale-carousel.php:253
#: modules/elementor/widgets/products-carousel-tabs-1.php:297
#: modules/js_composer/config/map.php:1630
#: modules/js_composer/config/map.php:4295
#: modules/js_composer/config/map.php:5134
msgid "Carousel: Nav Prev Text"
msgstr ""

#: modules/elementor/widgets/product-onsale.php:43
msgid "Onsale Product"
msgstr ""

#: modules/elementor/widgets/product-onsale.php:141
msgid "Enter the number of products to display"
msgstr ""

#: modules/elementor/widgets/products-2-1-2-block.php:43
msgid "Electro Products 2-1-2 Block"
msgstr ""

#: modules/elementor/widgets/products-2-1-2-block.php:97
msgid "Enter the Tilte"
msgstr ""

#: modules/elementor/widgets/products-2-1-2-block.php:104
#: modules/elementor/widgets/products-6-1-block.php:104
#: modules/elementor/widgets/products-6-1-with-categories.php:104
msgid "Shortcode Tag"
msgstr ""

#: modules/elementor/widgets/products-2-1-2-block.php:178
msgid "Enter the categgory operator seperate by comma(,)."
msgstr ""

#: modules/elementor/widgets/products-2-1-2-block.php:187
#: modules/elementor/widgets/products-6-1-with-categories.php:247
msgid "Enter the category limit."
msgstr ""

#: modules/elementor/widgets/products-2-1-2-block.php:219
#: modules/elementor/widgets/products-6-1-block.php:218
msgid "Enter category order."
msgstr ""

#: modules/elementor/widgets/products-2-1-2-block.php:228
msgid "Enter the id separate by comma(,)."
msgstr ""

#: modules/elementor/widgets/products-2-1-2-block.php:237
msgid "Enter the slug separate by comma(,)."
msgstr ""

#: modules/elementor/widgets/products-6-1-block.php:43
msgid "Products 6-1 Block"
msgstr ""

#: modules/elementor/widgets/products-6-1-block.php:97
#: modules/elementor/widgets/products-6-1-with-categories.php:97
msgid "Enter the title."
msgstr ""

#: modules/elementor/widgets/products-6-1-block.php:227
#: modules/elementor/widgets/products-6-1-with-categories.php:288
#: modules/elementor/widgets/products-6-1-with-categories.php:350
#: modules/elementor/widgets/products-cards-carousel.php:305
msgid "Enter term id separate by comma(,)."
msgstr ""

#: modules/elementor/widgets/products-6-1-block.php:236
#: modules/elementor/widgets/products-6-1-with-categories.php:279
#: modules/elementor/widgets/products-6-1-with-categories.php:359
#: modules/elementor/widgets/products-cards-carousel.php:275
#: modules/elementor/widgets/products-cards-carousel.php:296
msgid "Enter term slug separate by comma(,)."
msgstr ""

#: modules/elementor/widgets/products-6-1-with-categories.php:43
msgid "Products 6-1 With Categories"
msgstr ""

#: modules/elementor/widgets/products-6-1-with-categories.php:184
#: modules/js_composer/config/map.php:3696
msgid "Featured Product Shortcode"
msgstr ""

#: modules/elementor/widgets/products-6-1-with-categories.php:202
#: modules/js_composer/config/map.php:3711
msgid "Featured Product Choice"
msgstr ""

#: modules/elementor/widgets/products-6-1-with-categories.php:216
#: modules/js_composer/config/map.php:3723
msgid "Featured Product ID"
msgstr ""

#: modules/elementor/widgets/products-6-1-with-categories.php:218
#: modules/js_composer/config/map.php:3725
msgid "Enter ID/SKU. Only for Products Shortcode."
msgstr ""

#: modules/elementor/widgets/products-6-1-with-categories.php:304
#: modules/elementor/widgets/products-with-category-image.php:298
#: modules/js_composer/config/map.php:3794
#: modules/js_composer/config/map.php:4038
msgid "Number of Vertical Categories to display"
msgstr ""

#: modules/elementor/widgets/products-6-1-with-categories.php:357
#: modules/elementor/widgets/products-list-block.php:303
msgid "Include Slug's"
msgstr ""

#: modules/elementor/widgets/products-cards-carousel.php:43
msgid "Products Cards Carousel Block"
msgstr ""

#: modules/elementor/widgets/products-cards-carousel.php:105
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:347
#: modules/js_composer/config/map.php:1218
#: modules/js_composer/config/map.php:2774
msgid "Rows"
msgstr ""

#: modules/elementor/widgets/products-cards-carousel.php:107
msgid "Enter the number of rows."
msgstr ""

#: modules/elementor/widgets/products-cards-carousel.php:117
#: modules/elementor/widgets/products-cards-carousel.php:127
msgid "Enter the number of columns."
msgstr ""

#: modules/elementor/widgets/products-cards-carousel.php:125
#: modules/elementor/widgets/products-carousel.php:193
#: modules/elementor/widgets/products-with-category-image.php:142
#: modules/js_composer/config/map.php:1232
#: modules/js_composer/config/map.php:1547
#: modules/js_composer/config/map.php:3896
msgid "Products Wide Layout Columns"
msgstr ""

#: modules/elementor/widgets/products-cards-carousel.php:135
#: modules/js_composer/config/map.php:1250
msgid "Show Top Text"
msgstr ""

#: modules/elementor/widgets/products-cards-carousel.php:147
#: modules/js_composer/config/map.php:1259
msgid "Show Categories"
msgstr ""

#: modules/elementor/widgets/products-carousel-1.php:43
msgid "Products Carousel 1"
msgstr ""

#: modules/elementor/widgets/products-carousel-1.php:282
#: modules/elementor/widgets/products-carousel-category-with-image.php:353
#: modules/elementor/widgets/products-carousel-with-timer.php:312
msgid "Carousel:Show Dots"
msgstr ""

#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:43
msgid "Products Carousel Banner Vertical Tabs"
msgstr ""

#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:115
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:220
#: modules/elementor/widgets/products-carousel.php:135
msgid "Enter Orderby"
msgstr ""

#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:125
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:230
#: modules/elementor/widgets/products-carousel.php:144
msgid "Enter Order"
msgstr ""

#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:192
#: modules/js_composer/config/map.php:179
#: modules/js_composer/config/map.php:186
msgid "Tab Title"
msgstr ""

#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:194
msgid "Enter your tab title here"
msgstr ""

#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:201
#: modules/elementor/widgets/three-banners.php:209
msgid "Banner Title"
msgstr ""

#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:203
msgid "Enter your banner title here"
msgstr ""

#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:210
#: modules/elementor/widgets/three-banners.php:219
msgid "Banner Subtitle"
msgstr ""

#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:212
msgid "Enter your banner subtitle here"
msgstr ""

#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:227
#: modules/js_composer/config/map.php:200
msgid "Banner Action Text"
msgstr ""

#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:229
msgid "Enter your banner text here"
msgstr ""

#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:238
msgid "Enter your banner link here"
msgstr ""

#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:245
msgid "Banner Tabs"
msgstr ""

#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:304
#: modules/elementor/widgets/products-carousel.php:257
#: modules/elementor/widgets/recently-viewed-products-carousel.php:161
#: modules/js_composer/config/map.php:1589
msgid "Carousel: Items(1200 - 1429)"
msgstr ""

#: modules/elementor/widgets/products-carousel-banner-vertical-tabs.php:352
#: modules/elementor/widgets/recently-viewed-products-carousel.php:209
msgid "Enter the Additional Class."
msgstr ""

#: modules/elementor/widgets/products-carousel-category-with-image.php:43
msgid "Products Carousel Categories and Image"
msgstr ""

#: modules/elementor/widgets/products-carousel-category-with-image.php:132
msgid "Enale product description "
msgstr ""

#: modules/elementor/widgets/products-carousel-category-with-image.php:297
#: modules/elementor/widgets/products-category-with-image.php:305
#: modules/elementor/widgets/products-with-category-image.php:368
#: modules/js_composer/config/map.php:4091
#: modules/js_composer/config/map.php:5602
#: modules/js_composer/config/map.php:5863
msgid "Image Action Link"
msgstr ""

#: modules/elementor/widgets/products-carousel-tabs-1.php:43
msgid "Product Tabs Carousel 1"
msgstr ""

#: modules/elementor/widgets/products-carousel-tabs-1.php:121
#: modules/elementor/widgets/products-carousel-tabs.php:111
#: modules/elementor/widgets/products-tabs-element.php:97
msgid "title"
msgstr ""

#: modules/elementor/widgets/products-carousel-tabs-1.php:219
#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:328
#: modules/js_composer/config/map.php:174
#: modules/js_composer/config/map.php:723
#: modules/js_composer/config/map.php:1069
#: modules/js_composer/config/map.php:2664
#: modules/js_composer/config/map.php:4801
#: modules/js_composer/config/map.php:5219
msgid "Tabs"
msgstr ""

#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:43
msgid "Products Carousel Tabs With Deal"
msgstr ""

#: modules/elementor/widgets/products-carousel-tabs-with-deal.php:121
msgid "Deals Title"
msgstr ""

#: modules/elementor/widgets/products-carousel-tabs.php:43
msgid "Product Tabs Carousel"
msgstr ""

#: modules/elementor/widgets/products-carousel-tabs.php:95
#: modules/js_composer/config/map.php:988
#: modules/js_composer/config/map.php:1056
msgid "Header Align"
msgstr ""

#: modules/elementor/widgets/products-carousel-tabs.php:98
#: modules/js_composer/config/map.php:992
#: modules/js_composer/config/map.php:1060
msgid "Center"
msgstr ""

#: modules/elementor/widgets/products-carousel-tabs.php:99
#: modules/js_composer/config/map.php:994
#: modules/js_composer/config/map.php:1062
msgid "Right"
msgstr ""

#: modules/elementor/widgets/products-carousel-tabs.php:100
#: modules/js_composer/config/map.php:993
#: modules/js_composer/config/map.php:1061
msgid "Left"
msgstr ""

#: modules/elementor/widgets/products-carousel-tabs.php:267
#: modules/js_composer/config/map.php:1028
msgid "Carousel: Items(1200 - 1440)"
msgstr ""

#: modules/elementor/widgets/products-carousel-with-timer.php:43
msgid "Products Carousel With Timer"
msgstr ""

#: modules/elementor/widgets/products-carousel-with-timer.php:120
msgid "Enter your timer title here"
msgstr ""

#: modules/elementor/widgets/products-carousel-with-timer.php:131
msgid "Enter your timer value here"
msgstr ""

#: modules/elementor/widgets/products-carousel.php:43
msgid "Products Carousel"
msgstr ""

#: modules/elementor/widgets/products-carousel.php:126
msgid "Enter id separate by comma(,) Note: Only works with Products Shortcode."
msgstr ""

#: modules/elementor/widgets/products-carousel.php:154
msgid "Enter product choice."
msgstr ""

#: modules/elementor/widgets/products-carousel.php:185
msgid "Custom navigation"
msgstr ""

#: modules/elementor/widgets/products-carousel.php:196
#: modules/js_composer/config/map.php:1235
#: modules/js_composer/config/map.php:1549
#: modules/js_composer/config/map.php:3898
#: modules/js_composer/config/map.php:5919
#: modules/js_composer/config/map.php:6140
msgid "Option only works if Wide Electro Layout enabled."
msgstr ""

#: modules/elementor/widgets/products-carousel.php:267
msgid "Show Dots"
msgstr ""

#: modules/elementor/widgets/products-carousel.php:292
msgid "Autoplay"
msgstr ""

#: modules/elementor/widgets/products-categories-1-6.php:43
msgid "Products Carousel 1-6"
msgstr ""

#: modules/elementor/widgets/products-category-with-image.php:43
msgid "Products Categories Tabs and Image"
msgstr ""

#: modules/elementor/widgets/products-category-with-image.php:134
#: modules/elementor/widgets/products-category-with-image.php:144
#: modules/elementor/widgets/products-with-category-image.php:134
#: modules/elementor/widgets/products-with-category-image.php:144
#: modules/elementor/widgets/sidebar-with-products.php:158
#: modules/elementor/widgets/sidebar-with-products.php:178
msgid "Enter columns of the products."
msgstr ""

#: modules/elementor/widgets/products-category-with-image.php:142
#: modules/elementor/widgets/products-tabs-element.php:144
#: modules/elementor/widgets/sidebar-with-products.php:176
#: modules/elementor/widgets/two-row-products.php:159
#: modules/js_composer/config/map.php:766
#: modules/js_composer/config/map.php:5501
#: modules/js_composer/config/map.php:5917
#: modules/js_composer/config/map.php:6138
msgid "Columns Wide"
msgstr ""

#: modules/elementor/widgets/products-category-with-image.php:313
msgid "Section Class"
msgstr ""

#: modules/elementor/widgets/products-category-with-image.php:315
msgid "Enter Class"
msgstr ""

#: modules/elementor/widgets/products-list-block.php:43
msgid "Products List Block"
msgstr ""

#: modules/elementor/widgets/products-list-block.php:103
#: modules/js_composer/config/map.php:4578
msgid "Type"
msgstr ""

#: modules/elementor/widgets/products-list-block.php:107
#: modules/js_composer/config/map.php:4581
msgid "v1"
msgstr ""

#: modules/elementor/widgets/products-list-block.php:108
#: modules/js_composer/config/map.php:4582
msgid "v2"
msgstr ""

#: modules/elementor/widgets/products-list-block.php:206
msgid "Enter IDs/SKsp separate by comma(,)."
msgstr ""

#: modules/elementor/widgets/products-list-block.php:253
msgid "Category: Items"
msgstr ""

#: modules/elementor/widgets/products-list-block.php:296
#: modules/js_composer/config/map.php:4490
#: modules/js_composer/config/map.php:4750
msgid "Enter IDs of Categories to display"
msgstr ""

#: modules/elementor/widgets/products-list-block.php:305
#: modules/js_composer/config/map.php:4498
#: modules/js_composer/config/map.php:4758
msgid "Enter slug of Categories to display"
msgstr ""

#: modules/elementor/widgets/products-one-two-block.php:43
#: modules/js_composer/config/map.php:3031
msgid "Products 1-2 Block"
msgstr ""

#: modules/elementor/widgets/products-tabs-element.php:43
#: modules/js_composer/config/map.php:574
#: modules/js_composer/config/map.php:713
msgid "Product Tabs"
msgstr ""

#: modules/elementor/widgets/products-tabs-element.php:146
#: modules/js_composer/config/map.php:768
#: modules/js_composer/config/map.php:5503
msgid "Enter the number of columns wide to display."
msgstr ""

#: modules/elementor/widgets/products-with-category-image.php:43
msgid "Products With Categories and Image"
msgstr ""

#: modules/elementor/widgets/recent-viewed-products.php:43
msgid "Recent Vieved Products"
msgstr ""

#: modules/elementor/widgets/recently-viewed-products-carousel.php:43
msgid "Recently Vieved Products Carousel"
msgstr ""

#: modules/elementor/widgets/sidebar-with-products.php:44
msgid "Sidebar With Products"
msgstr ""

#: modules/elementor/widgets/sidebar-with-products.php:105
#: modules/js_composer/config/map.php:7093
msgid "Enable Sidebar"
msgstr ""

#: modules/elementor/widgets/sidebar-with-products.php:107
msgid "Show sidebar block."
msgstr ""

#: modules/elementor/widgets/sidebar-with-products.php:118
msgid "Sidebar Title"
msgstr ""

#: modules/elementor/widgets/sidebar-with-products.php:166
msgid "Columns Tablet"
msgstr ""

#: modules/elementor/widgets/sidebar-with-products.php:168
msgid "Enter columns of the products in tablet view."
msgstr ""

#: modules/elementor/widgets/sidebar-with-products.php:300
#: modules/js_composer/config/map.php:7227
msgid "Enable Pagination"
msgstr ""

#: modules/elementor/widgets/sidebar-with-products.php:302
msgid "Show pagination  block."
msgstr ""

#: modules/elementor/widgets/sidebar-with-products.php:315
#: modules/elementor/widgets/slider-with-ads-block-v2.php:181
#: modules/elementor/widgets/two-row-products.php:235
#: modules/elementor/widgets/two-row-products.php:244
msgid "If you wish to style particular style use this field"
msgstr ""

#: modules/elementor/widgets/sidebar-with-products.php:379
msgid "Add Products"
msgstr ""

#: modules/elementor/widgets/slider-with-ads-block-v2.php:45
msgid "Slider With Ads Block v2"
msgstr ""

#: modules/elementor/widgets/slider-with-ads-block-v2.php:87
#: modules/elementor/widgets/slider-with-ads-block.php:85
#: modules/js_composer/config/map.php:22
msgid "No sliders found"
msgstr ""

#: modules/elementor/widgets/slider-with-ads-block-v2.php:144
msgid "Enter your ad text here"
msgstr ""

#: modules/elementor/widgets/slider-with-ads-block-v2.php:153
msgid "Enter your action text here"
msgstr ""

#: modules/elementor/widgets/slider-with-ads-block.php:43
msgid "Slider With Ads Block"
msgstr ""

#: modules/elementor/widgets/team-member.php:43
msgid "Team Member"
msgstr ""

#: modules/elementor/widgets/team-member.php:94
#: modules/js_composer/config/map.php:6270
msgid "Full Name"
msgstr ""

#: modules/elementor/widgets/team-member.php:96
#: modules/js_composer/config/map.php:6272
msgid "Enter team member full name"
msgstr ""

#: modules/elementor/widgets/team-member.php:103
#: modules/js_composer/config/map.php:6277
msgid "Designation"
msgstr ""

#: modules/elementor/widgets/team-member.php:105
#: modules/js_composer/config/map.php:6279
msgid "Enter designation of team member"
msgstr ""

#: modules/elementor/widgets/team-member.php:112
#: modules/js_composer/config/map.php:6283
msgid "Profile Pic"
msgstr ""

#: modules/elementor/widgets/team-member.php:120
#: modules/js_composer/config/map.php:6288
msgid "Display Style"
msgstr ""

#: modules/elementor/widgets/team-member.php:123
#: modules/js_composer/config/map.php:6290
msgid "Square"
msgstr ""

#: modules/elementor/widgets/team-member.php:124
#: modules/js_composer/config/map.php:6291
msgid "Circle"
msgstr ""

#: modules/elementor/widgets/team-member.php:133
#: modules/js_composer/config/map.php:6298
msgid "Link"
msgstr ""

#: modules/elementor/widgets/team-member.php:135
#: modules/js_composer/config/map.php:6300
msgid "Add link to the team member. Leave blank if there aren't any"
msgstr ""

#: modules/elementor/widgets/three-banners.php:43
msgid "Three Banners"
msgstr ""

#: modules/elementor/widgets/three-banners.php:87
msgid "Banner 1 Options"
msgstr ""

#: modules/elementor/widgets/three-banners.php:95
#: modules/elementor/widgets/three-banners.php:138
#: modules/elementor/widgets/three-banners.php:193
msgid "Banner Background Image"
msgstr ""

#: modules/elementor/widgets/three-banners.php:111
#: modules/elementor/widgets/three-banners.php:146
#: modules/elementor/widgets/three-banners.php:201
msgid "Banner Link"
msgstr ""

#: modules/elementor/widgets/three-banners.php:119
#: modules/elementor/widgets/three-banners.php:154
#: modules/elementor/widgets/three-banners.php:229
msgid "Banner Description"
msgstr ""

#: modules/elementor/widgets/three-banners.php:130
msgid "Banner 2 Options"
msgstr ""

#: modules/elementor/widgets/three-banners.php:156
#: modules/js_composer/config/map.php:7793
#: modules/js_composer/includes/elements/vc-three-banners.php:14
msgid "OFFICE LAPTOPSFOR WORK"
msgstr ""

#: modules/elementor/widgets/three-banners.php:163
msgid "Banner Before Price Text"
msgstr ""

#: modules/elementor/widgets/three-banners.php:165
msgid "Enter pre text"
msgstr ""

#: modules/elementor/widgets/three-banners.php:166
#: modules/js_composer/config/map.php:7800
#: modules/js_composer/includes/elements/vc-three-banners.php:15
msgid "FROM"
msgstr ""

#: modules/elementor/widgets/three-banners.php:173
msgid "Banner Price Text"
msgstr ""

#: modules/elementor/widgets/three-banners.php:175
msgid "Enter price"
msgstr ""

#: modules/elementor/widgets/three-banners.php:185
msgid "Banner 3 Options"
msgstr ""

#: modules/elementor/widgets/three-banners.php:211
msgid "Enter banner title"
msgstr ""

#: modules/elementor/widgets/three-banners.php:212
#: modules/js_composer/config/map.php:7826
#: modules/js_composer/includes/elements/vc-three-banners.php:19
msgid "LIMITED"
msgstr ""

#: modules/elementor/widgets/three-banners.php:221
msgid "Enter banner subtitle"
msgstr ""

#: modules/elementor/widgets/three-banners.php:222
#: modules/js_composer/config/map.php:7833
#: modules/js_composer/includes/elements/vc-three-banners.php:20
msgid "WEEK DEAL"
msgstr ""

#: modules/elementor/widgets/three-banners.php:231
#: modules/js_composer/config/map.php:7840
#: modules/js_composer/includes/elements/vc-three-banners.php:21
msgid "HURRY UP BEFORE OFFER WILL END"
msgstr ""

#: modules/elementor/widgets/three-banners.php:240
msgid "Additional Class"
msgstr ""

#: modules/elementor/widgets/two-row-products.php:43
#: modules/js_composer/config/map.php:6059
msgid "Two Row Products"
msgstr ""

#: modules/elementor/widgets/two-row-products.php:149
msgid "columns"
msgstr ""

#: modules/elementor/widgets/two-row-products.php:150
msgid "Enter the products columns to display"
msgstr ""

#: modules/elementor/widgets/two-row-products.php:160
msgid "Enter the products columns wide to display"
msgstr ""

#: modules/elementor/widgets/two-row-products.php:233
msgid "Section class"
msgstr ""

#: modules/elementor/widgets/two-row-products.php:242
msgid "Header class"
msgstr ""

#: modules/elementor/widgets/vertical-nav-menu.php:43
msgid "Vertical Nav menu"
msgstr ""

#: modules/elementor/widgets/vertical-nav-menu.php:116
msgid "Enter the action text."
msgstr ""

#: modules/elementor/widgets/vertical-nav-menu.php:126
msgid "Enter the action link."
msgstr ""

#: modules/js_composer/config/map.php:53
msgid "Extra Class for Banner"
msgstr ""

#: modules/js_composer/config/map.php:63
#: modules/js_composer/config/map.php:6307
msgid "Add your extra classes here."
msgstr ""

#: modules/js_composer/config/map.php:73
msgid "Add Ad Block to your page."
msgstr ""

#: modules/js_composer/config/map.php:84 modules/js_composer/config/map.php:131
#: modules/js_composer/config/map.php:7041
msgid "Caption Text"
msgstr ""

#: modules/js_composer/config/map.php:106
msgid "Slider with Ads Block"
msgstr ""

#: modules/js_composer/config/map.php:108
msgid "Add Slider with Ads Block to your page."
msgstr ""

#: modules/js_composer/config/map.php:114
#: modules/js_composer/config/map.php:7024
msgid "Slider"
msgstr ""

#: modules/js_composer/config/map.php:121
#: modules/js_composer/config/map.php:7031
msgid "Ads"
msgstr ""

#: modules/js_composer/config/map.php:148
#: modules/js_composer/config/map.php:466
#: modules/js_composer/config/map.php:1864
#: modules/js_composer/config/map.php:2078
#: modules/js_composer/config/map.php:2344
#: modules/js_composer/config/map.php:2429
#: modules/js_composer/config/map.php:2569
#: modules/js_composer/config/map.php:2877
#: modules/js_composer/config/map.php:3259
#: modules/js_composer/config/map.php:5431
#: modules/js_composer/config/map.php:5979
#: modules/js_composer/config/map.php:6045
#: modules/js_composer/config/map.php:6200
#: modules/js_composer/config/map.php:6636
#: modules/js_composer/config/map.php:6701
#: modules/js_composer/config/map.php:6998
#: modules/js_composer/config/map.php:7060
#: modules/js_composer/config/map.php:7236
#: modules/js_composer/config/map.php:7385
#: modules/js_composer/config/map.php:7571
#: modules/js_composer/config/map.php:7647
#: modules/js_composer/config/map.php:7732
#: modules/js_composer/config/map.php:7845
msgid "Enter class name"
msgstr ""

#: modules/js_composer/config/map.php:161
msgid "Products carousel Banner Vertical Tabs"
msgstr ""

#: modules/js_composer/config/map.php:163
msgid "Add Products carousel with Banner Vertical Tabs to your page."
msgstr ""

#: modules/js_composer/config/map.php:181
msgid "Enter tab title."
msgstr ""

#: modules/js_composer/config/map.php:188
msgid "Enter your banner title here."
msgstr ""

#: modules/js_composer/config/map.php:193
msgid "Tab Sub Title"
msgstr ""

#: modules/js_composer/config/map.php:195
msgid "Enter your banner subtitle here."
msgstr ""

#: modules/js_composer/config/map.php:202
msgid "Enter your banner action text here."
msgstr ""

#: modules/js_composer/config/map.php:209
msgid "Enter your banner action link here."
msgstr ""

#: modules/js_composer/config/map.php:231
#: modules/js_composer/config/map.php:272
#: modules/js_composer/config/map.php:591
#: modules/js_composer/config/map.php:629
#: modules/js_composer/config/map.php:667
#: modules/js_composer/config/map.php:738
#: modules/js_composer/config/map.php:792
#: modules/js_composer/config/map.php:869
#: modules/js_composer/config/map.php:907
#: modules/js_composer/config/map.php:945
#: modules/js_composer/config/map.php:991
#: modules/js_composer/config/map.php:1059
#: modules/js_composer/config/map.php:1084
#: modules/js_composer/config/map.php:1124
#: modules/js_composer/config/map.php:1277
#: modules/js_composer/config/map.php:1310
#: modules/js_composer/config/map.php:1446
#: modules/js_composer/config/map.php:1486
#: modules/js_composer/config/map.php:1694
#: modules/js_composer/config/map.php:1734
#: modules/js_composer/config/map.php:1936
#: modules/js_composer/config/map.php:1976
#: modules/js_composer/config/map.php:2406
#: modules/js_composer/config/map.php:2679
#: modules/js_composer/config/map.php:2712
#: modules/js_composer/config/map.php:2911
#: modules/js_composer/config/map.php:2960
#: modules/js_composer/config/map.php:3051
#: modules/js_composer/config/map.php:3084
#: modules/js_composer/config/map.php:3293
#: modules/js_composer/config/map.php:3326
#: modules/js_composer/config/map.php:3452
#: modules/js_composer/config/map.php:3485
#: modules/js_composer/config/map.php:3611
#: modules/js_composer/config/map.php:3644
#: modules/js_composer/config/map.php:3699
#: modules/js_composer/config/map.php:3714
#: modules/js_composer/config/map.php:3868
#: modules/js_composer/config/map.php:3922
#: modules/js_composer/config/map.php:4345
#: modules/js_composer/config/map.php:4386
#: modules/js_composer/config/map.php:4605
#: modules/js_composer/config/map.php:4646
#: modules/js_composer/config/map.php:4816
#: modules/js_composer/config/map.php:4856
#: modules/js_composer/config/map.php:5001
#: modules/js_composer/config/map.php:5041
#: modules/js_composer/config/map.php:5234
#: modules/js_composer/config/map.php:5287
#: modules/js_composer/config/map.php:5612
#: modules/js_composer/config/map.php:5655
#: modules/js_composer/config/map.php:5873
#: modules/js_composer/config/map.php:5927
#: modules/js_composer/config/map.php:6092
#: modules/js_composer/config/map.php:6148
#: modules/js_composer/config/map.php:6474
#: modules/js_composer/config/map.php:6514
#: modules/js_composer/config/map.php:7113
#: modules/js_composer/config/map.php:7139
#: modules/js_composer/config/map.php:7174
#: modules/js_composer/config/map.php:7301
#: modules/js_composer/config/map.php:7337
#: modules/js_composer/config/map.php:7456
#: modules/js_composer/config/map.php:7473
#: modules/js_composer/config/map.php:7509
#: modules/js_composer/config/map.php:7639
#: modules/js_composer/config/map.php:7715
msgid "Select"
msgstr ""

#: modules/js_composer/config/map.php:282
#: modules/js_composer/config/map.php:802
#: modules/js_composer/config/map.php:1134
#: modules/js_composer/config/map.php:1320
#: modules/js_composer/config/map.php:1496
#: modules/js_composer/config/map.php:1744
#: modules/js_composer/config/map.php:1986
#: modules/js_composer/config/map.php:2722
#: modules/js_composer/config/map.php:2970
#: modules/js_composer/config/map.php:3094
#: modules/js_composer/config/map.php:3336
#: modules/js_composer/config/map.php:3495
#: modules/js_composer/config/map.php:3654
#: modules/js_composer/config/map.php:3932
#: modules/js_composer/config/map.php:4396
#: modules/js_composer/config/map.php:4656
#: modules/js_composer/config/map.php:4866
#: modules/js_composer/config/map.php:5051
#: modules/js_composer/config/map.php:5297
#: modules/js_composer/config/map.php:5665
#: modules/js_composer/config/map.php:5937
#: modules/js_composer/config/map.php:6158
#: modules/js_composer/config/map.php:6524
#: modules/js_composer/config/map.php:7184
#: modules/js_composer/config/map.php:7483
msgid "Enter IDs/SKUs spearate by comma(,)."
msgstr ""

#: modules/js_composer/config/map.php:289
#: modules/js_composer/config/map.php:809
#: modules/js_composer/config/map.php:1141
#: modules/js_composer/config/map.php:1327
#: modules/js_composer/config/map.php:1503
#: modules/js_composer/config/map.php:1751
#: modules/js_composer/config/map.php:1993
#: modules/js_composer/config/map.php:2729
#: modules/js_composer/config/map.php:2977
#: modules/js_composer/config/map.php:3101
#: modules/js_composer/config/map.php:3343
#: modules/js_composer/config/map.php:3502
#: modules/js_composer/config/map.php:3661
#: modules/js_composer/config/map.php:3939
#: modules/js_composer/config/map.php:4403
#: modules/js_composer/config/map.php:4663
#: modules/js_composer/config/map.php:4873
#: modules/js_composer/config/map.php:5058
#: modules/js_composer/config/map.php:5304
#: modules/js_composer/config/map.php:5380
#: modules/js_composer/config/map.php:5672
#: modules/js_composer/config/map.php:5944
#: modules/js_composer/config/map.php:6165
#: modules/js_composer/config/map.php:6531
#: modules/js_composer/config/map.php:7191
#: modules/js_composer/config/map.php:7519
msgid "Enter slug spearate by comma(,)."
msgstr ""

#: modules/js_composer/config/map.php:311
#: modules/js_composer/config/map.php:831
#: modules/js_composer/config/map.php:1163
#: modules/js_composer/config/map.php:1349
#: modules/js_composer/config/map.php:1525
#: modules/js_composer/config/map.php:1773
#: modules/js_composer/config/map.php:2015
#: modules/js_composer/config/map.php:2751
#: modules/js_composer/config/map.php:2999
#: modules/js_composer/config/map.php:3123
#: modules/js_composer/config/map.php:3365
#: modules/js_composer/config/map.php:3524
#: modules/js_composer/config/map.php:3683
#: modules/js_composer/config/map.php:3961
#: modules/js_composer/config/map.php:4425
#: modules/js_composer/config/map.php:4685
#: modules/js_composer/config/map.php:4895
#: modules/js_composer/config/map.php:5080
#: modules/js_composer/config/map.php:5326
#: modules/js_composer/config/map.php:5694
#: modules/js_composer/config/map.php:5966
#: modules/js_composer/config/map.php:6187
#: modules/js_composer/config/map.php:6553
#: modules/js_composer/config/map.php:7213
#: modules/js_composer/config/map.php:7541
msgid "Enter term slug spearate by comma(,)."
msgstr ""

#: modules/js_composer/config/map.php:318
#: modules/js_composer/config/map.php:838
#: modules/js_composer/config/map.php:1170
#: modules/js_composer/config/map.php:1356
#: modules/js_composer/config/map.php:1532
#: modules/js_composer/config/map.php:1780
#: modules/js_composer/config/map.php:2022
#: modules/js_composer/config/map.php:2758
#: modules/js_composer/config/map.php:3006
#: modules/js_composer/config/map.php:3130
#: modules/js_composer/config/map.php:3372
#: modules/js_composer/config/map.php:3531
#: modules/js_composer/config/map.php:3690
#: modules/js_composer/config/map.php:3968
#: modules/js_composer/config/map.php:4432
#: modules/js_composer/config/map.php:4692
#: modules/js_composer/config/map.php:4902
#: modules/js_composer/config/map.php:5087
#: modules/js_composer/config/map.php:5333
#: modules/js_composer/config/map.php:5701
#: modules/js_composer/config/map.php:5973
#: modules/js_composer/config/map.php:6194
#: modules/js_composer/config/map.php:6560
#: modules/js_composer/config/map.php:7220
#: modules/js_composer/config/map.php:7548
msgid "Operator to compare terms. Possible values are 'IN', 'NOT IN', 'AND'."
msgstr ""

#: modules/js_composer/config/map.php:369
#: modules/js_composer/config/map.php:378
#: modules/js_composer/config/map.php:387
#: modules/js_composer/config/map.php:396
#: modules/js_composer/config/map.php:1038
#: modules/js_composer/config/map.php:1188
#: modules/js_composer/config/map.php:1243
#: modules/js_composer/config/map.php:1252
#: modules/js_composer/config/map.php:1261
#: modules/js_composer/config/map.php:1365
#: modules/js_composer/config/map.php:1382
#: modules/js_composer/config/map.php:1541
#: modules/js_composer/config/map.php:1599
#: modules/js_composer/config/map.php:1608
#: modules/js_composer/config/map.php:1617
#: modules/js_composer/config/map.php:1647
#: modules/js_composer/config/map.php:1831
#: modules/js_composer/config/map.php:1840
#: modules/js_composer/config/map.php:1849
#: modules/js_composer/config/map.php:1858
#: modules/js_composer/config/map.php:1899
#: modules/js_composer/config/map.php:2045
#: modules/js_composer/config/map.php:2054
#: modules/js_composer/config/map.php:2063
#: modules/js_composer/config/map.php:2072
#: modules/js_composer/config/map.php:2120
#: modules/js_composer/config/map.php:2152
#: modules/js_composer/config/map.php:2161
#: modules/js_composer/config/map.php:2178
#: modules/js_composer/config/map.php:2187
#: modules/js_composer/config/map.php:2227
#: modules/js_composer/config/map.php:2304
#: modules/js_composer/config/map.php:2388
#: modules/js_composer/config/map.php:2473
#: modules/js_composer/config/map.php:2546
#: modules/js_composer/config/map.php:2555
#: modules/js_composer/config/map.php:2564
#: modules/js_composer/config/map.php:2793
#: modules/js_composer/config/map.php:2802
#: modules/js_composer/config/map.php:2842
#: modules/js_composer/config/map.php:3189
#: modules/js_composer/config/map.php:3213
#: modules/js_composer/config/map.php:3389
#: modules/js_composer/config/map.php:3548
#: modules/js_composer/config/map.php:3741
#: modules/js_composer/config/map.php:3758
#: modules/js_composer/config/map.php:3805
#: modules/js_composer/config/map.php:3985
#: modules/js_composer/config/map.php:4002
#: modules/js_composer/config/map.php:4049
#: modules/js_composer/config/map.php:4237
#: modules/js_composer/config/map.php:4246
#: modules/js_composer/config/map.php:4255
#: modules/js_composer/config/map.php:4264
#: modules/js_composer/config/map.php:4273
#: modules/js_composer/config/map.php:4282
#: modules/js_composer/config/map.php:4312
#: modules/js_composer/config/map.php:4442
#: modules/js_composer/config/map.php:4466
#: modules/js_composer/config/map.php:4521
#: modules/js_composer/config/map.php:4530
#: modules/js_composer/config/map.php:4539
#: modules/js_composer/config/map.php:4548
#: modules/js_composer/config/map.php:4702
#: modules/js_composer/config/map.php:4726
#: modules/js_composer/config/map.php:4920
#: modules/js_composer/config/map.php:4929
#: modules/js_composer/config/map.php:4938
#: modules/js_composer/config/map.php:4971
#: modules/js_composer/config/map.php:5103
#: modules/js_composer/config/map.php:5112
#: modules/js_composer/config/map.php:5121
#: modules/js_composer/config/map.php:5151
#: modules/js_composer/config/map.php:5412
#: modules/js_composer/config/map.php:5536
#: modules/js_composer/config/map.php:5560
#: modules/js_composer/config/map.php:5630
#: modules/js_composer/config/map.php:5745
#: modules/js_composer/config/map.php:5754
#: modules/js_composer/config/map.php:5763
#: modules/js_composer/config/map.php:5797
#: modules/js_composer/config/map.php:5821
#: modules/js_composer/config/map.php:6021
#: modules/js_composer/config/map.php:6030
#: modules/js_composer/config/map.php:6039
#: modules/js_composer/config/map.php:6451
#: modules/js_composer/config/map.php:6600
#: modules/js_composer/config/map.php:6663
#: modules/js_composer/config/map.php:6752
#: modules/js_composer/config/map.php:6761
#: modules/js_composer/config/map.php:6770
#: modules/js_composer/config/map.php:6779
#: modules/js_composer/config/map.php:6832
#: modules/js_composer/config/map.php:6856
#: modules/js_composer/config/map.php:6895
#: modules/js_composer/config/map.php:6912
#: modules/js_composer/config/map.php:6951
#: modules/js_composer/config/map.php:6974
#: modules/js_composer/config/map.php:6983
#: modules/js_composer/config/map.php:6992
#: modules/js_composer/config/map.php:7096
#: modules/js_composer/config/map.php:7230
#: modules/js_composer/config/map.php:7312
#: modules/js_composer/config/map.php:7356
#: modules/js_composer/config/map.php:7557
#: modules/js_composer/config/map.php:7726
msgid "Allow"
msgstr ""

#: modules/js_composer/config/map.php:408
msgid "Ads with Banners Block"
msgstr ""

#: modules/js_composer/config/map.php:410
#: modules/js_composer/config/map.php:7747
msgid "Add Ads with Banner Block to your page."
msgstr ""

#: modules/js_composer/config/map.php:417
msgid "Ads with Banner"
msgstr ""

#: modules/js_composer/config/map.php:459
msgid "Is End"
msgstr ""

#: modules/js_composer/config/map.php:481
msgid "Add Feature Block to your page."
msgstr ""

#: modules/js_composer/config/map.php:487
msgid "Icon 1"
msgstr ""

#: modules/js_composer/config/map.php:492
msgid "Text 1"
msgstr ""

#: modules/js_composer/config/map.php:497
msgid "Icon 2"
msgstr ""

#: modules/js_composer/config/map.php:502
msgid "Text 2"
msgstr ""

#: modules/js_composer/config/map.php:507
msgid "Icon 3"
msgstr ""

#: modules/js_composer/config/map.php:512
msgid "Text 3"
msgstr ""

#: modules/js_composer/config/map.php:517
msgid "Icon 4"
msgstr ""

#: modules/js_composer/config/map.php:522
msgid "Text 4"
msgstr ""

#: modules/js_composer/config/map.php:527
msgid "Icon 5"
msgstr ""

#: modules/js_composer/config/map.php:532
msgid "Text 5"
msgstr ""

#: modules/js_composer/config/map.php:546
msgid "Add Jumbotron to your page."
msgstr ""

#: modules/js_composer/config/map.php:576
#: modules/js_composer/config/map.php:715
msgid "Add Product Tabs to your page."
msgstr ""

#: modules/js_composer/config/map.php:577
#: modules/js_composer/config/map.php:855
msgid "Electro Deprecated Elements"
msgstr ""

#: modules/js_composer/config/map.php:582
#: modules/js_composer/config/map.php:860
msgid "Tab #1 title"
msgstr ""

#: modules/js_composer/config/map.php:588
#: modules/js_composer/config/map.php:866
msgid "Tab #1 Content, Show :"
msgstr ""

#: modules/js_composer/config/map.php:604
#: modules/js_composer/config/map.php:642
#: modules/js_composer/config/map.php:680
#: modules/js_composer/config/map.php:882
#: modules/js_composer/config/map.php:920
#: modules/js_composer/config/map.php:958
msgid "Enter Product IDs"
msgstr ""

#: modules/js_composer/config/map.php:605
#: modules/js_composer/config/map.php:643
#: modules/js_composer/config/map.php:681
#: modules/js_composer/config/map.php:883
#: modules/js_composer/config/map.php:921
#: modules/js_composer/config/map.php:959
msgid "This will only for Products Shortcode"
msgstr ""

#: modules/js_composer/config/map.php:612
#: modules/js_composer/config/map.php:650
#: modules/js_composer/config/map.php:688
#: modules/js_composer/config/map.php:890
#: modules/js_composer/config/map.php:928
#: modules/js_composer/config/map.php:966
msgid "Enter Category Slug"
msgstr ""

#: modules/js_composer/config/map.php:613
#: modules/js_composer/config/map.php:651
#: modules/js_composer/config/map.php:689
#: modules/js_composer/config/map.php:891
#: modules/js_composer/config/map.php:929
#: modules/js_composer/config/map.php:967
msgid "This will only for Product Category Shortcode"
msgstr ""

#: modules/js_composer/config/map.php:620
#: modules/js_composer/config/map.php:898
msgid "Tab #2 title"
msgstr ""

#: modules/js_composer/config/map.php:626
#: modules/js_composer/config/map.php:904
msgid "Tab #2 Content, Show :"
msgstr ""

#: modules/js_composer/config/map.php:658
#: modules/js_composer/config/map.php:936
msgid "Tab #3 title"
msgstr ""

#: modules/js_composer/config/map.php:664
#: modules/js_composer/config/map.php:942
msgid "Tab #3 Content, Show :"
msgstr ""

#: modules/js_composer/config/map.php:696
#: modules/js_composer/config/map.php:974
msgid "Enter Product Items"
msgstr ""

#: modules/js_composer/config/map.php:703
#: modules/js_composer/config/map.php:981
msgid "Enter Product Columns"
msgstr ""

#: modules/js_composer/config/map.php:852
#: modules/js_composer/config/map.php:1047
msgid "Product Carousel Tabs"
msgstr ""

#: modules/js_composer/config/map.php:854
#: modules/js_composer/config/map.php:1049
#: modules/js_composer/config/map.php:4772
msgid "Add Product Carousel Tabs to your page."
msgstr ""

#: modules/js_composer/config/map.php:1200
msgid "Electro Products Cards Carousel"
msgstr ""

#: modules/js_composer/config/map.php:1202
msgid "Add products cards carousel to your page."
msgstr ""

#: modules/js_composer/config/map.php:1241
msgid "Show Carousel Navigation"
msgstr ""

#: modules/js_composer/config/map.php:1425
msgid "Electro Products Carousel"
msgstr ""

#: modules/js_composer/config/map.php:1427
#: modules/js_composer/config/map.php:1661
msgid "Add products carousel to your page."
msgstr ""

#: modules/js_composer/config/map.php:1659
msgid "Electro Products Carousel 1"
msgstr ""

#: modules/js_composer/config/map.php:1793
msgid "Carousel: Items(0-479)"
msgstr ""

#: modules/js_composer/config/map.php:1800
msgid "Carousel: Items(480-767)"
msgstr ""

#: modules/js_composer/config/map.php:1807
msgid "Carousel: Items(768-991)"
msgstr ""

#: modules/js_composer/config/map.php:1814
msgid "Carousel: Items(992-1199)"
msgstr ""

#: modules/js_composer/config/map.php:1878
msgid "Electro Products Carousel With Timer"
msgstr ""

#: modules/js_composer/config/map.php:1880
msgid "Add products carousel with timer to your page."
msgstr ""

#: modules/js_composer/config/map.php:2092
msgid "Electro Brands Carousel"
msgstr ""

#: modules/js_composer/config/map.php:2094
msgid "Add brands carousel to your page."
msgstr ""

#: modules/js_composer/config/map.php:2168
msgid "Carousel: Autoplay Timeout"
msgstr ""

#: modules/js_composer/config/map.php:2169
msgid "Autoplay interval timeout for slides speed"
msgstr ""

#: modules/js_composer/config/map.php:2176
msgid "Carousel: Autoplay Pause on Hover"
msgstr ""

#: modules/js_composer/config/map.php:2199
msgid "Product List Categories"
msgstr ""

#: modules/js_composer/config/map.php:2201
#: modules/js_composer/config/map.php:2271
#: modules/js_composer/config/map.php:2359
#: modules/js_composer/config/map.php:2816
#: modules/js_composer/config/map.php:2893
#: modules/js_composer/config/map.php:3033
#: modules/js_composer/config/map.php:3157
#: modules/js_composer/config/map.php:6574
#: modules/js_composer/config/map.php:6651
msgid "Add product categories to your page."
msgstr ""

#: modules/js_composer/config/map.php:2357
msgid "Product Categories Card Block"
msgstr ""

#: modules/js_composer/config/map.php:2385
msgid "Hide empty products"
msgstr ""

#: modules/js_composer/config/map.php:2386
msgid "Hide Categories does not have products"
msgstr ""

#: modules/js_composer/config/map.php:2396
#: modules/js_composer/config/map.php:7347
msgid ""
" Sort retrieved posts by parameter. Defaults to 'menu_order'. One or more "
"options can be passed"
msgstr ""

#: modules/js_composer/config/map.php:2423
msgid "ID's works when the slugs are empty"
msgstr ""

#: modules/js_composer/config/map.php:2442
msgid "Product Category Icons Carousel Block"
msgstr ""

#: modules/js_composer/config/map.php:2444
msgid "Add product category icons carousel to your page."
msgstr ""

#: modules/js_composer/config/map.php:2452
msgid "Style"
msgstr ""

#: modules/js_composer/config/map.php:2454
msgid "Style v1"
msgstr ""

#: modules/js_composer/config/map.php:2455
msgid "Style v2"
msgstr ""

#: modules/js_composer/config/map.php:2582
msgid "Product Carousel Tabs with Deal"
msgstr ""

#: modules/js_composer/config/map.php:2584
msgid "Add Product Carousel Tabs with deal to your page."
msgstr ""

#: modules/js_composer/config/map.php:2612
msgid "Enter Deal title"
msgstr ""

#: modules/js_composer/config/map.php:2622
#: modules/js_composer/config/map.php:4123
#: modules/js_composer/config/map.php:4186
#: modules/js_composer/config/map.php:5182
msgid "Show Savings"
msgstr ""

#: modules/js_composer/config/map.php:3155
msgid "Product Catgories List With Header Image "
msgstr ""

#: modules/js_composer/config/map.php:3172
msgid "Enter Subtitle"
msgstr ""

#: modules/js_composer/config/map.php:3272
msgid "Product 2-1-2 Grid"
msgstr ""

#: modules/js_composer/config/map.php:3274
#: modules/js_composer/config/map.php:3433
msgid "Add products to your page."
msgstr ""

#: modules/js_composer/config/map.php:3431
msgid "Product 6-1 Grid"
msgstr ""

#: modules/js_composer/config/map.php:3590
msgid "Products 6-1 with Categories"
msgstr ""

#: modules/js_composer/config/map.php:3592
#: modules/js_composer/config/map.php:4326
msgid "Add products 6-1 with vertical categories to your page."
msgstr ""

#: modules/js_composer/config/map.php:3718
msgid "Only for Products Shortcode."
msgstr ""

#: modules/js_composer/config/map.php:3847
msgid "Products with Categories and Image"
msgstr ""

#: modules/js_composer/config/map.php:3849
msgid "Add products with vertical categories and image to your page."
msgstr ""

#: modules/js_composer/config/map.php:4104
msgid "Electro Onsale Product"
msgstr ""

#: modules/js_composer/config/map.php:4106
msgid "Add onsale product to your page."
msgstr ""

#: modules/js_composer/config/map.php:4166
msgid "Electro Onsale Products Carousel"
msgstr ""

#: modules/js_composer/config/map.php:4168
#: modules/js_composer/config/map.php:6716
msgid "Add onsale products carousel to your page."
msgstr ""

#: modules/js_composer/config/map.php:4324
msgid "Electro Products Carousel with Category Tabs"
msgstr ""

#: modules/js_composer/config/map.php:4560
msgid "Electro Products List Block"
msgstr ""

#: modules/js_composer/config/map.php:4562
msgid "Add Products list to your page."
msgstr ""

#: modules/js_composer/config/map.php:4770
msgid "Product Carousel Tabs 1"
msgstr ""

#: modules/js_composer/config/map.php:4950
msgid "Electro Deal Products Carousel"
msgstr ""

#: modules/js_composer/config/map.php:4952
msgid "Add deal products carousel to your page."
msgstr ""

#: modules/js_composer/config/map.php:5163
msgid "Electro Deal and Products Tabs"
msgstr ""

#: modules/js_composer/config/map.php:5165
msgid "Add deal and products tabs to your page."
msgstr ""

#: modules/js_composer/config/map.php:5353
#: modules/js_composer/config/map.php:5367
msgid "List Categories Menus"
msgstr ""

#: modules/js_composer/config/map.php:5355
msgid "Add List Categories Menus to your page."
msgstr ""

#: modules/js_composer/config/map.php:5378
msgid "Category Slug"
msgstr ""

#: modules/js_composer/config/map.php:5401
msgid "Number of categories to display"
msgstr ""

#: modules/js_composer/config/map.php:5444
msgid "Electro Nav Menu"
msgstr ""

#: modules/js_composer/config/map.php:5446
msgid "Add a navigation menu to your page."
msgstr ""

#: modules/js_composer/config/map.php:5474
msgid "Recently Viewed Products Block"
msgstr ""

#: modules/js_composer/config/map.php:5476
msgid "Add Recently Viewed Products Block to your page."
msgstr ""

#: modules/js_composer/config/map.php:5514
msgid "Products Carousel Category with Image"
msgstr ""

#: modules/js_composer/config/map.php:5516
msgid "Add products carousel category with image to your page."
msgstr ""

#: modules/js_composer/config/map.php:5627
msgid "Enable Description"
msgstr ""

#: modules/js_composer/config/map.php:5628
msgid "Show Description on the products."
msgstr ""

#: modules/js_composer/config/map.php:5775
msgid "Products Category with Image"
msgstr ""

#: modules/js_composer/config/map.php:5777
msgid "Add products category with image to your page."
msgstr ""

#: modules/js_composer/config/map.php:5993
msgid "Electro Recent Viewed Products Carousel"
msgstr ""

#: modules/js_composer/config/map.php:5995
msgid "Add recent viewed products carousel to your page."
msgstr ""

#: modules/js_composer/config/map.php:6061
msgid "Add two row products to your page."
msgstr ""

#: modules/js_composer/config/map.php:6207
msgid "Enter Section Header Class"
msgstr ""

#: modules/js_composer/config/map.php:6220
msgid "Electro Vertical Nav Menu"
msgstr ""

#: modules/js_composer/config/map.php:6222
msgid "Add a verical navigation menu to your page."
msgstr ""

#: modules/js_composer/config/map.php:6260
msgid "Electro Team Member"
msgstr ""

#: modules/js_composer/config/map.php:6262
msgid "Add a team member profile to your page."
msgstr ""

#: modules/js_composer/config/map.php:6318
msgid "Electro Terms"
msgstr ""

#: modules/js_composer/config/map.php:6320
msgid ""
"Adds a shortcode for get_terms. Used to get terms including categories, "
"product categories, etc."
msgstr ""

#: modules/js_composer/config/map.php:6328
msgid "Taxonomy"
msgstr ""

#: modules/js_composer/config/map.php:6330
msgid ""
"Taxonomy name, or comma-separated taxonomies, to which results should be "
"limited."
msgstr ""

#: modules/js_composer/config/map.php:6338
msgid ""
"Field(s) to order terms by. Accepts term fields ('name', 'slug', "
"'term_group', 'term_id', 'id', 'description'). Defaults to 'name'."
msgstr ""

#: modules/js_composer/config/map.php:6345
msgid ""
"Whether to order terms in ascending or descending order. Accepts 'ASC' "
"(ascending) or 'DESC' (descending). Default 'ASC'."
msgstr ""

#: modules/js_composer/config/map.php:6350
msgid "Hide Empty ?"
msgstr ""

#: modules/js_composer/config/map.php:6352
msgid "Whether to hide terms not assigned to any posts. Accepts 1 or 0. Default 0."
msgstr ""

#: modules/js_composer/config/map.php:6357
msgid "Include IDs"
msgstr ""

#: modules/js_composer/config/map.php:6359
msgid "Comma-separated string of term ids to include."
msgstr ""

#: modules/js_composer/config/map.php:6363
msgid "Exclude IDs"
msgstr ""

#: modules/js_composer/config/map.php:6365
msgid ""
"Comma-separated string of term ids to exclude. If Include is non-empty, "
"Exclude is ignored."
msgstr ""

#: modules/js_composer/config/map.php:6369
msgid "Number"
msgstr ""

#: modules/js_composer/config/map.php:6371
msgid ""
"Maximum number of terms to return. Accepts 0 (all) or any positive number. "
"Default 0 (all)."
msgstr ""

#: modules/js_composer/config/map.php:6376
msgid "Offset"
msgstr ""

#: modules/js_composer/config/map.php:6378
msgid "The number by which to offset the terms query."
msgstr ""

#: modules/js_composer/config/map.php:6383
msgid "Name"
msgstr ""

#: modules/js_composer/config/map.php:6385
msgid "Name or comma-separated string of names to return term(s) for."
msgstr ""

#: modules/js_composer/config/map.php:6389
msgid "Slug"
msgstr ""

#: modules/js_composer/config/map.php:6391
msgid "Slug or comma-separated string of slugs to return term(s) for."
msgstr ""

#: modules/js_composer/config/map.php:6395
msgid "Hierarchical"
msgstr ""

#: modules/js_composer/config/map.php:6397
msgid ""
"Whether to include terms that have non-empty descendants. Accepts 1 (true) "
"or 0 (false). Default 1 (true)"
msgstr ""

#: modules/js_composer/config/map.php:6402
msgid "Child Of"
msgstr ""

#: modules/js_composer/config/map.php:6404
msgid ""
"Term ID to retrieve child terms of. If multiple taxonomies are passed, "
"child_of is ignored. Default 0."
msgstr ""

#: modules/js_composer/config/map.php:6409
msgid "Include \"Child Of\" term ?"
msgstr ""

#: modules/js_composer/config/map.php:6411
msgid ""
"Include \"Child Of\" term in the terms list. Accepts 1 (yes) or 0 (no). "
"Default 1."
msgstr ""

#: modules/js_composer/config/map.php:6416
msgid "Parent"
msgstr ""

#: modules/js_composer/config/map.php:6418
msgid "Parent term ID to retrieve direct-child terms of."
msgstr ""

#: modules/js_composer/config/map.php:6432
msgid "Add deal product with featured to your page."
msgstr ""

#: modules/js_composer/config/map.php:6582
msgid "Enter section title"
msgstr ""

#: modules/js_composer/config/map.php:6649
msgid "Product Categories 1-6"
msgstr ""

#: modules/js_composer/config/map.php:6714
msgid "Electro Onsale Products Carousel 2"
msgstr ""

#: modules/js_composer/config/map.php:6791
msgid "Banners 1-6 Block"
msgstr ""

#: modules/js_composer/config/map.php:6793
msgid "Add banners to your page."
msgstr ""

#: modules/js_composer/config/map.php:6853
msgid "Categories List 1: Have no products"
msgstr ""

#: modules/js_composer/config/map.php:6870
msgid "Categories List 1: Order"
msgstr ""

#: modules/js_composer/config/map.php:6909
msgid "Categories List 2: Have no products"
msgstr ""

#: modules/js_composer/config/map.php:6926
msgid "Categories List 2: Order"
msgstr ""

#: modules/js_composer/config/map.php:7011
msgid "Slider with Ads Block V2"
msgstr ""

#: modules/js_composer/config/map.php:7013
msgid "Add Slider with Ads Block Version-2 to your page."
msgstr ""

#: modules/js_composer/config/map.php:7043
#: modules/js_composer/includes/elements/vc-slider-with-ads-block-v2.php:40
msgid "Catch Big <strong>Deals</strong> on<br>The Consoles"
msgstr ""

#: modules/js_composer/config/map.php:7049
#: modules/js_composer/includes/elements/vc-slider-with-ads-block-v2.php:41
msgid "Shop now"
msgstr ""

#: modules/js_composer/config/map.php:7075
msgid "Sidebar with Products Block"
msgstr ""

#: modules/js_composer/config/map.php:7077
msgid "Add Sidebar with Products Block to your page."
msgstr ""

#: modules/js_composer/config/map.php:7084
msgid "Enter Sidebar title"
msgstr ""

#: modules/js_composer/config/map.php:7087
#: modules/js_composer/includes/elements/vc-sidebar-with-products.php:8
msgid "Assortment"
msgstr ""

#: modules/js_composer/config/map.php:7094
msgid "Show Sidebar block."
msgstr ""

#: modules/js_composer/config/map.php:7105
#: modules/js_composer/includes/elements/vc-sidebar-with-products.php:10
msgid "Hot Products Today"
msgstr ""

#: modules/js_composer/config/map.php:7163
msgid "Wide Columns"
msgstr ""

#: modules/js_composer/config/map.php:7228
msgid "Show Pagination block."
msgstr ""

#: modules/js_composer/config/map.php:7252
msgid "Add Brands With Category Block to your page."
msgstr ""

#: modules/js_composer/config/map.php:7258
msgid "Brand Title"
msgstr ""

#: modules/js_composer/config/map.php:7261
#: modules/js_composer/includes/elements/vc-brands-with-category-block.php:7
msgid "Brands:"
msgstr ""

#: modules/js_composer/config/map.php:7266
msgid "More Brands Text"
msgstr ""

#: modules/js_composer/config/map.php:7269
#: modules/js_composer/includes/elements/vc-brands-with-category-block.php:8
msgid "+ More Brands"
msgstr ""

#: modules/js_composer/config/map.php:7274
msgid "More Brands URL"
msgstr ""

#: modules/js_composer/config/map.php:7281
msgid "Brand Limit"
msgstr ""

#: modules/js_composer/config/map.php:7283
msgid "Enter the brands to display."
msgstr ""

#: modules/js_composer/config/map.php:7289
msgid "Brand Order by"
msgstr ""

#: modules/js_composer/config/map.php:7297
msgid "Brand Order"
msgstr ""

#: modules/js_composer/config/map.php:7310
#: modules/js_composer/config/map.php:7724
msgid "Brand Hide Empty ?"
msgstr ""

#: modules/js_composer/config/map.php:7318
msgid "Arrow Icon"
msgstr ""

#: modules/js_composer/config/map.php:7333
msgid "Category Order"
msgstr ""

#: modules/js_composer/config/map.php:7345
msgid "Category Order by"
msgstr ""

#: modules/js_composer/config/map.php:7354
msgid "Category Hide Empty ?"
msgstr ""

#: modules/js_composer/config/map.php:7362
msgid "Category ID's"
msgstr ""

#: modules/js_composer/config/map.php:7364
msgid "Enter ID spearate by comma(,)."
msgstr ""

#: modules/js_composer/config/map.php:7369
msgid "Category Limit"
msgstr ""

#: modules/js_composer/config/map.php:7371
msgid "Enter the number of categories to display."
msgstr ""

#: modules/js_composer/config/map.php:7377
msgid "More Child Text"
msgstr ""

#: modules/js_composer/config/map.php:7401
msgid "Add Banner With Products Carousel to your page."
msgstr ""

#: modules/js_composer/config/map.php:7453
msgid "Products Content"
msgstr ""

#: modules/js_composer/config/map.php:7481
msgid "Choose Products"
msgstr ""

#: modules/js_composer/config/map.php:7489
msgid "Products Limit"
msgstr ""

#: modules/js_composer/config/map.php:7588
msgid "Add blog posts to your page."
msgstr ""

#: modules/js_composer/config/map.php:7664
msgid "Add Brands to your page."
msgstr ""

#: modules/js_composer/config/map.php:7745
msgid "Three Banners Block"
msgstr ""

#: modules/js_composer/config/map.php:7754
msgid "Banner 1 Background Image"
msgstr ""

#: modules/js_composer/config/map.php:7760
msgid "Banner 1 Image"
msgstr ""

#: modules/js_composer/config/map.php:7766
msgid "Banner 1 Link"
msgstr ""

#: modules/js_composer/config/map.php:7772
msgid "Banner 1 Description"
msgstr ""

#: modules/js_composer/config/map.php:7779
msgid "Banner 2 Background Image"
msgstr ""

#: modules/js_composer/config/map.php:7785
msgid "Banner 2 Link"
msgstr ""

#: modules/js_composer/config/map.php:7791
msgid "Banner 2 Description"
msgstr ""

#: modules/js_composer/config/map.php:7798
msgid "Banner 2 Before Price Text"
msgstr ""

#: modules/js_composer/config/map.php:7805
msgid "Banner 2 Price Text"
msgstr ""

#: modules/js_composer/config/map.php:7812
msgid "Banner 3 Background Image"
msgstr ""

#: modules/js_composer/config/map.php:7818
msgid "Banner 3 Link"
msgstr ""

#: modules/js_composer/config/map.php:7824
msgid "Banner 3 Title"
msgstr ""

#: modules/js_composer/config/map.php:7831
msgid "Banner 3 Subtitle"
msgstr ""

#: modules/js_composer/config/map.php:7838
msgid "Banner 3 Description"
msgstr ""

#: modules/mas-static-content-migrator/index.php:69
msgid "MAS Static Content Migrate"
msgstr ""

#: modules/mas-static-content-migrator/index.php:69
msgid "This process will move all posts from Static Blocks to MAS Static Contents."
msgstr ""

#: modules/mas-static-content-migrator/index.php:70
msgid "Run the updater"
msgstr ""

#: modules/mas-static-content-migrator/index.php:74
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""

#: modules/mas-static-content-migrator/index.php:81
msgid "MAS Static Content posts migrated."
msgstr ""

#: modules/mas-static-content-migrator/index.php:83
msgid "MAS Static Content posts migration failed. Please try again."
msgstr ""

#: modules/post-formats/post-formats.php:70
msgid "Loading..."
msgstr ""

#: modules/post-formats/views/format-audio.php:6
msgid "Audio Options"
msgstr ""

#: modules/post-formats/views/format-audio.php:10
msgid "Enter the paths to your audio files in the fields below"
msgstr ""

#: modules/post-formats/views/format-audio.php:17
msgid "MP3 File URL"
msgstr ""

#: modules/post-formats/views/format-audio.php:18
msgid "URL to an .mp3 file"
msgstr ""

#: modules/post-formats/views/format-audio.php:28
msgid "OGA File URL"
msgstr ""

#: modules/post-formats/views/format-audio.php:29
msgid "URL to an .oga or .ogg file"
msgstr ""

#: modules/post-formats/views/format-audio.php:39
msgid "Embedded Audio"
msgstr ""

#: modules/post-formats/views/format-audio.php:40
msgid "Add embedded audio formats."
msgstr ""

#: modules/post-formats/views/format-gallery.php:25
msgid "Edit Gallery"
msgstr ""

#: modules/post-formats/views/format-gallery.php:25
msgid "Add Images to Gallery"
msgstr ""

#: modules/post-formats/views/format-link.php:5
#: modules/post-formats/views/format-link.php:12
msgid "Link URL"
msgstr ""

#: modules/post-formats/views/format-link.php:13
msgid "The URL of your link."
msgstr ""

#: modules/post-formats/views/format-quote.php:5
#: modules/post-formats/views/format-quote.php:14
msgid "Quote"
msgstr ""

#: modules/post-formats/views/format-quote.php:15
msgid "The text being quoted."
msgstr ""

#: modules/post-formats/views/format-quote.php:25
msgid "Source Name"
msgstr ""

#: modules/post-formats/views/format-quote.php:26
msgid "The person being cited."
msgstr ""

#: modules/post-formats/views/format-video.php:5
msgid "Video Options"
msgstr ""

#: modules/post-formats/views/format-video.php:7
msgid ""
"For HTML5 video support and Flash fallback please include an M4V file. "
"Include an OGV file optionally to increase cross browser support."
msgstr ""

#: modules/post-formats/views/format-video.php:13
msgid "M4V File URL"
msgstr ""

#: modules/post-formats/views/format-video.php:14
msgid "The URL to the .m4v video file"
msgstr ""

#: modules/post-formats/views/format-video.php:24
msgid "OGV File URL"
msgstr ""

#: modules/post-formats/views/format-video.php:25
msgid "The URL to the .ogv video file"
msgstr ""

#: modules/post-formats/views/format-video.php:35
msgid "WEBM File URL"
msgstr ""

#: modules/post-formats/views/format-video.php:36
msgid "The URL to the .webm video file"
msgstr ""

#: modules/post-formats/views/format-video.php:46
msgid "Video Poster"
msgstr ""

#: modules/post-formats/views/format-video.php:47
msgid "A preivew image."
msgstr ""

#: modules/post-formats/views/format-video.php:57
msgid "Embedded Code"
msgstr ""

#: modules/post-formats/views/format-video.php:58
msgid "If not using self hosted video you can include embeded code here."
msgstr ""

#: modules/post-formats/views/tabs.php:9
msgid "Standard"
msgstr ""

#: modules/post-types/static-block.php:51
msgid "Add New Block"
msgstr ""

#: modules/post-types/static-block.php:52
msgid "Edit Block"
msgstr ""

#: modules/post-types/static-block.php:53
msgid "New Block"
msgstr ""

#: modules/post-types/static-block.php:54
msgid "Static Blocks"
msgstr ""

#: modules/post-types/static-block.php:55
msgid "View Block"
msgstr ""

#: modules/post-types/static-block.php:56
msgid "Search"
msgstr ""

#: modules/post-types/static-block.php:57
msgid "No blocks found"
msgstr ""

#: modules/post-types/static-block.php:58
msgid "No blocks found in Trash"
msgstr ""

#: modules/post-types/static-block.php:202
msgid "Content Options"
msgstr ""

#: modules/post-types/static-block.php:208
msgid "Content Filters"
msgstr ""

#: modules/post-types/static-block.php:209
msgid "Apply all WP content filters? This will include plugin added filters."
msgstr ""

#: modules/post-types/static-block.php:214
msgid "Defaults (recommended)"
msgstr ""

#: modules/post-types/static-block.php:215
msgid "All Content Filters"
msgstr ""

#: modules/post-types/static-block.php:219
msgid "Auto Paragraphs"
msgstr ""

#: modules/post-types/static-block.php:220
msgid ""
"Add &lt;p&gt; and &lt;br&gt; tags automatically. (disabling may fix layout "
"issues)"
msgstr ""

#: modules/post-types/static-block.php:225
msgid "On"
msgstr ""

#: modules/post-types/static-block.php:226
msgid "Off"
msgstr ""

#: modules/theme-shortcodes/theme-shortcodes.php:19
msgid "You need to enable YITH Compare plugin for product comparison to work"
msgstr ""

#. Plugin Name of the plugin/theme
msgid "Electro Extensions"
msgstr ""

#. Plugin URI of the plugin/theme
msgid "https://electro.madrasthemes.com/"
msgstr ""

#. Description of the plugin/theme
msgid ""
"This selection of extensions compliment our lean and mean theme for "
"WooCommerce, Electro. Please note: they don’t work with any WordPress "
"theme, just Electro."
msgstr ""

#. Author of the plugin/theme
msgid "MadrasThemes"
msgstr ""

#: modules/post-types/static-block.php:48
msgctxt "post type general name"
msgid "Static Content Blocks"
msgstr ""

#: modules/post-types/static-block.php:49
msgctxt "post type singular name"
msgid "Static Block"
msgstr ""

#: modules/post-types/static-block.php:50
msgctxt "block"
msgid "Add New"
msgstr ""