# This file was generated by WPM<PERSON>
# WPML is a WordPress plugin that can turn any WordPress site into a full featured multilingual content management system.
# https://wpml.org
msgid ""
msgstr ""
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Project-Id-Version: WPML_EXPORT\n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: en\n"
"MIME-Version: 1.0\n"
"X-Generator: Poedit 2.0.4\n"

# <div class="wrap duplicate_page_settings">
# <h1><?php _e('Duplicate Page Settings', 'duplicate-page')?></h1>
# <?php $duplicatepageoptions = array();
msgid "Duplicate Page Settings"
msgstr ""

# if(isset($_POST['submit_duplicate_page']) && wp_verify_nonce( $_POST['duplicatepage_nonce_field'], 'duplicatepage_action' )):
# _e("<strong>Saving Please wait...</strong>", 'duplicate-page');
# $needToUnset = array('submit_duplicate_page');//no need to save in Database
msgid "<strong>Saving Please wait...</strong>"
msgstr ""

# if(!empty($msg) && $msg == 1):
# _e( '<div class="updated settings-error notice is-dismissible" id="setting-error-settings_updated">
# <p><strong>Settings saved.</strong></p><button class="notice-dismiss" type="button"><span class="screen-reader-text">Dismiss this notice.</span></button></div>', 'duplicate-page');
msgid ""
"<div class=\"updated settings-error notice is-dismissible\" id=\"setting-error-settings_updated\"> \r\n"
"<p><strong>Settings saved.</strong></p><button class=\"notice-dismiss\" type=\"button\"><span class=\"screen-reader-text\">Dismiss this notice.</span></button></div>"
msgstr ""

# elseif(!empty($msg) && $msg == 2):
# _e( '<div class="error settings-error notice is-dismissible" id="setting-error-settings_updated">
# <p><strong>Settings not saved.</strong></p><button class="notice-dismiss" type="button"><span class="screen-reader-text">Dismiss this notice.</span></button></div>', 'duplicate-page');
msgid ""
"<div class=\"error settings-error notice is-dismissible\" id=\"setting-error-settings_updated\"> \r\n"
"<p><strong>Settings not saved.</strong></p><button class=\"notice-dismiss\" type=\"button\"><span class=\"screen-reader-text\">Dismiss this notice.</span></button></div>"
msgstr ""

# <tr>
# <th scope="row"><label for="duplicate_post_status"><?php _e('Duplicate Post Status', 'duplicate-page')?></label></th>
# <td><select id="duplicate_post_status" name="duplicate_post_status">
msgid "Duplicate Post Status"
msgstr ""

# </select>
# <p><?php _e('Please select any post status you want to assign for duplicate post. <strong>Default:</strong> Draft.', 'duplicate-page')?></p>
# </td>
msgid "Please select any post status you want to assign for duplicate post. <strong>Default:</strong> Draft."
msgstr ""

# <tr>
# <th scope="row"><label for="duplicate_post_redirect"><?php _e('Redirect to after click on <strong>Duplicate This Link</strong>', 'duplicate-page')?></label></th>
# <td><select id="duplicate_post_redirect" name="duplicate_post_redirect">
msgid "Redirect to after click on <strong>Duplicate This Link</strong>"
msgstr ""

# <td><select id="duplicate_post_redirect" name="duplicate_post_redirect">
# <option value="to_list" <?php echo($opt['duplicate_post_redirect'] == 'to_list' ) ? "selected = 'selected'" : ""; ?>><?php _e('To All Posts List', 'duplicate-page')?></option>
# <option value="to_page" <?php echo($opt['duplicate_post_redirect'] == 'to_page' ) ? "selected = 'selected'" : ""; ?>><?php _e('To Duplicate Edit Screen', 'duplicate-page')?></option>
msgid "To All Posts List"
msgstr ""

# <option value="to_list" <?php echo($opt['duplicate_post_redirect'] == 'to_list' ) ? "selected = 'selected'" : ""; ?>><?php _e('To All Posts List', 'duplicate-page')?></option>
# <option value="to_page" <?php echo($opt['duplicate_post_redirect'] == 'to_page' ) ? "selected = 'selected'" : ""; ?>><?php _e('To Duplicate Edit Screen', 'duplicate-page')?></option>
# </select>
msgid "To Duplicate Edit Screen"
msgstr ""

# </select>
# <p><?php _e('Please select any post redirection, redirect you to selected after click on duplicate this link. <strong>Default:</strong> To current list.', 'duplicate-page')?></p>
# </td>
msgid "Please select any post redirection, redirect you to selected after click on duplicate this link. <strong>Default:</strong> To current list."
msgstr ""

# <tr>
# <th scope="row"><label for="duplicate_post_suffix"><?php _e('Duplicate Post Suffix', 'duplicate-page')?></label></th>
# <td>
msgid "Duplicate Post Suffix"
msgstr ""

# <input type="text" class="regular-text" value="<?php echo !empty($opt['duplicate_post_suffix']) ? $opt['duplicate_post_suffix'] : ''?>" id="duplicate_post_suffix" name="duplicate_post_suffix">
# <p><?php _e('Add a suffix for duplicate or clone post as Copy, Clone etc. It will show after title.', 'duplicate-page')?></p>
# </td>
msgid "Add a suffix for duplicate or clone post as Copy, Clone etc. It will show after title."
msgstr ""

# <div id="submitdiv" class="postbox" style="padding: 6px;">
# <p><strong style="color:#F00"><?php _e('Contribute some donation, to make plugin more stable. You can pay amount of your choice.', 'duplicate-page')?></strong></p>
# <form name="_xclick" action="https://www.paypal.com/yt/cgi-bin/webscr" method="post">
msgid "Contribute some donation, to make plugin more stable. You can pay amount of your choice."
msgstr ""

# if ( $file == plugin_basename( __FILE__ ) ) {
# $duplicate_page_links = '<a href="'.get_admin_url().'options-general.php?page=duplicate_page_settings">'.__('Settings', 'duplicate-page').'</a>';
# $duplicate_page_donate = '<a href="http://www.webdesi9.com/donate/?plugin=duplicate-page" title="Donate Now" target="_blank" style="font-weight:bold">'.__('Donate', 'duplicate-page').'</a>';
msgid "Settings"
msgstr ""

# $duplicate_page_links = '<a href="'.get_admin_url().'options-general.php?page=duplicate_page_settings">'.__('Settings', 'duplicate-page').'</a>';
# $duplicate_page_donate = '<a href="http://www.webdesi9.com/donate/?plugin=duplicate-page" title="Donate Now" target="_blank" style="font-weight:bold">'.__('Donate', 'duplicate-page').'</a>';
# array_unshift( $links, $duplicate_page_donate );
msgid "Donate"
msgstr ""

# public function duplicate_page_options_page(){
# add_options_page( __( 'Duplicate Page', 'duplicate-page' ), __( 'Duplicate Page', 'duplicate-page' ), 'manage_options', 'duplicate_page_settings',array(&$this, 'duplicate_page_settings'));
# }
msgid "Duplicate Page"
msgstr ""

# if (current_user_can('edit_posts')) {
# $actions['duplicate'] = '<a href="admin.php?action=dt_duplicate_post_as_draft&amp;post=' . $post->ID . '" title="Duplicate this as '.$post_status.'" rel="permalink">'.__( "Duplicate This", "duplicate-page" ).'</a>';
# }
msgid "Duplicate This"
msgstr ""
