# ddev generic/default/php config for apache2

#ddev-generated
# If you want to take over this file and customize it, remove the line above
# and ddev will respect it and won't overwrite the file.
# See https://ddev.readthedocs.io/en/stable/users/extend/customization-extendibility/#custom-apache-configuration
<VirtualHost *:80>
    RewriteEngine On
    RewriteCond %{HTTP:X-Forwarded-Proto} =https
    RewriteCond    %{DOCUMENT_ROOT}%{REQUEST_FILENAME} -d
    RewriteRule    ^(.+[^/])$           https://%{HTTP_HOST}$1/ [redirect,last]

    SetEnvIf X-Forwarded-Proto "https" HTTPS=on

    ServerAdmin webmaster@localhost
    DocumentRoot /var/www/html/public
    <Directory "/var/www/html/public/">
      AllowOverride All
      Allow from All
    </Directory>
    # Available loglevels: trace8, ..., trace1, debug, info, notice, warn,
    # error, crit, alert, emerg.
    # It is also possible to configure the loglevel for particular
    # modules, e.g.
    #LogLevel info ssl:warn

    ErrorLog /dev/stdout
    CustomLog ${APACHE_LOG_DIR}/access.log combined

    # For most configuration files from conf-available/, which are
    # enabled or disabled at a global level, it is possible to
    # include a line for only one particular virtual host. For example the
    # following line enables the CGI configuration for this host only
    # after it has been globally disabled with "a2disconf".
    #Include conf-available/serve-cgi-bin.conf

    # Increase allowed field size for large cookies header.
    LimitRequestFieldSize 16380

    # Simple ddev technique to get a phpstatus
    Alias "/phpstatus" "/var/www/phpstatus.php"
    Alias "/xhprof" "/var/xhprof/xhprof_html"
    <Directory "/var/xhprof">
        Options Indexes
        AllowOverride None
        Require all granted
    </Directory>

</VirtualHost>

<VirtualHost *:443>
    SSLEngine on
    SSLCertificateFile /etc/ssl/certs/master.crt
    SSLCertificateKeyFile /etc/ssl/certs/master.key

    # Workaround from https://mail-archives.apache.org/mod_mbox/httpd-users/201403.mbox/%<EMAIL>%3E
    # See also https://gist.github.com/nurtext/b6ac07ac7d8c372bc8eb

    RewriteEngine On
    RewriteCond %{HTTP:X-Forwarded-Proto} =https
    RewriteCond    %{DOCUMENT_ROOT}%{REQUEST_FILENAME} -d
    RewriteRule    ^(.+[^/])$           https://%{HTTP_HOST}$1/ [redirect,last]

    SetEnvIf X-Forwarded-Proto "https" HTTPS=on

    ServerAdmin webmaster@localhost
    DocumentRoot /var/www/html/public
    <Directory "/var/www/html/public/">
      AllowOverride All
      Allow from All
    </Directory>
    # Available loglevels: trace8, ..., trace1, debug, info, notice, warn,
    # error, crit, alert, emerg.
    # It is also possible to configure the loglevel for particular
    # modules, e.g.
    #LogLevel info ssl:warn

    ErrorLog /dev/stdout
    CustomLog ${APACHE_LOG_DIR}/access.log combined

    # Increase allowed field size for large cookies header.
    LimitRequestFieldSize 16380

    # For most configuration files from conf-available/, which are
    # enabled or disabled at a global level, it is possible to
    # include a line for only one particular virtual host. For example the
    # following line enables the CGI configuration for this host only
    # after it has been globally disabled with "a2disconf".
    #Include conf-available/serve-cgi-bin.conf
    # Simple ddev technique to get a phpstatus
    Alias "/phpstatus" "/var/www/phpstatus.php"
    Alias "/xhprof" "/var/xhprof/xhprof_html"
    <Directory "/var/xhprof">
        Options Indexes
        AllowOverride None
        Require all granted
    </Directory>

</VirtualHost>
# vim: syntax=apache ts=4 sw=4 sts=4 sr noet
