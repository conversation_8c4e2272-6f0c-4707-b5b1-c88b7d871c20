#ddev-generated
The .ddev/nginx_full directory contains a generated nginx-site.conf file
for the specific project type chosen in .ddev/config.yaml.
which handles most projects on ddev, including those with multiple
hostnames, etc.

However, if you have very specific needs for configuration, you can edit
the nginx-site.conf file and remove the #ddev-generated line in it and change
as you see fit. Use `ddev start` to restart.

You can also add more configurations, for example with separate configurations
for each site, as demonstrated by the second_docroot.conf.example, which shows
how to have nginx serve completely different configurations for a named site
that is different from the default.
