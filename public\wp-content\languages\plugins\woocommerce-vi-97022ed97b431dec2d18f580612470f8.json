{"translation-revision-date": "2024-11-20 18:08:48+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "vi_VN"}, "Align the last block to the bottom.": ["<PERSON><PERSON>n chỉnh khối cuối cùng xuống dưới cùng."], "An error has prevented the block from being updated.": ["<PERSON><PERSON> xảy ra lỗi ngăn khối đ<PERSON><PERSON><PERSON> cập nh<PERSON>t."], "Align the last block to the bottom": ["Căn chỉnh khối cuối cùng xuống dưới cùng"], "Stock status \"%s\" hidden.": ["Tình trạng kho \"%s\" bị ẩn."], "Stock status \"%s\" visible.": ["Tình trạng kho \"%s\" hiển thị."], "Edit selected attribute": ["Chỉnh sửa thu<PERSON><PERSON> t<PERSON>h đã chọn"], "%d term": ["%d thu<PERSON><PERSON> t<PERSON>h"], "%1$s, has %2$d product": ["%1$s, có %2$d sản phẩm"], "%1$s, has %2$d term": ["%1$s, có %2$d thuật ngữ"], "Loading…": ["<PERSON><PERSON>…"], "The last inner block will follow other content.": ["Block cuối cùng sẽ dựa theo nội dung khác."], "The following error was returned": ["Lỗi sau đã được trả về"], "The following error was returned from the API": ["Lỗi sau là do từ đoạn đáp của API"], "Clear all selected items": ["<PERSON><PERSON><PERSON> tất cả các mục đã chọn"], "Search results updated.": ["<PERSON><PERSON><PERSON> quả tìm kiếm đã đ<PERSON><PERSON><PERSON> cập nh<PERSON>."], "%d item selected": ["%d mục <PERSON><PERSON><PERSON> ch<PERSON>n"], "Search for items": ["<PERSON><PERSON><PERSON> k<PERSON> mục"], "No results for %s": ["<PERSON><PERSON><PERSON>ng có kết quả cho %s"], "No items found.": ["<PERSON><PERSON><PERSON><PERSON> tìm thấy mục nào."], "Clear all": ["<PERSON><PERSON><PERSON> tất cả"], "Remove %s": ["Xóa %s"], "Products by Attribute": ["Các sản phẩm dựa theo đặc điểm"], "Filter by Product Attribute": ["Chọn lọc theo đặc điểm sản phẩm"], "All selected attributes": ["Các đặc điểm bạn đã chọn"], "Any selected attributes": ["Bất kỳ đặc điểm được chọn"], "Pick at least two attributes to use this setting.": ["Chọn ít nhất 2 đặc tính khi sử dụng cài đặt này."], "Product attribute search results updated.": ["<PERSON><PERSON><PERSON> quả tìm kiếm thuộc t<PERSON>h sản phẩm đ<PERSON><PERSON><PERSON> cập nhật."], "%d attribute selected": ["%d thu<PERSON><PERSON> t<PERSON>h sản phẩm được chọn"], "Search for product attributes": ["Đặc điểm sản phẩm"], "Your store doesn't have any product attributes.": ["Cửa hàng của bạn chưa có đặc tính sản phẩm nào."], "Showing Products by Attribute block preview.": ["<PERSON><PERSON> sách sản phẩm theo đặc điểm."], "Add to Cart button": ["<PERSON><PERSON><PERSON>ê<PERSON> vào giỏ hàng"], "Display a grid of products from your selected attributes.": ["Hiển thị sản phẩm theo các đặc điểm bạn đã chọn."], "Clear all product attributes": ["Xóa tất cả đặc tính sản phẩm"], "Done": ["<PERSON><PERSON><PERSON> th<PERSON>"], "Order By": ["<PERSON><PERSON><PERSON> xếp theo"], "Layout": ["Bố cục"], "Rows": ["<PERSON><PERSON><PERSON>"], "Columns": ["<PERSON><PERSON><PERSON>"], "Price - high to low": ["G<PERSON><PERSON> - cao tới thấp"], "Price - low to high": ["<PERSON><PERSON><PERSON> <PERSON> thấp tới cao"], "Newness - newest first": ["Sự mới mẻ - <PERSON><PERSON><PERSON> nhất trước tiên"], "Order products by": ["<PERSON><PERSON><PERSON> x<PERSON><PERSON> sản phẩm theo"], "Product title": ["<PERSON><PERSON><PERSON><PERSON> đ<PERSON> sản phẩm"], "Product rating": ["<PERSON><PERSON><PERSON> gi<PERSON> sản phẩm"], "Product price": ["<PERSON><PERSON><PERSON> s<PERSON>n ph<PERSON>m"], "Menu Order": ["Thứ tự menu"], "Title - alphabetical": ["<PERSON>i<PERSON><PERSON> đề - theo thứ tự bảng chữ cái"], "Sales - most first": ["<PERSON><PERSON> h<PERSON> - n<PERSON><PERSON>u nhất trước"], "Rating - highest first": ["<PERSON><PERSON><PERSON> giá - cao nhất trư<PERSON>c"], "Display products matching": ["<PERSON><PERSON><PERSON> thị sản phẩm phù hợp"], "Filter by stock status": ["<PERSON><PERSON><PERSON> theo trạng thái kho"], "Product image": ["Ảnh sản phẩm"], "%d product": ["%d s<PERSON><PERSON> ph<PERSON>m"], "Content": ["<PERSON><PERSON>i dung"]}}, "comment": {"reference": "assets/client/blocks/products-by-attribute.js"}}