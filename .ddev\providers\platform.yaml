#ddev-generated
# Platform.sh provider configuration. This works out of the box, but can be edited to add
# your own preferences. If you edit it, remove the `ddev-generated` line from the top so
# that it won't be overwritten.

# Consider using `ddev add-on get ddev/ddev-platformsh` (https://github.com/ddev/ddev-platformsh) for more
# complete platform integration.

# To use this configuration,

# 1. Check out the site from platform.sh and then configure it with `ddev config`. You'll want to use `ddev start` and make sure the basic functionality is working.
# 2. Obtain and configure an API token.
#    a. Login to the Platform.sh Dashboard and go to Account->API Tokens to create an API token for ddev to use.
#    b. Add the API token to the `web_environment` section in your global ddev configuration at ~/.ddev/global_config.yaml:
#    ```yaml
#    web_environment:
#        - PLATFORMSH_CLI_TOKEN=abcdeyourtoken
#    ```
#    You can also do this with `ddev config global --web-environment-add="PLATFORMSH_CLI_TOKEN=abcdeyourtoken"`.
#
#    To use multiple API tokens for different projects, add them to your per-project configuration
#    using the .ddev/config.local.yaml file instead. This file is gitignored by default.
#    ```yaml
#    web_environment:
#        - PLATFORMSH_CLI_TOKEN=abcdeyourtoken
#    ```
#
# 3. Add PLATFORM_PROJECT and PLATFORM_ENVIRONMENT and optional PLATFORM_APP (only if your environment contains more than one app) variables to your project `.ddev/config.yaml` or a `.ddev/config.platform.yaml`
#    ```yaml
#    web_environment:
#        - PLATFORM_PROJECT=nf4amudfn23biyourproject
#        - PLATFORM_ENVIRONMENT=main
#        - PLATFORM_APP=app
#    ```
#    You can also do this with `ddev config --web-environment-add="PLATFORM_PROJECT=nf4amudfn23biyourproject,PLATFORM_ENVIRONMENT=main,PLATFORM_APP=app"`.
#
# 4. `ddev restart`
# 5. Run `ddev pull platform`. After you agree to the prompt, the current upstream database and files will be downloaded.
# 6. Optionally use `ddev push platform` to push local files and database to platform.sh. Note that `ddev push` is a command that can potentially damage your production site, so this is not recommended.

# If you have more than one database on your Platform.sh project,
# you will likely to choose which one you want to use
# as the primary database ('db').
# Do this by setting PLATFORM_PRIMARY_RELATIONSHIP, for example, `ddev config --web-environment-add=PLATFORM_PRIMARY_RELATIONSHIP=main`
# or run `ddev pull platform` with the environment variable, for example
# `ddev pull platform -y --environment=PLATFORM_PRIMARY_RELATIONSHIP=main`
# If you need to change this `platform.yaml` recipe, you can change it to suit your needs, but remember to remove the "ddev-generated" line from the top.

# Debugging: Use `ddev exec platform` to see what platform.sh knows about
# your configuration and whether it's working correctly.

auth_command:
  command: |
    set -eu -o pipefail
    if [ -z "${PLATFORMSH_CLI_TOKEN:-}" ]; then echo "Please make sure you have set PLATFORMSH_CLI_TOKEN." && exit 1; fi
    if [ -z "${PLATFORM_PROJECT:-}" ]; then echo "Please make sure you have set PLATFORM_PROJECT." && exit 1; fi
    if [ -z "${PLATFORM_ENVIRONMENT:-}" ]; then echo "Please make sure you have set PLATFORM_ENVIRONMENT." && exit 1; fi

db_pull_command:
  command: |
    # set -x   # You can enable bash debugging output by uncommenting
    set -eu -o pipefail
    export PLATFORMSH_CLI_NO_INTERACTION=1
    # /tmp/db_relationships.yaml is the full yaml output of the database relationships
    db_relationships_file=/tmp/db_relationships.yaml
    PLATFORM_RELATIONSHIPS="" platform relationships -y  -e "${PLATFORM_ENVIRONMENT}" ${PLATFORM_APP:+"--app=${PLATFORM_APP}"} | yq 'with_entries(select(.[][].type == "mariadb:*" or .[][].type == "*mysql:*" or .[][].type == "postgresql:*")) ' >${db_relationships_file}
    db_relationships=($(yq ' keys | .[] ' ${db_relationships_file}))
    db_names=($(yq '.[][].path' ${db_relationships_file}))
    db_count=${#db_relationships[@]}
    # echo "db_relationships=${db_relationships} sizeof db_relationships=${#db_relationships[@]} db_names=${db_names} db_count=${db_count} PLATFORM_PRIMARY_RELATIONSHIP=${PLATFORM_PRIMARY_RELATIONSHIP}"
    # If we have only one database, import it into local database named 'db'
    if [ ${#db_names[@]} -eq 1 ]; then db_names[0]="db"; fi

    for (( i=0; i<${#db_relationships[@]}; i++ )); do
      db_name=${db_names[$i]}
      rel=${db_relationships[$i]}
      # if PLATFORM_PRIMARY_RELATIONSHIP is set, then when doing that one, import it into local database 'db'
      if [ "${rel}" = "${PLATFORM_PRIMARY_RELATIONSHIP:-notset}" ] ; then
        echo "PLATFORM_PRIMARY_RELATIONSHIP=${PLATFORM_PRIMARY_RELATIONSHIP:-} so using it as database 'db' instead of the upstream '${db_name}'"
        db_name="db"
      fi

      platform db:dump --yes ${PLATFORM_APP:+"--app=${PLATFORM_APP}"} --relationship=${rel} --gzip --file=/var/www/html/.ddev/.downloads/${db_name}.sql.gz --project="${PLATFORM_PROJECT:-setme}" --environment="${PLATFORM_ENVIRONMENT:-setme}"
    done
    echo "Downloaded db dumps for databases '${db_names[@]}'"

files_import_command:
  command: |
    #set -x   # You can enable bash debugging output by uncommenting
    set -eu -o pipefail
    export PLATFORMSH_CLI_NO_INTERACTION=1
    # Use $PLATFORM_MOUNTS if it exists to get list of mounts to download, otherwise just web/sites/default/files (drupal)
    declare -a mounts=(${PLATFORM_MOUNTS:-/web/sites/default/files})
    platform mount:download --all --yes --quiet --project="${PLATFORM_PROJECT}" --environment="${PLATFORM_ENVIRONMENT}" ${PLATFORM_APP:+"--app=${PLATFORM_APP}"} --target=/var/www/html


# push is a dangerous command. If not absolutely needed it's better to delete these lines.
db_push_command:
  command: |
    # set -x   # You can enable bash debugging output by uncommenting
    set -eu -o pipefail
    export PLATFORMSH_CLI_NO_INTERACTION=1
    pushd /var/www/html/.ddev/.downloads >/dev/null;
    if [ "${PLATFORM_PRIMARY_RELATIONSHIP:-}" != "" ] ; then
      rel="--relationship ${PLATFORM_PRIMARY_RELATIONSHIP}"
    fi
    gzip -dc db.sql.gz | platform db:sql --project="${PLATFORM_PROJECT}" ${rel:-} --environment="${PLATFORM_ENVIRONMENT}" ${PLATFORM_APP:+"--app=${PLATFORM_APP}"}

# push is a dangerous command. If not absolutely needed it's better to delete these lines.
# TODO: This is a naive, Drupal-centric push, which needs adjustment for the mount to be pushed.
files_push_command:
  command: |
    # set -x   # You can enable bash debugging output by uncommenting
    set -eu -o pipefail
    export PLATFORMSH_CLI_NO_INTERACTION=1
    ls "${DDEV_FILES_DIR}" >/dev/null # This just refreshes stale NFS if possible
    platform mount:upload --yes --quiet --project="${PLATFORM_PROJECT}" --environment="${PLATFORM_ENVIRONMENT}" ${PLATFORM_APP:+"--app=${PLATFORM_APP}"} --source="${DDEV_FILES_DIR}" --mount=web/sites/default/files
