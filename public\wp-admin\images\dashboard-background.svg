<svg preserveAspectRatio="xMidYMin slice" fill="none" viewBox="0 0 1232 240" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false">
  <g clip-path="url(#a)">
    <path fill="#151515" d="M0 0h1232v240H0z"/>
    <ellipse cx="616" cy="232" fill="url(#b)" opacity=".05" rx="1497" ry="249"/>
    <mask id="d" width="1000" height="400" x="232" y="20" maskUnits="userSpaceOnUse" style="mask-type:alpha">
      <path fill="url(#c)" d="M0 0h1000v400H0z" transform="translate(232 20)"/>
    </mask>
    <g stroke-width="2" mask="url(#d)">
      <path stroke="url(#e)" d="M387 20v1635"/>
      <path stroke="url(#f)" d="M559.5 20v1635"/>
      <path stroke="url(#g)" d="M732 20v1635"/>
      <path stroke="url(#h)" d="M904.5 20v1635"/>
      <path stroke="url(#i)" d="M1077 20v1635"/>
    </g>
  </g>
  <defs>
    <linearGradient id="e" x1="387.5" x2="387.5" y1="20" y2="1655" gradientUnits="userSpaceOnUse">
      <stop stop-color="#3858E9" stop-opacity="0"/>
      <stop offset=".297" stop-color="#3858E9"/>
      <stop offset=".734" stop-color="#3858E9"/>
      <stop offset="1" stop-color="#3858E9" stop-opacity="0"/>
      <stop offset="1" stop-color="#3858E9" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="f" x1="560" x2="560" y1="20" y2="1655" gradientUnits="userSpaceOnUse">
      <stop stop-color="#FFFCB5" stop-opacity="0"/>
      <stop offset="0" stop-color="#FFFCB5" stop-opacity="0"/>
      <stop offset=".297" stop-color="#FFFCB5"/>
      <stop offset=".734" stop-color="#FFFCB5"/>
      <stop offset="1" stop-color="#FFFCB5" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="g" x1="732.5" x2="732.5" y1="20" y2="1655" gradientUnits="userSpaceOnUse">
      <stop stop-color="#C7FFDB" stop-opacity="0"/>
      <stop offset=".297" stop-color="#C7FFDB"/>
      <stop offset=".693" stop-color="#C7FFDB"/>
      <stop offset="1" stop-color="#C7FFDB" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="h" x1="905" x2="905" y1="20" y2="1655" gradientUnits="userSpaceOnUse">
      <stop stop-color="#FFB7A7" stop-opacity="0"/>
      <stop offset=".297" stop-color="#FFB7A7"/>
      <stop offset=".734" stop-color="#FFB7A7"/>
      <stop offset="1" stop-color="#3858E9" stop-opacity="0"/>
      <stop offset="1" stop-color="#FFB7A7" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="i" x1="1077.5" x2="1077.5" y1="20" y2="1655" gradientUnits="userSpaceOnUse">
      <stop stop-color="#7B90FF" stop-opacity="0"/>
      <stop offset=".297" stop-color="#7B90FF"/>
      <stop offset=".734" stop-color="#7B90FF"/>
      <stop offset="1" stop-color="#3858E9" stop-opacity="0"/>
      <stop offset="1" stop-color="#7B90FF" stop-opacity="0"/>
    </linearGradient>
    <radialGradient id="b" cx="0" cy="0" r="1" gradientTransform="matrix(0 249 -1497 0 616 232)" gradientUnits="userSpaceOnUse">
      <stop stop-color="#3858E9"/>
      <stop offset="1" stop-color="#151515" stop-opacity="0"/>
    </radialGradient>
    <radialGradient id="c" cx="0" cy="0" r="1" gradientTransform="matrix(0 765 -1912.5 0 500 -110)" gradientUnits="userSpaceOnUse">
      <stop offset=".161" stop-color="#151515" stop-opacity="0"/>
      <stop offset=".682"/>
    </radialGradient>
    <clipPath id="a">
      <path fill="#fff" d="M0 0h1232v240H0z"/>
    </clipPath>
  </defs>
</svg>
