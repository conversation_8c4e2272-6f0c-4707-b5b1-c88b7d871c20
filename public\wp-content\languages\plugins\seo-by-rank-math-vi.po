# Translation of Plugins - Rank Math SEO – AI SEO Tools to Dominate SEO Rankings - Stable (latest release) in Vietnamese
# This file is distributed under the same license as the Plugins - Rank Math SEO – AI SEO Tools to Dominate SEO Rankings - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-09-10 09:11:03+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: vi_VN\n"
"Project-Id-Version: Plugins - Rank Math SEO – AI SEO Tools to Dominate SEO Rankings - Stable (latest release)\n"

#: includes/modules/content-ai/views/options.php:161
msgid "Arabic"
msgstr "Tiếng Ả Rập"

#: includes/modules/content-ai/views/options.php:189
msgid "Turkish"
msgstr "Tiếng <PERSON>"

#: includes/modules/content-ai/class-bulk-image-alt.php:139
msgid "Bulk image alt generation failed."
msgstr "Tạo alt hình ảnh hàng loạt không thành công."

#. Translators: placeholder is the number of modified posts.
#: includes/modules/content-ai/class-bulk-image-alt.php:109
msgid "Image alt attributes successfully updated in %d post."
msgid_plural "Image alt attributes successfully updated in %d posts."
msgstr[0] "Thuộc tính alt hình ảnh đã được cập nhật thành công trong %d bài viết."

#. Translators: placeholder is the number of modified posts.
#: includes/modules/content-ai/class-bulk-image-alt.php:174
msgid "Image alt attributes successfully updated in %d posts. The process was stopped as you have used all the credits on your site."
msgstr "Thuộc tính alt hình ảnh đã được cập nhật thành công trong %d bài viết. Quá trình đã bị dừng vì bạn đã sử dụng hết điểm trên trang web của mình."

#: includes/modules/content-ai/class-bulk-image-alt.php:54
msgid "Bulk image alt generation started. It might take few minutes to complete the process."
msgstr "Tạo alt hình ảnh hàng loạt đã bắt đầu. Có thể mất vài phút để hoàn thành quá trình."

#: includes/modules/content-ai/class-bulk-actions.php:90
#: includes/modules/content-ai/class-bulk-actions.php:107
msgid "Write Image Alt Text with AI"
msgstr "Viết văn bản thay thế hình ảnh bằng AI"

#: includes/helpers/class-choices.php:634
msgid "Tax ID"
msgstr "Mã số thuế"

#: includes/modules/local-seo/views/titles-options.php:249
msgid "Additional Info"
msgstr "Thông tin bổ sung"

#: includes/modules/local-seo/views/titles-options.php:95
msgid "Enter the contact email address that could be displayed on search engines."
msgstr "Nhập địa chỉ email liên hệ có thể được hiển thị trên các công cụ tìm kiếm."

#: includes/modules/local-seo/views/titles-options.php:85
msgid "URL of your website or your company’s website."
msgstr "URL của trang web của bạn hoặc trang web của công ty bạn."

#: includes/modules/local-seo/views/titles-options.php:65
msgid "Provide a detailed description of your organization."
msgstr "Cung cấp mô tả chi tiết về tổ chức của bạn."

#: includes/helpers/class-choices.php:635
msgid "Number of Employees"
msgstr "Số lượng nhân viên"

#: includes/helpers/class-choices.php:631
msgid "NAICS Code"
msgstr "Mã NAICS"

#: includes/helpers/class-choices.php:630
msgid "LEI Code"
msgstr "Mã LEI"

#: includes/helpers/class-choices.php:629
msgid "DUNS"
msgstr "DUNS"

#: includes/helpers/class-choices.php:628
msgid "ISO 6523 Code"
msgstr "Mã ISO 6523"

#: includes/helpers/class-choices.php:627
msgid "Founding Date"
msgstr "Ngày thành lập"

#: includes/helpers/class-choices.php:626
msgid "Legal Name"
msgstr "Tên pháp lý"

#: includes/frontend/class-shortcodes.php:370
msgid "Description:"
msgstr "Mô tả:"

#: includes/helpers/class-choices.php:633
msgid "VAT ID"
msgstr "Mã số VAT"

#: includes/helpers/class-choices.php:632
msgid "Global Location Number"
msgstr "Mã toàn cầu phân định địa điểm (GLN)"

#: includes/modules/local-seo/views/titles-options.php:250
msgid "Provide relevant details of your company to include in the Organization Schema."
msgstr "Cung cấp thông tin chi tiết liên quan đến công ty của bạn để đưa vào Schema tổ chức."

#: includes/modules/content-ai/views/options.php:33
msgid "Escape the Writer's Block Using AI to Write Inside WordPress."
msgstr "Vượt qua tình trạng bí ý tưởng bằng cách sử dụng AI để viết trong WordPress."

#: includes/modules/content-ai/views/options.php:32
msgid "Engage with RankBot, Our AI Chatbot, For SEO Advice."
msgstr "Trò chuyện với RankBot, Chatbot AI của chúng tôi, để nhận tư vấn về SEO."

#: includes/modules/content-ai/views/options.php:30
msgid "Gain Access to 40+ Advanced AI Tools."
msgstr "Truy cập hơn 40 công cụ AI nâng cao."

#: includes/modules/content-ai/views/options.php:31
msgid "Experience the Revolutionary AI-Powered Content Editor."
msgstr "Trải nghiệm trình chỉnh sửa nội dung được hỗ trợ bởi AI mang tính cách mạng."

#. Description of the plugin
#: rank-math.php
msgid "Rank Math SEO is the Best WordPress SEO plugin with the features of many SEO and AI SEO tools in a single package to help multiply your SEO traffic."
msgstr "Rank Math SEO là plugin SEO WordPress tốt nhất với các tính năng của nhiều công cụ SEO và SEO AI trong một gói duy nhất để giúp nhân lên lưu lượng truy cập SEO của bạn."

#: includes/modules/content-ai/class-bulk-actions.php:248
msgid "Terminate"
msgstr "Kết thúc"

#: includes/modules/content-ai/class-bulk-actions.php:260
msgid "Bulk Editing Process Successfully Cancelled"
msgstr "Quá trình chỉnh sửa hàng loạt đã được hủy thành công"

#. Translators: placeholders are the number of posts that were processed.
#: includes/modules/content-ai/class-bulk-actions.php:244
msgid "Terminate the ongoing Content AI Bulk Editing Process to halt any pending modifications and revert to the previous state. The bulk metadata has been generated for %1$d out of %1$d posts so far."
msgstr "Kết thúc Quá trình chỉnh sửa hàng loạt Content AI đang diễn ra để dừng mọi sửa đổi đang chờ xử lý và trở về trạng thái trước đó. Siêu dữ liệu hàng loạt đã được tạo cho %1$d trong số %1$d bài viết cho đến nay."

#: includes/modules/content-ai/class-bulk-actions.php:241
msgid "Cancel Content AI Bulk Editing Process"
msgstr "Hủy Quá trình chỉnh sửa hàng loạt Content AI"

#: includes/helpers/class-content-ai.php:453
msgid "User wallet not found."
msgstr "Không tìm thấy ví người dùng."

#: includes/helpers/class-content-ai.php:452
msgid "Invalid API key. Please check your API key or reconnect the site and try again."
msgstr "Khóa API không hợp lệ. Vui lòng kiểm tra khóa API của bạn hoặc kết nối lại trang web và thử lại."

#: includes/admin/class-bulk-actions.php:90
msgid "Remove redirection"
msgstr "Xóa chuyển hướng"

#: includes/admin/class-admin-menu.php:246
msgid "Black Friday Sale"
msgstr "Giảm giá Thứ Sáu Đen"

#: includes/admin/class-admin-menu.php:241
msgid "Anniversary Sale"
msgstr "Giảm giá Kỷ niệm"

#: includes/admin/class-bulk-actions.php:79
msgid "&#8595; Rank Math"
msgstr "&#8595; Rank Math"

#: includes/admin/class-bulk-actions.php:85
msgid "Set to follow"
msgstr "Đặt thành theo dõi"

#: includes/admin/class-bulk-actions.php:84
msgid "Set to nofollow"
msgstr "Đặt thành không theo dõi"

#: includes/admin/class-admin-menu.php:251
msgid "Cyber Monday Sale"
msgstr "Giảm giá Thứ Hai Điện Tử"

#: includes/admin/class-admin-menu.php:236
msgid "New Year Sale"
msgstr "Giảm giá Năm Mới"

#: includes/admin/class-admin-menu.php:231
msgid "Christmas Sale"
msgstr "Giảm giá Giáng Sinh"

#. Translators: placeholder is the default Schema type setting.
#: includes/admin/class-bulk-actions.php:101
msgid "Set Schema: Default (%s)"
msgstr "Đặt Schema: Mặc định (%s)"

#: includes/admin/class-bulk-actions.php:95
msgid "Set Schema: None"
msgstr "Đặt Schema: Không"

#: includes/admin/class-bulk-actions.php:83
msgid "Set to index"
msgstr "Đặt là lập chỉ mục"

#: includes/admin/class-bulk-actions.php:82
msgid "Set to noindex"
msgstr "Đặt là không lập chỉ mục"

#: includes/admin/class-bulk-actions.php:86
msgid "Remove custom canonical URL"
msgstr "Xóa URL chính tắc (Canonical) tùy chỉnh"

#: includes/modules/schema/shortcode/course.php:76
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Repeat Count"
msgstr "Số lần lặp lại"

#: includes/modules/schema/shortcode/course.php:104
msgid "Course Price"
msgstr "Giá khóa học"

#: includes/modules/schema/shortcode/course.php:97
msgid "Course Currency"
msgstr "Tiền tệ khóa học"

#: includes/modules/schema/shortcode/course.php:90
msgid "Course Type"
msgstr "Loại khóa học"

#: includes/modules/schema/shortcode/course.php:83
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Repeat Frequency"
msgstr "Tần suất lặp lại"

#: includes/modules/schema/shortcode/course.php:48
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Course Workload"
msgstr "Khối lượng công việc khóa học"

#: includes/modules/schema/shortcode/course.php:41
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Course Mode"
msgstr "Chế độ khóa học"

#: includes/modules/analytics/class-analytics-common.php:301
msgid "This is the number of pageviews carried out by visitors from Search Engines."
msgstr "Đây là số lượt xem trang được thực hiện bởi khách truy cập từ Công cụ Tìm kiếm."

#: includes/modules/content-ai/class-admin.php:143
msgid "Content AI Refresh Date"
msgstr "Ngày làm mới Content AI"

#: includes/modules/content-ai/class-admin.php:135
msgid "Content AI Plan"
msgstr "Gói Content AI"

#: includes/modules/content-ai/views/options.php:160
msgid "UK English"
msgstr "Tiếng Anh (Anh)"

#: includes/modules/content-ai/views/options.php:159
msgid "US English"
msgstr "Tiếng Anh (Mỹ)"

#: includes/modules/content-ai/class-admin.php:139
msgid "Content AI Credits"
msgstr "Điểm Content AI"

#: includes/modules/content-ai/blocks/command/assets/src/block.json
msgctxt "block keyword"
msgid "seo"
msgstr "seo"

#: includes/modules/content-ai/blocks/command/assets/src/block.json
msgctxt "block keyword"
msgid "content ai"
msgstr "content ai"

#: includes/modules/content-ai/blocks/command/assets/src/block.json
msgctxt "block keyword"
msgid "rankmath"
msgstr "rankmath"

#: includes/modules/content-ai/blocks/command/assets/src/block.json
msgctxt "block keyword"
msgid "rank math"
msgstr "rank math"

#: includes/modules/content-ai/blocks/command/assets/src/block.json
msgctxt "block keyword"
msgid "content"
msgstr "nội dung"

#: includes/modules/content-ai/blocks/command/assets/src/block.json
msgctxt "block keyword"
msgid "ai"
msgstr "ai"

#: includes/modules/content-ai/blocks/command/assets/src/block.json
msgctxt "block description"
msgid "Generate content without any hassle, powered by Rank Math's Content AI."
msgstr "Tạo nội dung mà không gặp bất kỳ rắc rối nào, được hỗ trợ bởi Content AI của Rank Math."

#: includes/modules/content-ai/blocks/command/assets/src/block.json
msgctxt "block title"
msgid "AI Assistant [Content AI]"
msgstr "Trợ lý AI [Content AI]"

#: includes/modules/content-ai/views/options.php:188
msgid "Swedish"
msgstr "Tiếng Thụy Điển"

#: includes/modules/content-ai/views/options.php:187
msgid "Spanish"
msgstr "Tiếng Tây Ban Nha"

#: includes/modules/content-ai/views/options.php:186
msgid "Slovenian"
msgstr "Tiếng Slovenia"

#: includes/modules/content-ai/views/options.php:185
msgid "Slovak"
msgstr "Tiếng Slovak"

#: includes/modules/content-ai/views/options.php:183
msgid "Romanian"
msgstr "Tiếng Rumani"

#: includes/modules/content-ai/views/options.php:182
msgid "Portuguese"
msgstr "Tiếng Bồ Đào Nha"

#: includes/modules/content-ai/views/options.php:181
msgid "Polish"
msgstr "Tiếng Ba Lan"

#: includes/modules/content-ai/views/options.php:180
msgid "Norwegian"
msgstr "Tiếng Na Uy"

#: includes/modules/content-ai/views/options.php:179
msgid "Lithuanian"
msgstr "Tiếng Litva"

#: includes/modules/content-ai/views/options.php:178
msgid "Latvian"
msgstr "Tiếng Latvia"

#: includes/modules/content-ai/views/options.php:177
msgid "Korean"
msgstr "Tiếng Hàn"

#: includes/modules/content-ai/views/options.php:176
msgid "Japanese"
msgstr "Tiếng Nhật"

#: includes/modules/content-ai/views/options.php:175
msgid "Italian"
msgstr "Tiếng Ý"

#: includes/modules/content-ai/views/options.php:174
msgid "Indonesian"
msgstr "Tiếng Indonesia"

#: includes/modules/content-ai/views/options.php:173
msgid "Hungarian"
msgstr "Tiếng Hungary"

#: includes/modules/content-ai/views/options.php:172
msgid "Hebrew"
msgstr "Tiếng Do Thái"

#: includes/modules/content-ai/views/options.php:171
msgid "Greek"
msgstr "Tiếng Hy Lạp"

#: includes/modules/content-ai/views/options.php:170
msgid "German"
msgstr "Tiếng Đức"

#: includes/modules/content-ai/views/options.php:169
msgid "French"
msgstr "Tiếng Pháp"

#: includes/modules/content-ai/views/options.php:168
msgid "Finnish"
msgstr "Tiếng Phần Lan"

#: includes/modules/content-ai/views/options.php:167
msgid "Estonian"
msgstr "Tiếng Estonia"

#: includes/modules/content-ai/views/options.php:166
msgid "Dutch"
msgstr "Tiếng Hà Lan"

#: includes/modules/content-ai/views/options.php:165
msgid "Danish"
msgstr "Tiếng Đan Mạch"

#: includes/modules/content-ai/views/options.php:164
msgid "Czech"
msgstr "Tiếng Séc"

#: includes/modules/content-ai/views/options.php:163
msgid "Chinese"
msgstr "Tiếng Trung"

#: includes/modules/content-ai/views/options.php:155
msgid "This option lets you set the default language for content generated using Content AI. You can override this in individual tools."
msgstr "Tùy chọn này cho phép bạn đặt ngôn ngữ mặc định cho nội dung được tạo bằng Content AI. Bạn có thể ghi đè tùy chọn này trong các công cụ riêng lẻ."

#: includes/modules/content-ai/views/options.php:154
msgid "Default Language"
msgstr "Ngôn ngữ mặc định"

#: includes/modules/content-ai/views/options.php:145
msgid "Writers"
msgstr "Nhà văn"

#: includes/modules/content-ai/views/options.php:144
msgid "Video Creators"
msgstr "Người sáng tạo video"

#: includes/modules/content-ai/views/options.php:142
msgid "Travelers"
msgstr "Du khách"

#: includes/modules/content-ai/views/options.php:141
msgid "Tech Enthusiasts"
msgstr "Người đam mê công nghệ"

#: includes/modules/content-ai/views/options.php:140
msgid "Students"
msgstr "Học sinh"

#: includes/modules/content-ai/views/options.php:139
msgid "Sports Fans"
msgstr "Người hâm mộ thể thao"

#: includes/modules/content-ai/views/options.php:138
msgid "Social Media Users"
msgstr "Người dùng mạng xã hội"

#: includes/modules/content-ai/views/options.php:137
msgid "Seniors"
msgstr "Người cao tuổi"

#: includes/modules/content-ai/views/options.php:135
msgid "Retirees"
msgstr "Người về hưu"

#: includes/modules/content-ai/views/options.php:134
msgid "Professionals"
msgstr "Chuyên gia"

#: includes/modules/content-ai/views/options.php:133
msgid "Podcast Listeners"
msgstr "Người nghe Podcast"

#: includes/modules/content-ai/views/options.php:132
msgid "Photographers"
msgstr "Nhiếp ảnh gia"

#: includes/modules/content-ai/views/options.php:131
msgid "Pet Owners"
msgstr "Chủ sở hữu thú cưng"

#: includes/modules/content-ai/views/options.php:129
msgid "Outdoor Enthusiasts"
msgstr "Người đam mê hoạt động ngoài trời"

#: includes/modules/content-ai/views/options.php:128
msgid "Musicians"
msgstr "Nhạc sĩ"

#: includes/modules/content-ai/views/options.php:126
msgid "Job Seekers"
msgstr "Người tìm việc"

#: includes/modules/content-ai/views/options.php:125
msgid "Investors"
msgstr "Nhà đầu tư"

#: includes/modules/content-ai/views/options.php:124
msgid "Indoor Hobbyists"
msgstr "Người có sở thích trong nhà"

#: includes/modules/content-ai/views/options.php:123
msgid "Healthcare Professionals"
msgstr "Chuyên gia chăm sóc sức khỏe"

#: includes/modules/content-ai/views/options.php:122
msgid "Health Enthusiasts"
msgstr "Người đam mê sức khỏe"

#: includes/modules/content-ai/views/options.php:121
msgid "General Audience"
msgstr "Khán giả đại chúng"

#: includes/modules/content-ai/views/options.php:120
msgid "Gardeners"
msgstr "Người làm vườn"

#: includes/modules/content-ai/views/options.php:118
msgid "Foodies"
msgstr "Người sành ăn"

#: includes/modules/content-ai/views/options.php:117
msgid "Fitness Enthusiasts"
msgstr "Người đam mê thể dục"

#: includes/modules/content-ai/views/options.php:116
msgid "Fashionistas"
msgstr "Tín đồ thời trang"

#: includes/modules/content-ai/views/options.php:115
msgid "Environmentalists"
msgstr "Nhà môi trường học"

#: includes/modules/content-ai/views/options.php:114
msgid "Entrepreneurs"
msgstr "Doanh nhân"

#: includes/modules/content-ai/views/options.php:113
msgid "Engineers"
msgstr "Kỹ sư"

#: includes/modules/content-ai/views/options.php:112
msgid "Educators"
msgstr "Nhà giáo dục"

#: includes/modules/content-ai/views/options.php:111
msgid "Designers"
msgstr "Nhà thiết kế"

#: includes/modules/content-ai/views/options.php:110
msgid "DIYers"
msgstr "Người tự làm"

#: includes/modules/content-ai/views/options.php:109
msgid "Dancers"
msgstr "Vũ công"

#: includes/modules/content-ai/views/options.php:107
msgid "Cooks"
msgstr "Đầu bếp"

#: includes/modules/content-ai/views/options.php:106
msgid "Collectors"
msgstr "Nhà sưu tập"

#: includes/modules/content-ai/views/options.php:105
msgid "Business Owners"
msgstr "Chủ doanh nghiệp"

#: includes/modules/content-ai/views/options.php:104
msgid "Bloggers"
msgstr "Blogger"

#: includes/modules/content-ai/views/options.php:101
msgid "Artists"
msgstr "Nghệ sĩ"

#: includes/modules/content-ai/views/options.php:100
msgid "Activists"
msgstr "Nhà hoạt động"

#: includes/modules/content-ai/views/options.php:96
msgid "This option lets you set the default audience that usually reads your content. You can override this in individual tools."
msgstr "Tùy chọn này cho phép bạn đặt đối tượng mặc định thường đọc nội dung của bạn. Bạn có thể ghi đè tùy chọn này trong các công cụ riêng lẻ."

#: includes/modules/content-ai/views/options.php:95
msgid "Default Audience"
msgstr "Đối tượng mặc định"

#: includes/modules/content-ai/views/options.php:86
msgid "Technical"
msgstr "Kỹ thuật"

#: includes/modules/content-ai/views/options.php:85
msgid "Subjective"
msgstr "Chủ quan"

#: includes/modules/content-ai/views/options.php:84
msgid "Story-telling"
msgstr "Kể chuyện"

#: includes/modules/content-ai/views/options.php:83
msgid "Satirical"
msgstr "Châm biếm"

#: includes/modules/content-ai/views/options.php:81
msgid "Persuasive"
msgstr "Thuyết phục"

#: includes/modules/content-ai/views/options.php:80
msgid "Opinionated"
msgstr "Có ý kiến"

#: includes/modules/content-ai/views/options.php:79
msgid "Objective"
msgstr "Khách quan"

#: includes/modules/content-ai/views/options.php:78
msgid "Narrative"
msgstr "Tường thuật"

#: includes/modules/content-ai/views/options.php:77
msgid "Journalese"
msgstr "Báo chí"

#: includes/modules/content-ai/views/options.php:76
msgid "Informal"
msgstr "Không chính thức"

#: includes/modules/content-ai/views/options.php:75
msgid "Humorous"
msgstr "Hài hước"

#: includes/modules/content-ai/views/options.php:74
msgid "Friendly"
msgstr "Thân thiện"

#: includes/modules/content-ai/views/options.php:73
msgid "Formal"
msgstr "Trang trọng"

#: includes/modules/content-ai/views/options.php:71
msgid "Expository"
msgstr "Giải thích"

#: includes/modules/content-ai/views/options.php:70
msgid "Empathetic"
msgstr "Đồng cảm"

#: includes/modules/content-ai/views/options.php:69
msgid "Emotional"
msgstr "Cảm xúc"

#: includes/modules/content-ai/views/options.php:68
msgid "Descriptive"
msgstr "Mô tả"

#: includes/modules/content-ai/views/options.php:67
msgid "Creative"
msgstr "Sáng tạo"

#: includes/modules/content-ai/views/options.php:64
msgid "Argumentative"
msgstr "Tranh luận"

#: includes/modules/content-ai/views/options.php:63
msgid "Analytical"
msgstr "Phân tích"

#: includes/modules/content-ai/views/options.php:59
msgid "This feature enables the default primary tone or writing style that characterizes your content. You can override this in individual tools."
msgstr "Tính năng này cho phép bật giọng điệu chính hoặc phong cách viết mặc định đặc trưng cho nội dung của bạn. Bạn có thể ghi đè tùy chọn này trong các công cụ riêng lẻ."

#: includes/modules/content-ai/views/options.php:58
msgid "Default Tone"
msgstr "Giọng điệu mặc định"

#: includes/modules/content-ai/class-content-ai-page.php:169
msgid "Content AI History"
msgstr "Lịch sử Content AI"

#: includes/modules/content-ai/class-content-ai-page.php:163
msgid "Content AI Chat"
msgstr "Trò chuyện Content AI"

#: includes/modules/content-ai/class-content-ai-page.php:157
msgid "Content AI Editor"
msgstr "Trình chỉnh sửa Content AI"

#: includes/modules/content-ai/class-content-ai-page.php:151
msgid "Content AI Tools"
msgstr "Công cụ Content AI"

#. Translators: placeholder is the new label.
#: includes/modules/content-ai/class-content-ai-page.php:101
msgid "Content AI %s"
msgstr "Content AI %s"

#: includes/helpers/class-content-ai.php:451
msgid "Could not generate. Please try again later."
msgstr "Không thể tạo. Vui lòng thử lại sau."

#: includes/helpers/class-content-ai.php:450
msgid "The output was stopped as it was identified as potentially unsafe by the content filter."
msgstr "Đầu ra đã bị dừng vì nó được xác định là có khả năng không an toàn bởi bộ lọc nội dung."

#: includes/helpers/class-content-ai.php:444
msgid "Please update the Rank Math SEO plugin to the latest version to use this feature."
msgstr "Vui lòng cập nhật plugin Rank Math SEO lên phiên bản mới nhất để sử dụng tính năng này."

#: includes/helpers/class-content-ai.php:443
msgid "Please connect your account to use the Content AI."
msgstr "Vui lòng kết nối tài khoản của bạn để sử dụng Content AI."

#: includes/modules/content-ai/class-content-ai-page.php:161
#: includes/modules/content-ai/assets/js/content-ai-page.js:1
#: includes/modules/content-ai/assets/js/content-ai.js:1
msgid "Chat"
msgstr "Trò chuyện"

#: includes/modules/content-ai/class-content-ai-page.php:155
#: includes/modules/content-ai/assets/js/content-ai-page.js:1
#: includes/modules/content-ai/assets/js/content-ai.js:1
msgid "Content Editor"
msgstr "Trình soạn thảo nội dung"

#: includes/modules/content-ai/class-content-ai-page.php:149
#: includes/modules/content-ai/assets/js/content-ai-page.js:1
#: includes/modules/content-ai/assets/js/content-ai.js:1
msgid "AI Tools"
msgstr "Công cụ AI"

#: includes/modules/content-ai/class-bulk-actions.php:86
#: includes/modules/content-ai/class-bulk-actions.php:106
msgid "&#8595; Rank Math Content AI"
msgstr "&#8595; Rank Math Content AI"

#. translators: 1. Credits left 2. Buy more credits link
#: includes/modules/content-ai/views/options.php:223
msgid "%1$s credits left this month. Credits will renew on %2$s or you can upgrade to get more credits %3$s."
msgstr "Còn lại %1$s tín dụng trong tháng này. Tín dụng sẽ được gia hạn vào %2$s hoặc bạn có thể nâng cấp để nhận thêm tín dụng %3$s."

#: includes/modules/content-ai/views/options.php:162
msgid "Bulgarian"
msgstr "Tiếng Bungari"

#: includes/modules/content-ai/views/options.php:143
msgid "TV Enthusiasts"
msgstr "Người đam mê TV"

#: includes/modules/content-ai/views/options.php:130
msgid "Parents"
msgstr "Phụ huynh"

#: includes/modules/content-ai/views/options.php:127
msgid "Movie Buffs"
msgstr "Người mê phim"

#: includes/modules/content-ai/views/options.php:119
msgid "Gaming Enthusiasts"
msgstr "Người đam mê trò chơi"

#: includes/modules/content-ai/views/options.php:108
msgid "Crafters"
msgstr "Thợ thủ công"

#: includes/modules/content-ai/views/options.php:103
msgid "Bargain Hunters"
msgstr "Thợ săn giá hời"

#: includes/modules/content-ai/views/options.php:82
msgid "Poetic"
msgstr "Thơ ca"

#: includes/modules/content-ai/views/options.php:72
msgid "Factual"
msgstr "Sự thật"

#: includes/modules/content-ai/views/options.php:66
msgid "Conversational"
msgstr "Trò chuyện"

#: includes/modules/content-ai/views/options.php:65
msgid "Casual"
msgstr "Thân mật"

#: includes/modules/content-ai/class-bulk-actions.php:128
msgid "Another bulk editing process is already running. Please try again later after the existing process is complete."
msgstr "Một quy trình chỉnh sửa hàng loạt khác đang chạy. Vui lòng thử lại sau khi quy trình hiện tại hoàn tất."

#: includes/modules/content-ai/class-bulk-actions.php:89
msgid "Write SEO Title & Description with AI"
msgstr "Viết Tiêu đề & Mô tả SEO bằng AI"

#: includes/modules/content-ai/class-bulk-actions.php:88
msgid "Write SEO Description with AI"
msgstr "Viết Mô tả SEO bằng AI"

#: includes/modules/content-ai/class-bulk-actions.php:87
msgid "Write SEO Title with AI"
msgstr "Viết Tiêu đề SEO bằng AI"

#: includes/modules/content-ai/class-content-ai-page.php:76
msgid "Credits Remaining: "
msgstr "Số dư tín dụng: "

#. Translators: placeholder is the number of modified items.
#: includes/modules/content-ai/class-bulk-edit-seo-meta.php:112
msgid "SEO meta successfully updated in %d item."
msgid_plural "SEO meta successfully updated in %d items."
msgstr[0] "Thẻ meta SEO đã được cập nhật thành công trong %d mục."

#: includes/modules/content-ai/class-bulk-edit-seo-meta.php:54
msgid "Bulk editing SEO meta started. It might take few minutes to complete the process."
msgstr "Chỉnh sửa hàng loạt thẻ meta SEO đã bắt đầu. Có thể mất vài phút để hoàn tất quá trình."

#: includes/helpers/class-content-ai.php:449
msgid "Please revise the entered values in the fields as they are not secure. Make the required adjustments and try again."
msgstr "Vui lòng xem xét lại các giá trị đã nhập trong các trường vì chúng không an toàn. Thực hiện các điều chỉnh cần thiết và thử lại."

#: includes/helpers/class-content-ai.php:448
msgid "You've used up all available credits from the connected account."
msgstr "Bạn đã sử dụng hết số tín dụng khả dụng từ tài khoản được kết nối."

#: includes/helpers/class-content-ai.php:447
msgid "You've used up all available credits for this domain."
msgstr "Bạn đã sử dụng hết số tín dụng khả dụng cho miền này."

#: includes/helpers/class-content-ai.php:446
msgid "Oops! Too many requests in a short time. Please try again after some time."
msgstr "Ôi! Quá nhiều yêu cầu trong một thời gian ngắn. Vui lòng thử lại sau một thời gian."

#: includes/helpers/class-content-ai.php:445
msgid "This feature is only available for Content AI subscribers."
msgstr "Tính năng này chỉ khả dụng cho người đăng ký Content AI."

#: includes/modules/content-ai/views/options.php:204
msgid "Choose the type of posts/pages/CPTs where you want to use Content AI."
msgstr "Chọn loại bài viết/trang/CPT mà bạn muốn sử dụng Content AI."

#: includes/modules/content-ai/views/options.php:48
msgid "Content AI tailors keyword research to the target country for highly relevant suggestions. You can override this in individual posts/pages/CPTs."
msgstr "Content AI điều chỉnh nghiên cứu từ khóa theo quốc gia mục tiêu để có các đề xuất có liên quan cao. Bạn có thể ghi đè tùy chọn này trong các bài viết/trang/CPT riêng lẻ."

#. Translators: placeholder is the number of modified posts.
#: includes/modules/content-ai/class-bulk-edit-seo-meta.php:161
msgid "SEO meta successfully updated in %d posts. The process was stopped as you have used all the credits on your site."
msgstr "Thẻ meta SEO đã được cập nhật thành công trong %d bài viết. Quá trình đã bị dừng lại vì bạn đã sử dụng hết tín dụng trên trang web của mình."

#. translators: %s: Allowed tags
#: includes/settings/general/webmaster.php:94
msgid "Enter your custom webmaster tags. Only %s tags are allowed."
msgstr "Nhập thẻ quản trị web tùy chỉnh của bạn. Chỉ cho phép thẻ %s."

#: includes/settings/general/webmaster.php:91
msgid "Custom Webmaster Tags"
msgstr "Thẻ quản trị web tùy chỉnh"

#. Translators: link to the update page.
#: includes/modules/content-ai/class-rest.php:534
msgid "Please update"
msgstr "Vui lòng cập nhật"

#. Translators: link to the update page.
#: includes/modules/content-ai/class-rest.php:533
msgid "There is a new version of Content AI available! %s the Rank Math SEO plugin to use this feature."
msgstr "Đã có phiên bản Content AI mới! %s plugin Rank Math SEO để sử dụng tính năng này."

#: includes/modules/sitemap/settings/authors.php:52
msgid "Selected roles will be excluded from the XML &amp; HTML sitemaps."
msgstr "Các vai trò đã chọn sẽ bị loại trừ khỏi XML &amp; Sơ đồ trang web HTML."

#: includes/rest/class-admin.php:289
msgid "The redirection you are trying to create may cause an infinite loop. Please check the source and destination URLs. The redirection has been deactivated."
msgstr "Chuyển hướng bạn đang cố gắng tạo có thể gây ra vòng lặp vô hạn. Vui lòng kiểm tra URL nguồn và URL đích. Chuyển hướng đã bị hủy kích hoạt."

#: includes/rest/class-admin.php:296
msgid "The redirection you are trying to update may cause an infinite loop. Please check the source and destination URLs."
msgstr "Chuyển hướng bạn đang cố gắng cập nhật có thể gây ra vòng lặp vô hạn. Vui lòng kiểm tra URL nguồn và URL đích."

#: includes/modules/version-control/views/beta-optin-panel.php:21
msgid "You cannot turn on the Beta Tester feature because site wide plugins auto-update option is disabled on your site."
msgstr "Bạn không thể bật tính năng Người kiểm tra Beta vì tùy chọn tự động cập nhật plugin trên toàn trang web đã bị tắt trên trang web của bạn."

#: includes/modules/version-control/views/auto-update-panel.php:20
msgid "You cannot turn on auto-updates to automatically update to stable versions of Rank Math as soon as they are released, because site wide plugins auto-update option is disabled on your site."
msgstr "Bạn không thể bật tự động cập nhật để tự động cập nhật lên các phiên bản ổn định của Rank Math ngay khi chúng được phát hành, vì tùy chọn tự động cập nhật plugin trên toàn trang web đã bị tắt trên trang web của bạn."

#: includes/modules/seo-analysis/seo-analysis-tests.php:180
msgid "Site wide plugins auto-update option is disabled on your site."
msgstr "Tùy chọn tự động cập nhật plugin trên toàn trang web đã bị tắt trên trang web của bạn."

#: includes/modules/analytics/google/class-console.php:221
msgid "The Google Search Console request failed."
msgstr "Yêu cầu Google Search Console không thành công."

#: includes/modules/analytics/google/class-analytics.php:341
msgid "The Google Analytics Console request failed."
msgstr "Yêu cầu Google Analytics Console không thành công."

#: includes/modules/analytics/google/class-analytics.php:246
msgid "The Google Analytics request failed."
msgstr "Yêu cầu Google Analytics không thành công."

#: includes/modules/analytics/class-ajax.php:160
#: includes/modules/analytics/class-ajax.php:176
#: includes/modules/analytics/class-ajax.php:202
#: includes/modules/analytics/class-ajax.php:271
msgid "Data import will not work for this service as sufficient permissions are not given."
msgstr "Nhập dữ liệu sẽ không hoạt động cho dịch vụ này vì không được cấp đủ quyền."

#: includes/admin/wizard/views/search-console-ui.php:94
msgid "Test Connections"
msgstr "Kiểm tra kết nối"

#. Author URI of the plugin
#: rank-math.php
msgid "https://rankmath.com/?utm_source=Plugin&utm_medium=Readme%20Author%20URI&utm_campaign=WP"
msgstr "https://rankmath.com/?utm_source=Plugin&utm_medium=Readme%20Author%20URI&utm_campaign=WP"

#. Plugin URI of the plugin
#: rank-math.php
msgid "https://rankmath.com/"
msgstr "https://rankmath.com/"

#: includes/admin/importers/class-aioseo.php:69
msgid "Import Social URLs of your author archive pages."
msgstr "Nhập URL mạng xã hội của các trang lưu trữ tác giả của bạn."

#: includes/admin/class-admin.php:78
msgid "Additional profile URLs"
msgstr "URL hồ sơ bổ sung"

#: includes/settings/titles/social.php:87
msgid "Additional Profiles"
msgstr "Hồ sơ bổ sung"

#. translators: Link to social setting KB article
#: includes/admin/class-option-center.php:154
msgid "Add social account information to your website's Schema and Open Graph. %s."
msgstr "Thêm thông tin tài khoản mạng xã hội vào Schema và Open Graph của trang web của bạn. %s."

#: includes/admin/class-admin.php:450 includes/settings/titles/social.php:88
msgid "Additional Profiles to add in the <code>sameAs</code> Schema property."
msgstr "Hồ sơ bổ sung để thêm vào thuộc tính Schema <code>sameAs</code>."

#: includes/modules/seo-analysis/views/serp-preview.php:38
msgid "(No Description)"
msgstr "(Không có mô tả)"

#: includes/modules/seo-analysis/seo-analysis-tests.php:138
msgid "Check the presence of sitemaps on your website"
msgstr "Kiểm tra sự hiện diện của sơ đồ trang web trên trang web của bạn"

#: includes/modules/seo-analysis/views/form.php:32
msgid "Start SEO Analyzer"
msgstr "Bắt đầu phân tích SEO"

#: includes/modules/seo-analysis/views/seo-analyzer.php:22
msgid "Restart SEO Analyzer"
msgstr "Khởi động lại Trình phân tích SEO"

#: includes/modules/seo-analysis/views/competitor-analysis.php:215
msgid "Make more informed decisions & strategy"
msgstr "Đưa ra quyết định và chiến lược sáng suốt hơn"

#: includes/modules/seo-analysis/class-admin.php:116
msgid "Print"
msgstr "In"

#: includes/modules/seo-analysis/views/competitor-analysis.php:214
msgid "Explore new keywords and opportunities"
msgstr "Khám phá từ khóa và cơ hội mới"

#: includes/modules/seo-analysis/seo-analysis-tests.php:161
msgid "Verify auto-updates are enabled for Rank Math"
msgstr "Xác minh tự động cập nhật được bật cho Rank Math"

#: includes/module/class-manager.php:244
#: includes/modules/seo-analysis/class-admin-tabs.php:89
#: includes/modules/seo-analysis/class-seo-analysis.php:61
#: includes/modules/seo-analysis/class-seo-analysis.php:74
msgid "SEO Analyzer"
msgstr "Trình phân tích SEO"

#: includes/modules/seo-analysis/views/competitor-analysis.php:213
msgid "Evaluate strengths and weaknesses"
msgstr "Đánh giá điểm mạnh và điểm yếu"

#: includes/modules/seo-analysis/views/competitor-analysis.php:212
msgid "Analyze competitor websites to gain an edge"
msgstr "Phân tích trang web của đối thủ cạnh tranh để giành lợi thế"

#. translators: 1: Date, 2: Time.
#: includes/modules/seo-analysis/class-seo-analyzer.php:341
msgid "%1$s at %2$s"
msgstr "%1$s lúc %2$s"

#. Translators: placeholder is the new Rank Math label.
#: includes/modules/seo-analysis/class-admin.php:88
msgid "SEO Analyzer %s"
msgstr "Trình phân tích SEO %s"

#: includes/modules/seo-analysis/seo-analysis-tests.php:128
msgid "Confirm if Rank Math is connected to Search Console"
msgstr "Xác nhận xem Rank Math có được kết nối với Search Console hay không"

#: includes/modules/seo-analysis/seo-analysis-tests.php:56
msgid "Check your site's visibility to search engines"
msgstr "Kiểm tra khả năng hiển thị của trang web của bạn đối với các công cụ tìm kiếm"

#: includes/modules/seo-analysis/seo-analysis-tests.php:40
msgid "Confirm custom tagline is set for your site"
msgstr "Xác nhận khẩu hiệu tùy chỉnh được đặt cho trang web của bạn"

#: includes/modules/seo-analysis/views/serp-preview.php:30
msgid "(No Title)"
msgstr "(Không có tiêu đề)"

#: includes/modules/seo-analysis/views/seo-analyzer.php:23
msgid "View Issues"
msgstr "Xem sự cố"

#: includes/modules/seo-analysis/views/seo-analyzer.php:18
msgid "SEO Analysis for"
msgstr "Phân tích SEO cho"

#. translators: 1: Date, 2: Time.
#: includes/modules/seo-analysis/class-seo-analyzer.php:341
msgid "Last checked:"
msgstr "Lần kiểm tra cuối:"

#: includes/modules/seo-analysis/class-admin-tabs.php:97
#: includes/modules/seo-analysis/views/competitor-analysis.php:210
msgid "Competitor Analyzer"
msgstr "Trình phân tích đối thủ cạnh tranh"

#: includes/modules/seo-analysis/seo-analysis-tests.php:90
msgid "Verify the presence of focus keywords in your post titles"
msgstr "Xác minh sự hiện diện của các từ khóa trọng tâm trong tiêu đề bài viết của bạn"

#: includes/modules/seo-analysis/seo-analysis-tests.php:81
msgid "Confirm focus keywords are set for all your posts"
msgstr "Xác nhận các từ khóa trọng tâm được đặt cho tất cả các bài viết của bạn"

#: includes/modules/seo-analysis/seo-analysis-tests.php:70
msgid "Check your site for SEO-friendly permalink structure"
msgstr "Kiểm tra trang web của bạn để biết cấu trúc liên kết cố định thân thiện với SEO"

#: includes/modules/database-tools/class-database-tools.php:370
msgid "Convert FAQ, HowTo, & Table of Contents Blocks created using Yoast. Use this option to easily move your previous blocks into Rank Math."
msgstr "Chuyển đổi các khối FAQ, HowTo và Mục lục được tạo bằng Yoast. Sử dụng tùy chọn này để dễ dàng di chuyển các khối trước đó của bạn vào Rank Math."

#: includes/modules/schema/blocks/toc/block.json
msgctxt "block title"
msgid "Table of Contents by Rank Math"
msgstr "Mục lục bởi Rank Math"

#: includes/modules/schema/blocks/toc/block.json
msgctxt "block description"
msgid "Automatically generate the Table of Contents from the Headings added to this page."
msgstr "Tự động tạo Mục lục từ các Tiêu đề được thêm vào trang này."

#: includes/modules/schema/blocks/views/options-general.php:20
#: includes/modules/schema/blocks/toc/assets/js/index.js:1
#: includes/modules/schema/blocks/toc/assets/src/edit.js:104
msgid "Table of Contents"
msgstr "Mục lục"

#: includes/modules/schema/blocks/class-admin.php:46
msgid "Blocks"
msgstr "Khối"

#: includes/modules/schema/blocks/views/options-general.php:51
msgid "Heading H6"
msgstr "Tiêu đề H6"

#: includes/modules/schema/blocks/views/options-general.php:50
msgid "Heading H5"
msgstr "Tiêu đề H5"

#: includes/modules/schema/blocks/views/options-general.php:49
msgid "Heading H4"
msgstr "Tiêu đề H4"

#: includes/modules/schema/blocks/views/options-general.php:48
msgid "Heading H3"
msgstr "Tiêu đề H3"

#: includes/modules/schema/blocks/views/options-general.php:47
msgid "Heading H2"
msgstr "Tiêu đề H2"

#: includes/modules/schema/blocks/views/options-general.php:46
msgid "Heading H1"
msgstr "Tiêu đề H1"

#: includes/modules/schema/blocks/views/options-general.php:43
msgid "Choose the headings to exclude from the Table of Contents block."
msgstr "Chọn các tiêu đề để loại trừ khỏi khối Mục lục."

#: includes/modules/schema/blocks/views/options-general.php:29
msgid "Select the default list style for the Table of Contents block."
msgstr "Chọn kiểu danh sách mặc định cho khối Mục lục."

#: includes/modules/schema/blocks/views/options-general.php:28
msgid "Table of Contents List style"
msgstr "Kiểu danh sách Mục lục"

#: includes/modules/schema/blocks/views/options-general.php:18
msgid "Enter the default title to use for the Table of Contents block."
msgstr "Nhập tiêu đề mặc định để sử dụng cho khối Mục lục."

#: includes/modules/schema/blocks/views/options-general.php:42
msgid "Table of Contents Exclude Headings"
msgstr "Mục lục Loại trừ Tiêu đề"

#: includes/modules/schema/blocks/views/options-general.php:17
msgid "Table of Contents Title"
msgstr "Tiêu đề Mục lục"

#: includes/modules/schema/blocks/class-admin.php:47
msgid "Take control over the default settings available for Rank Math Blocks."
msgstr "Kiểm soát các cài đặt mặc định có sẵn cho các Khối Rank Math."

#: includes/modules/database-tools/class-database-tools.php:381
msgid "Are you sure you want to convert AIOSEO blocks into Rank Math blocks? This action is irreversible."
msgstr "Bạn có chắc chắn muốn chuyển đổi các khối AIOSEO thành các khối Rank Math không? Hành động này là không thể đảo ngược."

#: includes/modules/database-tools/class-database-tools.php:379
msgid "AIOSEO Block Converter"
msgstr "Trình chuyển đổi khối AIOSEO"

#: includes/modules/sitemap/settings/html-sitemap.php:96
msgid "Sort By"
msgstr "Sắp xếp theo"

#: includes/modules/sitemap/settings/html-sitemap.php:86
msgid "Select a page"
msgstr "Chọn một trang"

#: includes/modules/sitemap/settings/html-sitemap.php:125
#: includes/modules/sitemap/settings/html-sitemap.php:128
msgid "Item Titles"
msgstr "Tiêu đề mục"

#. translators: link to the selected page
#: includes/modules/sitemap/settings/html-sitemap.php:66
msgid "Selected page: <a href=\"%s\" target=\"_blank\" class=\"rank-math-selected-page\">%s</a>"
msgstr "Trang đã chọn: <a href=\"%s\" target=\"_blank\" class=\"rank-math-selected-page\">%s</a>"

#: includes/modules/sitemap/settings/html-sitemap.php:59
msgid "Selected page: "
msgstr "Trang đã chọn: "

#: includes/modules/sitemap/settings/taxonomies.php:32
msgid "Include archive pages for terms of this taxonomy in the HTML sitemap."
msgstr "Bao gồm các trang lưu trữ cho các thuật ngữ của phân loại này trong sơ đồ trang HTML."

#: includes/modules/sitemap/settings/html-sitemap.php:129
msgid "SEO Titles"
msgstr "Tiêu đề SEO"

#: includes/modules/sitemap/settings/html-sitemap.php:114
msgid "Show Dates"
msgstr "Hiển thị ngày"

#: includes/modules/sitemap/settings/html-sitemap.php:102
msgid "Alphabetical"
msgstr "Theo thứ tự bảng chữ cái"

#: includes/modules/sitemap/settings/html-sitemap.php:97
msgid "Choose how you want to sort the items in the HTML sitemap."
msgstr "Chọn cách bạn muốn sắp xếp các mục trong sơ đồ trang HTML."

#: includes/modules/sitemap/settings/html-sitemap.php:77
msgid "Select the page to display the HTML sitemap. Once the settings are saved, the sitemap will be displayed below the content of the selected page."
msgstr "Chọn trang để hiển thị sơ đồ trang HTML. Sau khi cài đặt được lưu, sơ đồ trang sẽ được hiển thị bên dưới nội dung của trang đã chọn."

#: includes/modules/sitemap/settings/html-sitemap.php:43
msgid "Use this shortcode to display the HTML sitemap."
msgstr "Sử dụng mã ngắn này để hiển thị sơ đồ trang HTML."

#: includes/modules/sitemap/settings/html-sitemap.php:28
msgid "Choose how you want to display the HTML sitemap."
msgstr "Chọn cách bạn muốn hiển thị sơ đồ trang HTML."

#: includes/modules/sitemap/settings/html-sitemap.php:27
msgid "Display Format"
msgstr "Định dạng hiển thị"

#: includes/modules/sitemap/settings/html-sitemap.php:18
msgid "Enable the HTML sitemap."
msgstr "Bật sơ đồ trang HTML."

#: includes/modules/sitemap/settings/authors.php:38
msgid "Include author archives in the HTML sitemap if it's enabled."
msgstr "Bao gồm các kho lưu trữ tác giả trong sơ đồ trang HTML nếu nó được bật."

#: includes/modules/sitemap/settings/html-sitemap.php:126
msgid "Show the post/term titles, or the SEO titles in the HTML sitemap."
msgstr "Hiển thị tiêu đề bài viết/thuật ngữ hoặc tiêu đề SEO trong sơ đồ trang HTML."

#: includes/modules/sitemap/settings/html-sitemap.php:115
msgid "Show published dates for each post & page."
msgstr "Hiển thị ngày xuất bản cho mỗi bài viết & trang."

#: includes/modules/sitemap/settings/post-types.php:46
msgid "Include this post type in the HTML sitemap if it's enabled."
msgstr "Bao gồm loại nội dung này trong sơ đồ trang HTML nếu nó được bật."

#: includes/modules/sitemap/settings/authors.php:37
#: includes/modules/sitemap/settings/post-types.php:45
#: includes/modules/sitemap/settings/taxonomies.php:31
msgid "Include in HTML Sitemap"
msgstr "Bao gồm trong sơ đồ trang HTML"

#: includes/modules/sitemap/settings/authors.php:28
msgid "Include author archives in the XML sitemap."
msgstr "Bao gồm các kho lưu trữ tác giả trong sơ đồ trang XML."

#: includes/modules/sitemap/class-admin.php:95
msgid "This tab contains settings related to the HTML sitemap."
msgstr "Tab này chứa các cài đặt liên quan đến sơ đồ trang HTML."

#: includes/admin/importers/class-aioseo.php:476
#: includes/modules/sitemap/class-admin.php:93
#: includes/modules/sitemap/settings/html-sitemap.php:17
msgid "HTML Sitemap"
msgstr "Sơ đồ trang HTML"

#: includes/admin/class-options.php:307
msgid "Ctrl/Cmd + Enter"
msgstr "Ctrl/Cmd + Enter"

#: includes/admin/class-admin.php:436
msgid "Exclusive Offer!"
msgstr "Ưu đãi độc quyền!"

#: includes/admin/wizard/class-your-site.php:75
#: includes/modules/local-seo/views/titles-options.php:35
#: includes/settings/titles/local.php:29
msgid "Website Name"
msgstr "Tên trang web"

#. translators: %1$s: general reading settings URL.
#: includes/admin/watcher/class-watcher.php:276
msgid "<strong>SEO Notice</strong>: Your site is set to No Index and will not appear in search engines. You can change the Search engine visibility <a href=\"%1$s\">from here</a>."
msgstr "<strong>Thông báo SEO</strong>: Trang web của bạn được đặt thành Không lập chỉ mục và sẽ không xuất hiện trong các công cụ tìm kiếm. Bạn có thể thay đổi khả năng hiển thị của công cụ tìm kiếm <a href=\"%1$s\">từ đây</a>."

#: includes/admin/wizard/class-your-site.php:96
#: includes/modules/local-seo/views/titles-options.php:55
#: includes/settings/titles/local.php:49
msgid "Your name or company name intended to feature in Google's Knowledge Panel."
msgstr "Tên của bạn hoặc tên công ty dự định xuất hiện trong Bảng tri thức của Google."

#: includes/admin/wizard/class-your-site.php:95
#: includes/modules/local-seo/views/titles-options.php:54
#: includes/settings/titles/local.php:48
msgid "Person/Organization Name"
msgstr "Tên Cá nhân/Tổ chức"

#: includes/admin/wizard/class-your-site.php:87
#: includes/modules/local-seo/views/titles-options.php:46
#: includes/settings/titles/local.php:40
msgid "An alternate version of your site name (for example, an acronym or shorter name)."
msgstr "Phiên bản thay thế của tên trang web của bạn (ví dụ: tên viết tắt hoặc tên ngắn hơn)."

#: includes/admin/wizard/class-your-site.php:86
#: includes/modules/local-seo/views/titles-options.php:45
#: includes/settings/titles/local.php:39
msgid "Website Alternate Name"
msgstr "Tên thay thế của trang web"

#: includes/admin/wizard/class-your-site.php:77
#: includes/modules/local-seo/views/titles-options.php:36
#: includes/settings/titles/local.php:30
msgid "Enter the name of your site to appear in search results."
msgstr "Nhập tên trang web của bạn để xuất hiện trong kết quả tìm kiếm."

#: includes/replace-variables/class-post-variables.php:223
msgid "Primary Terms"
msgstr "Thuật ngữ chính"

#: includes/replace-variables/class-post-variables.php:225
msgid "Output list of terms from the primary taxonomy associated to the current post."
msgstr "Xuất danh sách các thuật ngữ từ phân loại chính được liên kết với bài viết hiện tại."

#: includes/admin/wizard/class-import.php:124
msgid "Recalculate SEO Scores"
msgstr "Tính toán lại điểm SEO"

#: includes/modules/database-tools/class-database-tools.php:406
msgid "Update SEO Scores"
msgstr "Cập nhật điểm SEO"

#: includes/admin/views/import-export/plugins-panel.php:66
msgid "Calculate SEO Scores"
msgstr "Tính toán điểm SEO"

#. translators: start, end, total
#: includes/admin/importers/class-status.php:121
msgid "Recalculating scores for posts %1$s - %2$s... "
msgstr "Đang tính toán lại điểm cho các bài viết %1$s - %2$s... "

#: includes/modules/database-tools/class-update-score.php:290
msgid "The SEO Scores have been recalculated successfully!"
msgstr "Điểm SEO đã được tính toán lại thành công!"

#: includes/modules/database-tools/class-update-score.php:282
msgid "Calculated:"
msgstr "Đã tính toán:"

#: includes/modules/database-tools/class-update-score.php:278
msgid "This process may take a while. Please keep this window open until the process is complete."
msgstr "Quá trình này có thể mất một lúc. Vui lòng giữ cửa sổ này mở cho đến khi quá trình hoàn tất."

#: includes/modules/database-tools/class-update-score.php:277
msgid "Recalculating SEO Scores"
msgstr "Đang tính toán lại điểm SEO"

#: includes/modules/database-tools/class-database-tools.php:408
msgid "Recalculate Scores"
msgstr "Tính toán lại điểm"

#: includes/modules/database-tools/class-database-tools.php:407
msgid "This tool will calculate the SEO score for the posts/pages that have a Focus Keyword set. Note: This process may take some time and the browser tab must be kept open while it is running."
msgstr "Công cụ này sẽ tính toán điểm SEO cho các bài viết/trang đã đặt từ khóa chính. Lưu ý: Quá trình này có thể mất một chút thời gian và tab trình duyệt phải được giữ mở trong khi nó đang chạy."

#: includes/modules/local-seo/views/titles-options.php:75
#: includes/settings/titles/local.php:59
msgid "<strong>Min Size: 112Χ112px</strong>.<br /> A squared image is preferred by the search engines."
msgstr "<strong>Kích thước tối thiểu: 112Χ112px</strong>.<br /> Hình ảnh vuông được các công cụ tìm kiếm ưa chuộng hơn."

#: includes/module/class-manager.php:427
msgid "More powerful options are available in the PRO version."
msgstr "Nhiều tùy chọn mạnh mẽ hơn có sẵn trong phiên bản PRO."

#: includes/module/class-manager.php:204
msgid "Podcast"
msgstr "Podcast"

#: includes/module/class-manager.php:195
msgid "Video Sitemap"
msgstr "Sơ đồ trang web video"

#: includes/module/class-manager.php:186
msgid "News Sitemap"
msgstr "Sơ đồ trang web tin tức"

#: includes/module/class-manager.php:432
msgid "PRO options are enabled."
msgstr "Các tùy chọn PRO đã được bật."

#: includes/module/class-manager.php:196
msgid "For your video content, a Video Sitemap is a recommended step for better rankings and inclusion in the Video search."
msgstr "Đối với nội dung video của bạn, Sơ đồ trang web video là một bước được khuyến nghị để xếp hạng tốt hơn và được đưa vào tìm kiếm video."

#: includes/module/class-manager.php:187
msgid "Create a News Sitemap for your news-related content. You only need a News sitemap if you plan on posting news-related content on your website."
msgstr "Tạo Sơ đồ trang web Tin tức cho nội dung liên quan đến tin tức của bạn. Bạn chỉ cần sơ đồ trang web Tin tức nếu bạn định đăng nội dung liên quan đến tin tức trên trang web của mình."

#: includes/admin/wizard/class-your-site.php:107
msgid "<strong>Min Size: 112Χ112px</strong>.<br />A squared image is preferred by the search engines."
msgstr "<strong>Kích thước tối thiểu: 112Χ112px</strong>.<br />Hình ảnh vuông được các công cụ tìm kiếm ưa chuộng hơn."

#: includes/module/class-manager.php:205
msgid "Make your podcasts discoverable via Google Podcasts, Apple Podcasts, and similar services with Podcast RSS feed and Schema Markup generated by Rank Math."
msgstr "Làm cho podcast của bạn có thể khám phá được thông qua Google Podcast, Apple Podcast và các dịch vụ tương tự với nguồn cấp dữ liệu RSS Podcast và Đánh dấu schema do Rank Math tạo."

#: includes/module/class-manager.php:191 includes/module/class-manager.php:200
#: includes/module/class-manager.php:209
msgid "This module is available in the PRO version."
msgstr "Module này có sẵn trong phiên bản PRO."

#. translators: Link to kb article
#: includes/modules/content-ai/class-admin.php:90
msgid "Get sophisticated AI suggestions for related Keywords, Questions & Links to include in the SEO meta & Content Area. %s."
msgstr "Nhận các đề xuất AI tinh vi cho Từ khóa, Câu hỏi & Liên kết liên quan để bao gồm trong siêu dữ liệu SEO & Khu vực nội dung. %s."

#: includes/modules/redirections/views/debugging.php:57
msgid "Served from cache"
msgstr "Được phục vụ từ bộ nhớ cache"

#: includes/modules/redirections/views/debugging.php:17
msgid "Rank Math SEO Redirection Debugger"
msgstr "Trình gỡ lỗi chuyển hướng SEO của Rank Math"

#: includes/admin/class-admin-helper.php:461 assets/admin/js/components.js:1
msgid "WordPress General Settings"
msgstr "Cài đặt chung của WordPress"

#: includes/admin/class-admin-helper.php:460 assets/admin/js/components.js:1
msgid "Site Address (URL)"
msgstr "Địa chỉ trang web (URL)"

#. Translators: 1 is "WordPress Address (URL)", 2 is "Site Address (URL)", 3 is
#. a link to the General Settings, with "WordPress General Settings" as anchor
#. text.
#: includes/admin/class-admin-helper.php:459 assets/admin/js/components.js:1
msgid "WordPress Address (URL)"
msgstr "Địa chỉ WordPress (URL)"

#. Translators: 1 is "WordPress Address (URL)", 2 is "Site Address (URL)", 3 is
#. a link to the General Settings, with "WordPress General Settings" as anchor
#. text.
#: includes/admin/class-admin-helper.php:458 assets/admin/js/components.js:1
msgid "Rank Math cannot be connected because your site URL doesn't appear to be a valid URL. If the domain name contains special characters, please make sure to use the encoded version in the %1$s &amp; %2$s fields on the %3$s page."
msgstr "Không thể kết nối Rank Math vì URL trang web của bạn dường như không phải là URL hợp lệ. Nếu tên miền chứa ký tự đặc biệt, vui lòng đảm bảo sử dụng phiên bản được mã hóa trong trường %1$s &amp; %2$s trên trang %3$s."

#: includes/settings/titles/misc.php:130
msgid "Noindex Subpages"
msgstr "Không lập chỉ mục các trang con"

#. Translators: %s is the plugin name.
#: includes/admin/class-import-export.php:151
msgid "Select data to import."
msgstr "Chọn dữ liệu để nhập."

#: includes/admin/class-admin-helper.php:172
msgid "Click here to reconnect."
msgstr "Nhấp vào đây để kết nối lại."

#: includes/admin/class-admin-helper.php:172
msgid "Seems like your site URL has changed since you connected to Rank Math."
msgstr "Có vẻ như URL trang web của bạn đã thay đổi kể từ khi bạn kết nối với Rank Math."

#: includes/settings/titles/misc.php:141
msgid "Noindex Paginated Single Pages"
msgstr "Không lập chỉ mục các trang đơn lẻ được phân trang"

#. Translators: %s is the plugin name.
#: includes/admin/class-import-export.php:156
msgid "Are you sure you want erase all traces of %s?"
msgstr "Bạn có chắc chắn muốn xóa tất cả dấu vết của %s không?"

#. Translators: %s is the plugin name.
#: includes/admin/class-import-export.php:150
msgid "Are you sure you want to import data from %s?"
msgstr "Bạn có chắc chắn muốn nhập dữ liệu từ %s không?"

#: includes/settings/titles/misc.php:142
msgid "Prevent paginated pages of single pages and posts to show up in the search results. This also applies for the Blog page."
msgstr "Ngăn các trang được phân trang của các trang đơn lẻ và bài viết hiển thị trong kết quả tìm kiếm. Điều này cũng áp dụng cho trang Blog."

#. Translators: %s is "Article" inside a <code> tag.
#: includes/settings/titles/post-types.php:163
msgctxt "Schema type name in a field description"
msgid "Article"
msgstr "Bài viết"

#. translators: The settings page link
#: includes/modules/sitemap/settings/post-types.php:24
#: includes/settings/titles/post-types.php:22
msgid "disable redirect attachments"
msgstr "tắt chuyển hướng tệp đính kèm"

#. translators: %1$s: opening tag of the link, %2$s: the closing tag
#: includes/modules/seo-analysis/seo-analysis-tests.php:514
msgid "Navigate to %1$sSettings > Reading%2$s and turn off this option: \"Discourage search engines from indexing this site\"."
msgstr "Điều hướng đến %1$sCài đặt > Đọc%2$s và tắt tùy chọn này: \"Không khuyến khích các công cụ tìm kiếm lập chỉ mục trang web này\"."

#: includes/admin/class-admin-helper.php:159
msgid "[3. Unable to Encrypt]"
msgstr "[3. Không thể mã hóa]"

#: includes/admin/class-admin-helper.php:155
msgid "Please try reconnecting."
msgstr "Vui lòng thử kết nối lại."

#: includes/admin/class-admin-helper.php:154
msgid "Unable to validate Rank Math SEO registration data."
msgstr "Không thể xác thực dữ liệu đăng ký Rank Math SEO."

#: includes/admin/wizard/views/compatibility.php:116
msgid "Base64 encode &amp; decode functions missing"
msgstr "Mã hóa Base64 &amp; thiếu chức năng giải mã"

#: includes/admin/wizard/views/compatibility.php:116
msgid "Base64 encode &amp; decode functions available"
msgstr "Mã hóa Base64 &amp; chức năng giải mã có sẵn"

#. translators: The settings page link
#: includes/modules/sitemap/settings/post-types.php:24
#: includes/settings/titles/post-types.php:22
msgid "To configure meta tags for your media attachment pages, you need to first %s to parent."
msgstr "Để định cấu hình thẻ meta cho các trang tệp đính kèm phương tiện của bạn, trước tiên bạn cần %s thành trang mẹ."

#. translators: KB Link
#: includes/admin/class-admin-helper.php:158
msgid "If the issue persists, please try the solution described in our Knowledge Base article: %s"
msgstr "Nếu sự cố vẫn tiếp diễn, vui lòng thử giải pháp được mô tả trong bài viết Cơ sở tri thức của chúng tôi: %s"

#. Translators: %s is "Article" inside a <code> tag.
#: includes/settings/titles/post-types.php:162
msgid "Default rich snippet selected when creating a new post of this type. If %s is selected, it will be applied for all existing posts with no Schema selected."
msgstr "Đoạn trích phong phú mặc định được chọn khi tạo bài viết mới thuộc loại này. Nếu %s được chọn, nó sẽ được áp dụng cho tất cả các bài viết hiện có mà không có Schema nào được chọn."

#: includes/modules/database-tools/class-database-tools.php:361
msgid "In some cases, the Analytics database tables or columns don't match with each other, which can cause database errors. This tool can fix that issue."
msgstr "Trong một số trường hợp, các bảng hoặc cột cơ sở dữ liệu Analytics không khớp với nhau, điều này có thể gây ra lỗi cơ sở dữ liệu. Công cụ này có thể khắc phục sự cố đó."

#: includes/modules/analytics/class-analytics-common.php:206
msgid "No collation mismatch to fix."
msgstr "Không có sự không khớp đối chiếu nào để sửa."

#: includes/admin/wizard/views/search-console-ui.php:231
#: assets/admin/js/common.js:1
msgid "Data Stream"
msgstr "Luồng dữ liệu"

#: includes/admin/wizard/views/search-console-ui.php:201
msgid "Click here to know how"
msgstr "Nhấp vào đây để biết cách"

#: includes/admin/wizard/views/search-console-ui.php:200
msgid "Ready to switch to Google Analytics 4? %s"
msgstr "Sẵn sàng chuyển sang Google Analytics 4? %s"

#: includes/admin/wizard/class-compatibility.php:56
msgid "You can easily switch between modes at any point."
msgstr "Bạn có thể dễ dàng chuyển đổi giữa các chế độ bất kỳ lúc nào."

#: includes/admin/wizard/class-compatibility.php:56
#: includes/admin/wizard/views/search-console-ui.php:196
msgid "Note"
msgstr "Lưu ý"

#: includes/modules/database-tools/class-database-tools.php:362
msgid "Fix Collations"
msgstr "Sửa đổi đối chiếu"

#: includes/modules/database-tools/class-database-tools.php:360
msgid "Fix Analytics table collations"
msgstr "Sửa đổi đối chiếu bảng Analytics"

#. translators: %1$d: number of changes, %2$s: new collation.
#: includes/modules/analytics/class-analytics-common.php:203
msgid "%1$d collation changed to %2$s."
msgid_plural "%1$d collations changed to %2$s."
msgstr[0] "%1$d đối chiếu đã được thay đổi thành %2$s."

#: includes/helpers/class-choices.php:77
msgid "Prevents images on a page from being indexed by Google and other search engines"
msgstr "Ngăn hình ảnh trên trang web bị Google và các công cụ tìm kiếm khác lập chỉ mục"

#: includes/admin/class-dashboard-widget.php:78
msgid "Error: the Rank Math blog feed could not be downloaded."
msgstr "Lỗi: không thể tải xuống nguồn cấp dữ liệu blog của Rank Math."

#: includes/modules/analytics/views/options.php:115
msgid "Frontend Stats Bar"
msgstr "Thanh thống kê Frontend"

#: includes/modules/analytics/views/options.php:116
msgid "Enable this option to show Analytics Stats on the front just after the admin bar."
msgstr "Bật tùy chọn này để hiển thị Thống kê phân tích ở mặt trước ngay sau thanh quản trị."

#: includes/modules/status/class-system-status.php:188
msgid "Database Table: Inspections"
msgstr "Bảng cơ sở dữ liệu: Kiểm tra"

#: includes/admin/wizard/views/search-console-ui.php:159
msgid "Enable the Index Status tab"
msgstr "Bật tab Trạng thái chỉ mục"

#: includes/modules/analytics/google/class-request.php:350
msgid "Unknown error, call get_response() to find out what happened."
msgstr "Lỗi không xác định, hãy gọi get_response() để tìm hiểu điều gì đã xảy ra."

#: includes/admin/wizard/views/search-console-ui.php:160
msgid "Enable this option to show the Index Status tab in the Analytics module."
msgstr "Bật tùy chọn này để hiển thị tab Trạng thái chỉ mục trong module Analytics."

#. Translators: Placeholder expands to number of redirections.
#: includes/modules/redirections/class-import-export.php:86
msgid "Warning: you have more than %d active redirections. Exporting them to your .htaccess file may cause performance issues."
msgstr "Cảnh báo: bạn có hơn %d chuyển hướng đang hoạt động. Xuất chúng sang tệp .htaccess của bạn có thể gây ra sự cố về hiệu suất."

#: includes/modules/instant-indexing/views/options.php:42
msgid "Check Key"
msgstr "Kiểm tra khóa"

#: includes/modules/instant-indexing/views/options.php:41
msgid "API Key Location"
msgstr "Vị trí khóa API"

#: includes/modules/instant-indexing/views/options.php:32
msgid "Change Key"
msgstr "Thay đổi khóa"

#: includes/modules/instant-indexing/views/options.php:30
msgid "The IndexNow API key proves the ownership of the site. It is generated automatically. You can change the key if it becomes known to third parties."
msgstr "Khóa API IndexNow chứng minh quyền sở hữu của trang web. Nó được tạo tự động. Bạn có thể thay đổi khóa nếu bên thứ ba biết được."

#: includes/modules/instant-indexing/views/options.php:29
msgid "API Key"
msgstr "Khóa API"

#. Translators: %s is the words "Check Key".
#: includes/modules/instant-indexing/views/options.php:45
msgid "Use the %1$s button to verify that the key is accessible for search engines. Clicking on it should open the key file in your browser and show the API key."
msgstr "Sử dụng nút %1$s để xác minh rằng khóa có thể truy cập được đối với các công cụ tìm kiếm. Nhấp vào đó sẽ mở tệp khóa trong trình duyệt của bạn và hiển thị khóa API."

#: includes/admin/wizard/views/compatibility.php:98
msgid "PHP GD or Imagick Extension missing"
msgstr "Tiện ích mở rộng PHP GD hoặc Imagick bị thiếu"

#: includes/admin/wizard/views/compatibility.php:98
msgid "PHP GD or Imagick Extension installed"
msgstr "Tiện ích mở rộng PHP GD hoặc Imagick đã được cài đặt"

#: includes/admin/class-pro-notice.php:218
#: includes/admin/class-pro-notice.php:237 assets/admin/js/post-list.js:1
#: includes/modules/content-ai/assets/js/content-ai-media.js:1
#: includes/modules/content-ai/assets/js/content-ai-page.js:1
#: includes/modules/content-ai/assets/js/content-ai.js:1
msgid "Keyword Rank Tracker"
msgstr "Trình theo dõi thứ hạng từ khóa"

#: includes/admin/class-pro-notice.php:222
#: includes/admin/class-pro-notice.php:241
msgid "and Many More…"
msgstr "và nhiều hơn nữa…"

#: includes/admin/class-pro-notice.php:220
#: includes/admin/class-pro-notice.php:239
msgid "24x7 Premium Support"
msgstr "Hỗ trợ cao cấp 24/7"

#: includes/admin/class-pro-notice.php:221
#: includes/admin/class-pro-notice.php:240
msgid "SEO Email Reports"
msgstr "Báo cáo email SEO"

#: includes/admin/class-pro-notice.php:217
#: includes/admin/class-pro-notice.php:236
msgid "Content A.I. (Artificial Intelligence)"
msgstr "AI nội dung (Trí tuệ nhân tạo)"

#: includes/admin/class-pro-notice.php:216
#: includes/admin/class-pro-notice.php:235
msgid "Unlimited Websites"
msgstr "Trang web không giới hạn"

#: includes/admin/class-pro-notice.php:213
#: includes/admin/class-pro-notice.php:232
msgid "Rank Your Content With the Power of PRO & A.I."
msgstr "Xếp hạng nội dung của bạn với sức mạnh của PRO & AI."

#: includes/modules/instant-indexing/views/history.php:13
msgid "Auto"
msgstr "Tự động"

#: includes/modules/instant-indexing/views/history.php:37
msgid "Too Many Requests"
msgstr "Quá nhiều yêu cầu"

#: includes/modules/instant-indexing/views/history.php:36
msgid "Unprocessable Entity"
msgstr "Thực thể không thể xử lý"

#: includes/modules/instant-indexing/views/history.php:35
msgid "The key was invalid (e.g. key not found, file found but key not in the file)."
msgstr "Khóa không hợp lệ (ví dụ: không tìm thấy khóa, tìm thấy tệp nhưng không có khóa trong tệp)."

#: includes/modules/instant-indexing/views/history.php:35
msgid "Forbidden"
msgstr "Bị cấm"

#: includes/modules/instant-indexing/views/history.php:34
msgid "The request was invalid."
msgstr "Yêu cầu không hợp lệ."

#: includes/modules/instant-indexing/views/history.php:34
msgid "Bad Request"
msgstr "Yêu cầu không hợp lệ"

#: includes/modules/instant-indexing/views/history.php:33
msgid "The URL was successfully submitted to the IndexNow API, but the API key will be checked later."
msgstr "URL đã được gửi thành công đến API IndexNow, nhưng khóa API sẽ được kiểm tra sau."

#: includes/modules/instant-indexing/views/history.php:32
msgid "The URL was successfully submitted to the IndexNow API."
msgstr "URL đã được gửi thành công đến API IndexNow."

#: includes/modules/instant-indexing/views/history.php:31
msgid "Reasons"
msgstr "Lý do"

#: includes/modules/instant-indexing/views/history.php:31
msgid "Response Message"
msgstr "Tin nhắn phản hồi"

#: includes/modules/instant-indexing/views/history.php:15
msgid "Response"
msgstr "Phản hồi"

#: includes/modules/instant-indexing/views/history.php:13
msgid "Manual"
msgstr "Thủ công"

#: includes/modules/instant-indexing/views/console.php:26
msgctxt "URL slug placeholder"
msgid "hello-world"
msgstr "xin-chào-thế-giới"

#: includes/modules/instant-indexing/views/console.php:15
msgid "Insert URLs to send to the IndexNow API (one per line, up to 10,000):"
msgstr "Chèn URL để gửi đến API IndexNow (mỗi dòng một URL, tối đa 10.000):"

#. Translators: placeholder is human-readable time, e.g. "1 hour".
#: includes/modules/instant-indexing/class-rest.php:155
msgid "%s ago"
msgstr "%s trước"

#: includes/modules/instant-indexing/class-rest.php:120
msgid "Failed to submit URLs. See details in the History tab."
msgstr "Không thể gửi URL. Xem chi tiết trong tab Lịch sử."

#: includes/modules/instant-indexing/class-rest.php:115
msgid "Invalid URLs provided."
msgstr "URL được cung cấp không hợp lệ."

#: includes/modules/instant-indexing/class-rest.php:108
msgid "No URLs provided."
msgstr "Không có URL nào được cung cấp."

#: includes/modules/instant-indexing/class-instant-indexing.php:451
msgid "Error submitting page to IndexNow."
msgstr "Lỗi khi gửi trang đến IndexNow."

#: includes/modules/instant-indexing/class-instant-indexing.php:371
msgid "Error: could not get history."
msgstr "Lỗi: không thể lấy lịch sử."

#: includes/modules/instant-indexing/class-instant-indexing.php:370
msgid "Error: could not clear history."
msgstr "Lỗi: không thể xóa lịch sử."

#: includes/modules/instant-indexing/class-instant-indexing.php:369
msgid "An error occurred while submitting the URL."
msgstr "Đã xảy ra lỗi khi gửi URL."

#: includes/modules/instant-indexing/class-instant-indexing.php:228
msgid "The last 100 IndexNow API requests."
msgstr "100 yêu cầu API IndexNow gần đây nhất."

#: includes/modules/content-ai/class-content-ai-page.php:167
#: includes/modules/instant-indexing/class-instant-indexing.php:227
#: includes/modules/content-ai/assets/js/content-ai-page.js:1
#: includes/modules/content-ai/assets/js/content-ai.js:1
msgid "History"
msgstr "Lịch sử"

#: includes/modules/instant-indexing/class-instant-indexing.php:214
msgid "Send URLs directly to the IndexNow API."
msgstr "Gửi URL trực tiếp đến API IndexNow."

#: includes/modules/instant-indexing/class-instant-indexing.php:144
msgid "Instant Indexing: Submit Page"
msgstr "Lập chỉ mục tức thì: Gửi trang"

#: includes/modules/instant-indexing/class-instant-indexing.php:108
msgid "Instant Indexing: Submit Pages"
msgstr "Lập chỉ mục tức thì: Gửi trang"

#: includes/modules/instant-indexing/class-api.php:361
msgid "Internal server error."
msgstr "Lỗi máy chủ nội bộ."

#: includes/modules/instant-indexing/class-api.php:360
msgid "Too many requests."
msgstr "Quá nhiều yêu cầu."

#: includes/modules/instant-indexing/class-api.php:359
msgid "Invalid URL."
msgstr "URL không hợp lệ."

#: includes/modules/instant-indexing/class-api.php:358
msgid "Invalid API key."
msgstr "Khóa API không hợp lệ."

#: includes/modules/instant-indexing/class-api.php:357
msgid "Invalid request."
msgstr "Yêu cầu không hợp lệ."

#: includes/modules/instant-indexing/class-api.php:355
msgid "Unknown error."
msgstr "Lỗi không xác định."

#. Translators: placeholder is "IndexNow API".
#: includes/module/class-manager.php:169
msgid "IndexNow API"
msgstr "API IndexNow"

#: includes/modules/instant-indexing/views/history.php:37
msgid "Too Many Requests (potential Spam)."
msgstr "Quá nhiều yêu cầu (tiềm ẩn thư rác)."

#: includes/modules/instant-indexing/views/history.php:33
msgid "Accepted"
msgstr "Đã chấp nhận"

#: includes/modules/instant-indexing/views/history.php:29
msgid "Response Code Help"
msgstr "Trợ giúp mã phản hồi"

#: includes/modules/instant-indexing/views/history.php:12
#: includes/modules/content-ai/assets/js/content-ai-page.js:1
#: includes/modules/content-ai/assets/js/content-ai.js:1
msgid "Clear History"
msgstr "Xóa lịch sử"

#. Translators: %s is the number of URLs submitted.
#: includes/modules/instant-indexing/class-rest.php:129
msgid "Successfully submitted %s URL."
msgid_plural "Successfully submitted %s URLs."
msgstr[0] "Đã gửi thành công %s URL."

#. translators: %s: Number of pages submitted.
#: includes/modules/instant-indexing/class-instant-indexing.php:457
msgid "%s page submitted to IndexNow."
msgid_plural "%s pages submitted to IndexNow."
msgstr[0] "%s trang đã được gửi đến IndexNow."

#: includes/modules/instant-indexing/class-instant-indexing.php:372
msgid "No submissions yet."
msgstr "Chưa có bài nộp nào."

#. Translators: placeholder is "IndexNow API".
#: includes/module/class-manager.php:169
msgid "Directly notify search engines like Bing & Yandex using the %s when pages are added, updated and removed, or submit URLs manually."
msgstr "Thông báo trực tiếp cho các công cụ tìm kiếm như Bing & Yandex bằng cách sử dụng %s khi các trang được thêm vào, cập nhật và xóa hoặc gửi URL theo cách thủ công."

#: includes/modules/instant-indexing/views/history.php:36
msgid "The URLs don't belong to the host or the key is not matching the schema in the protocol."
msgstr "URL không thuộc về máy chủ hoặc khóa không khớp với schema trong giao thức."

#: includes/modules/instant-indexing/views/options.php:20
msgid "Auto-Submit Post Types"
msgstr "Loại nội dung tự động gửi"

#: includes/modules/instant-indexing/views/options.php:21
msgid "Submit posts from these post types automatically to the IndexNow API when a post is published, updated, or trashed."
msgstr "Gửi bài viết từ các loại nội dung này tự động đến API IndexNow khi bài viết được xuất bản, cập nhật hoặc chuyển vào thùng rác."

#: includes/module/class-manager.php:534
msgid "Track 500 Keywords"
msgstr "Theo dõi 500 từ khóa"

#: includes/module/class-manager.php:540
msgid "Buy"
msgstr "Mua"

#: includes/module/class-manager.php:536
msgid "24/7 Support"
msgstr "Hỗ trợ 24/7"

#: includes/module/class-manager.php:530
msgid "Take SEO to the Next Level!"
msgstr "Đưa SEO lên một tầm cao mới!"

#: includes/admin/class-options.php:287
msgid "Click here to see all the exciting features."
msgstr "Nhấp vào đây để xem tất cả các tính năng thú vị."

#: includes/admin/class-options.php:285
msgid "Take your SEO to the Next Level!"
msgstr "Đưa SEO của bạn lên một tầm cao mới!"

#: includes/admin/class-options.php:286
msgid "Get Rank Math PRO!"
msgstr "Nhận Rank Math PRO!"

#: includes/module/class-manager.php:533
msgid "Free 15 Content AI Credits"
msgstr "Miễn phí 15 điểm AI nội dung"

#: includes/admin/class-pro-notice.php:219
#: includes/admin/class-pro-notice.php:238
#: includes/module/class-manager.php:535
msgid "Powerful Schema Generator"
msgstr "Trình tạo Schema mạnh mẽ"

#: includes/module/class-manager.php:532
msgid "Unlimited personal websites"
msgstr "Website cá nhân không giới hạn"

#: includes/settings/titles/taxonomies.php:115
msgid "When the option is enabled and a term from this taxonomy is shared on Slack, additional information will be shown (the total number of items with this term)."
msgstr "Khi tùy chọn được bật và một thuật ngữ từ phân loại này được chia sẻ trên Slack, thông tin bổ sung sẽ được hiển thị (tổng số mục có thuật ngữ này)."

#: includes/settings/titles/post-types.php:57
msgid "When the option is enabled and a product is shared on Slack, additional information will be shown (price)."
msgstr "Khi tùy chọn được bật và một sản phẩm được chia sẻ trên Slack, thông tin bổ sung sẽ được hiển thị (giá)."

#: includes/settings/titles/post-types.php:55
msgid "When the option is enabled and a product is shared on Slack, additional information will be shown (price & availability)."
msgstr "Khi tùy chọn được bật và một sản phẩm được chia sẻ trên Slack, thông tin bổ sung sẽ được hiển thị (giá & tình trạng còn hàng)."

#: includes/settings/titles/author.php:125
#: includes/settings/titles/post-types.php:343
#: includes/settings/titles/taxonomies.php:114
msgid "Slack Enhanced Sharing"
msgstr "Chia sẻ nâng cao trên Slack"

#: includes/opengraph/class-slack.php:266
msgid "Written by"
msgstr "Được viết bởi"

#: includes/modules/sitemap/class-admin.php:252
msgid "Exclude this attachment from sitemap"
msgstr "Loại trừ tệp đính kèm này khỏi sơ đồ trang web"

#: includes/settings/titles/post-types.php:53
msgid "When the option is enabled and a page is shared on Slack, additional information will be shown (estimated time to read)."
msgstr "Khi tùy chọn được bật và một trang được chia sẻ trên Slack, thông tin bổ sung sẽ được hiển thị (thời gian ước tính để đọc)."

#. Translators: Post type name.
#: includes/settings/titles/post-types.php:51
msgid "When the option is enabled and a %s is shared on Slack, additional information will be shown (estimated time to read and author)."
msgstr "Khi tùy chọn được bật và một %s được chia sẻ trên Slack, thông tin bổ sung sẽ được hiển thị (thời gian ước tính để đọc và tác giả)."

#: includes/opengraph/class-slack.php:316
msgid "Less than a minute"
msgstr "Chưa đến một phút"

#: includes/opengraph/class-slack.php:267
#: includes/opengraph/class-slack.php:281
msgid "Time to read"
msgstr "Thời gian để đọc"

#: includes/settings/titles/author.php:126
msgid "When the option is enabled and an author archive is shared on Slack, additional information will be shown (name & total number of posts)."
msgstr "Khi tùy chọn được bật và kho lưu trữ tác giả được chia sẻ trên Slack, thông tin bổ sung sẽ được hiển thị (tên & tổng số bài viết)."

#: includes/settings/general/others.php:18
msgid "Headless CMS Support"
msgstr "Hỗ trợ CMS không đầu"

#: includes/modules/content-ai/views/options.php:214
#: includes/modules/content-ai/assets/js/content-ai.js:1
msgid "Click to refresh the available credits."
msgstr "Nhấp để làm mới số dư có sẵn."

#. Translators: placeholder is a link to "Read more".
#: includes/settings/general/others.php:20
msgid "Enable this option to register a REST API endpoint that returns the HTML meta tags for a given URL. %s"
msgstr "Bật tùy chọn này để đăng ký một điểm cuối REST API trả về các thẻ meta HTML cho một URL nhất định. %s"

#: includes/rest/class-headless.php:62
msgid "URL to get HTML tags for."
msgstr "URL để lấy thẻ HTML cho."

#: includes/modules/content-ai/class-content-ai-page.php:88
#: includes/modules/seo-analysis/class-admin.php:82
#: includes/modules/content-ai/assets/js/content-ai.js:1
msgid "New!"
msgstr "Mới!"

#: includes/modules/content-ai/views/options.php:47
msgid "Default Country"
msgstr "Quốc gia mặc định"

#: includes/modules/content-ai/class-rest.php:568
msgid "This feature is not available on the localhost."
msgstr "Tính năng này không khả dụng trên localhost."

#: includes/modules/content-ai/class-rest.php:193
msgid "Sorry, only authenticated users can research the keyword."
msgstr "Xin lỗi, chỉ người dùng đã xác thực mới có thể nghiên cứu từ khóa."

#: includes/module/class-manager.php:418
msgid "NEW!"
msgstr "MỚI!"

#: includes/module/class-manager.php:380
msgid "You cant access this page."
msgstr "Bạn không thể truy cập trang này."

#: includes/helpers/class-choices.php:750
msgid "Viet Nam"
msgstr "Việt Nam"

#: includes/helpers/class-choices.php:748
msgid "Uruguay"
msgstr "Uruguay"

#: includes/helpers/class-choices.php:747
msgid "United States Of America"
msgstr "Hoa Kỳ"

#: includes/helpers/class-choices.php:742
msgid "Tunisia"
msgstr "Tunisia"

#: includes/helpers/class-choices.php:737
msgid "Sri Lanka"
msgstr "Sri Lanka"

#: includes/helpers/class-choices.php:734
msgid "Slovenia"
msgstr "Slovenia"

#: includes/helpers/class-choices.php:733
msgid "Slovakia"
msgstr "Slovakia"

#: includes/helpers/class-choices.php:731
msgid "Serbia"
msgstr "Serbia"

#: includes/helpers/class-choices.php:730
msgid "Senegal"
msgstr "Senegal"

#: includes/helpers/class-choices.php:728
msgid "Russian Federation"
msgstr "Liên bang Nga"

#: includes/helpers/class-choices.php:723
msgid "Peru"
msgstr "Peru"

#: includes/helpers/class-choices.php:722
msgid "Paraguay"
msgstr "Paraguay"

#: includes/helpers/class-choices.php:721
msgid "Pakistan"
msgstr "Pakistan"

#: includes/helpers/class-choices.php:718
msgid "Nicaragua"
msgstr "Nicaragua"

#: includes/helpers/class-choices.php:715
msgid "Myanmar"
msgstr "Myanmar"

#: includes/helpers/class-choices.php:714
msgid "Morocco"
msgstr "Morocco"

#: includes/helpers/class-choices.php:712
msgid "Malta"
msgstr "Malta"

#: includes/helpers/class-choices.php:709
msgid "Lithuania"
msgstr "Lithuania"

#: includes/helpers/class-choices.php:708
msgid "Latvia"
msgstr "Latvia"

#: includes/helpers/class-choices.php:707
msgid "Korea, Republic Of"
msgstr "Hàn Quốc"

#: includes/helpers/class-choices.php:705
msgid "Kazakhstan"
msgstr "Kazakhstan"

#: includes/helpers/class-choices.php:704
msgid "Jordan"
msgstr "Jordan"

#: includes/helpers/class-choices.php:695
msgid "Guatemala"
msgstr "Guatemala"

#: includes/helpers/class-choices.php:693
msgid "Ghana"
msgstr "Ghana"

#: includes/helpers/class-choices.php:689
msgid "Estonia"
msgstr "Estonia"

#: includes/helpers/class-choices.php:688
msgid "El Salvador"
msgstr "El Salvador"

#: includes/helpers/class-choices.php:686
msgid "Ecuador"
msgstr "Ecuador"

#: includes/helpers/class-choices.php:683
msgid "Cyprus"
msgstr "Cyprus"

#: includes/helpers/class-choices.php:682
msgid "Croatia"
msgstr "Croatia"

#: includes/helpers/class-choices.php:681
msgid "Costa Rica"
msgstr "Costa Rica"

#: includes/helpers/class-choices.php:677
msgid "Cambodia"
msgstr "Campuchia"

#: includes/helpers/class-choices.php:676
msgid "Bulgaria"
msgstr "Bulgaria"

#: includes/helpers/class-choices.php:672
msgid "Belarus"
msgstr "Belarus"

#: includes/helpers/class-choices.php:671
msgid "Bangladesh"
msgstr "Bangladesh"

#: includes/helpers/class-choices.php:670
msgid "Bahrain"
msgstr "Bahrain"

#: includes/helpers/class-choices.php:669
msgid "Azerbaijan"
msgstr "Azerbaijan"

#: includes/helpers/class-choices.php:666
msgid "Armenia"
msgstr "Armenia"

#: includes/helpers/class-choices.php:664
msgid "Algeria"
msgstr "Algeria"

#: includes/helpers/class-choices.php:749
msgid "Venezuela, Bolivarian Republic Of"
msgstr "Venezuela, Cộng hòa Bolivar"

#: includes/modules/content-ai/class-rest.php:574
msgid "You have used all the free credits which are allowed to this domain."
msgstr "Bạn đã sử dụng hết số dư miễn phí được phép cho miền này."

#: includes/modules/content-ai/class-rest.php:545
msgid "No data found for the researched keyword."
msgstr "Không tìm thấy dữ liệu cho từ khóa đã nghiên cứu."

#: includes/module/class-manager.php:177
msgid "Get sophisticated AI suggestions for related Keywords, Questions & Links to include in the SEO meta & Content Area. Supports 80+ Countries."
msgstr "Nhận các đề xuất AI tinh vi cho Từ khóa, Câu hỏi & Liên kết liên quan để bao gồm trong siêu dữ liệu SEO & Khu vực nội dung. Hỗ trợ hơn 80 quốc gia."

#: includes/module/class-manager.php:176
#: includes/modules/content-ai/class-admin.php:88
#: includes/modules/content-ai/class-admin.php:112
#: includes/modules/content-ai/class-content-ai-page.php:96
#: includes/modules/content-ai/class-content-ai-page.php:141
#: includes/modules/role-manager/class-capability-manager.php:70
#: includes/modules/content-ai/assets/js/content-ai-page.js:1
#: includes/modules/content-ai/assets/js/content-ai.js:1
msgid "Content AI"
msgstr "Content AI"

#: includes/helpers/class-choices.php:745
msgid "United Arab Emirates"
msgstr "Các tiểu vương quốc Ả Rập thống nhất"

#: includes/helpers/class-choices.php:710
msgid "Macedonia, The Former Yugoslav Republic Of"
msgstr "Macedonia, Cộng hòa Nam Tư cũ"

#: includes/helpers/class-choices.php:674
msgid "Bolivia, Plurinational State Of"
msgstr "Bolivia, Nhà nước Đa quốc gia"

#: includes/modules/content-ai/views/options.php:203
msgid "Select Post Type"
msgstr "Chọn loại nội dung"

#: includes/modules/content-ai/class-rest.php:239
msgid "Content AI is not enabled on this Post type."
msgstr "Content AI không được bật trên loại nội dung này."

#: includes/settings/titles/global.php:77
msgid "Automatically capitalize the first character of each word in the titles."
msgstr "Tự động viết hoa chữ cái đầu tiên của mỗi từ trong tiêu đề."

#: includes/modules/sitemap/class-admin.php:346
msgid "I already added"
msgstr "Tôi đã thêm"

#: includes/settings/titles/global.php:87
msgid "When a featured image or an OpenGraph Image is not set for individual posts/pages/CPTs, this image will be used as a fallback thumbnail when your post is shared on Facebook. The recommended image size is 1200 x 630 pixels."
msgstr "Khi hình ảnh nổi bật hoặc Hình ảnh OpenGraph không được đặt cho các bài viết / trang / CPT riêng lẻ, hình ảnh này sẽ được sử dụng làm hình thu nhỏ dự phòng khi bài viết của bạn được chia sẻ trên Facebook. Kích thước hình ảnh được đề xuất là 1200 x 630 pixel."

#: includes/modules/robots-txt/options.php:27
msgid "Rank Math could not detect if a robots.txt file exists or not because of a filesystem issue. The file contents entered here may not be applied."
msgstr "Rank Math không thể phát hiện xem tệp robots.txt có tồn tại hay không do sự cố hệ thống tệp. Nội dung tệp được nhập ở đây có thể không được áp dụng."

#. Translators: placeholders are anchor opening and closing tags.
#: includes/modules/analytics/views/email-reports/header-after.php:37
msgid "If you can see the site data in your Search Console and Analytics accounts, but not here, then %1$s try reconnecting your account %2$s and make sure that the correct properties are selected in the %1$s Analytics Settings%2$s."
msgstr "Nếu bạn có thể thấy dữ liệu trang web trong tài khoản Search Console và Analytics của mình, nhưng không thấy ở đây, thì %1$shãy thử kết nối lại tài khoản của bạn%2$s và đảm bảo rằng các thuộc tính chính xác được chọn trong %1$sCài đặt Analytics%2$s."

#: includes/modules/analytics/views/email-reports/header-after.php:35
msgid "It seems that there are no stats to show right now."
msgstr "Có vẻ như hiện tại không có số liệu thống kê nào để hiển thị."

#: includes/modules/analytics/views/email-reports/header-after.php:34
msgid "Uh-oh"
msgstr "Ôi trời"

#: includes/admin/class-import-export.php:352
msgid "Uploaded file could not be read."
msgstr "Không thể đọc tệp đã tải lên."

#. translators: help link
#: includes/admin/wizard/views/your-site.php:31
msgid "If you are new to Rank Math, %s to learn more."
msgstr "Nếu bạn mới sử dụng Rank Math, %s để tìm hiểu thêm."

#: includes/admin/wizard/class-your-site.php:117
msgid "When a featured image or an OpenGraph Image is not set for individual posts/pages/CPTs, this image will be used as a fallback thumbnail when your post is shared on Facebook. <strong>The recommended image size is 1200 x 630 pixels.</strong>"
msgstr "Khi hình ảnh nổi bật hoặc Hình ảnh OpenGraph không được đặt cho các bài viết / trang / CPT riêng lẻ, hình ảnh này sẽ được sử dụng làm hình thu nhỏ dự phòng khi bài viết của bạn được chia sẻ trên Facebook. <strong>Kích thước hình ảnh được đề xuất là 1200 x 630 pixel.</strong>"

#: includes/helpers/class-choices.php:736
msgid "Spain"
msgstr "Tây Ban Nha"

#. Translators: placeholder is the number of modified posts.
#: includes/modules/database-tools/class-aioseo-blocks.php:78
#: includes/modules/database-tools/class-yoast-blocks.php:106
msgid "Blocks successfully converted in %d post."
msgid_plural "Blocks successfully converted in %d posts."
msgstr[0] "Khối đã được chuyển đổi thành công trong %d bài viết."

#: includes/modules/seo-analysis/seo-analysis-tests.php:318
msgid "Could not check Focus Keywords in posts - the post meta table exceeds the size limit."
msgstr "Không thể kiểm tra từ khoá chính trong bài viết - bảng meta bài viết vượt quá giới hạn kích thước."

#: includes/admin/class-pro-notice.php:244
msgid "I already purchased"
msgstr "Tôi đã mua"

#: includes/admin/importers/class-aioseo.php:77
msgid "Import all the redirections you have already set up in AIO SEO Premium."
msgstr "Nhập tất cả các chuyển hướng bạn đã thiết lập trong AIO SEO Premium."

#: includes/rest/class-rest-helper.php:169
msgid "Sorry, you are not allowed to edit this term."
msgstr "Xin lỗi, bạn không được phép chỉnh sửa thuật ngữ này."

#: includes/admin/class-pro-notice.php:244
msgid "Yes, I want better SEO"
msgstr "Có, tôi muốn SEO tốt hơn"

#: includes/modules/analytics/class-analytics-common.php:325
#: includes/modules/analytics/assets/js/stats.js:1
msgid "Average position of all the ranking keywords below 100 position."
msgstr "Vị trí trung bình của tất cả các từ khóa xếp hạng dưới vị trí 100."

#: includes/rest/class-rest-helper.php:187
msgid "Invalid term ID."
msgstr "ID thuật ngữ không hợp lệ."

#: includes/admin/importers/class-aioseo.php:81
msgid "Import Locations Settings."
msgstr "Nhập cài đặt địa điểm."

#: includes/settings/titles/author.php:137
#: includes/settings/titles/post-types.php:354
#: includes/settings/titles/taxonomies.php:125
msgid "Add SEO Controls"
msgstr "Thêm điều khiển SEO"

#: includes/admin/class-pro-notice.php:225
msgid "I already upgraded"
msgstr "Tôi đã nâng cấp"

#: includes/admin/class-pro-notice.php:225
msgid "No, I don't want it"
msgstr "Không, tôi không muốn nó"

#: includes/admin/class-pro-notice.php:225
msgid "Yes, I want to learn more"
msgstr "Có, tôi muốn tìm hiểu thêm"

#: includes/admin/wizard/views/ready.php:49
msgid "Know more about the PRO version"
msgstr "Tìm hiểu thêm về phiên bản PRO"

#: rank-math.php:469
msgid "CAUTION:"
msgstr "THẬN TRỌNG:"

#: rank-math.php:470
msgid "This action is IRREVERSIBLE."
msgstr "Hành động này KHÔNG THỂ ĐẢO NGƯỢC."

#: includes/modules/analytics/views/email-reports/stat.php:68
msgid "Data Chart"
msgstr "Biểu đồ dữ liệu"

#: includes/modules/analytics/views/email-reports/header-after.php:23
msgid "FULL REPORT"
msgstr "BÁO CÁO ĐẦY ĐỦ"

#: includes/admin/wizard/views/search-console-ui.php:400
msgid "Receive Analytics reports periodically in email."
msgstr "Nhận báo cáo Analytics định kỳ qua email."

#. Translators: placeholder is "click here" as a link.
#: includes/modules/analytics/class-email-reports.php:133
msgid "To update your email preferences, %s."
msgstr "Để cập nhật tùy chọn email của bạn, %s."

#: includes/modules/analytics/views/options.php:142
msgid "Turn on email reports."
msgstr "Bật báo cáo qua email."

#. Translators: placeholder is the site URL.
#: includes/modules/analytics/class-email-reports.php:288
msgid "Rank Math [SEO Report] - %s"
msgstr "Rank Math [Báo cáo SEO] - %s"

#. Translators: don't translate the variable names between the #hashes#.
#: includes/modules/analytics/views/email-reports/header.php:43
msgid "Last ###PERIOD_DAYS### Days"
msgstr "###PERIOD_DAYS### ngày qua"

#: includes/modules/analytics/views/email-reports/cta.php:18
msgid "Rank Math PRO"
msgstr "Rank Math PRO"

#. Translators: Placeholders are the opening and closing tag for the link.
#: includes/modules/analytics/views/options.php:128
msgid "Receive periodic SEO Performance reports via email. Once enabled and options are saved, you can see %1$s the preview here%2$s."
msgstr "Nhận báo cáo hiệu suất SEO định kỳ qua email. Sau khi được bật và các tùy chọn được lưu, bạn có thể xem %1$s bản xem trước tại đây%2$s."

#: includes/admin/wizard/views/search-console-ui.php:400
msgid "Learn more about Email Reports."
msgstr "Tìm hiểu thêm về Báo cáo qua email."

#: includes/modules/analytics/views/options.php:157
msgid "Every 30 days"
msgstr "Cứ sau 30 ngày"

#: includes/modules/analytics/views/options.php:154
msgid "Email report frequency."
msgstr "Tần suất báo cáo email."

#: includes/modules/analytics/views/options.php:153
msgid "Email Frequency"
msgstr "Tần suất email"

#: includes/admin/wizard/views/search-console-ui.php:399
#: includes/admin/wizard/views/search-console-ui.php:404
#: includes/modules/analytics/views/options.php:126
#: includes/modules/analytics/views/options.php:141
msgid "Email Reports"
msgstr "Báo cáo qua email"

#: includes/modules/analytics/views/email-reports/header-after.php:24
msgid "External Link Icon"
msgstr "Biểu tượng liên kết ngoài"

#: includes/modules/analytics/views/email-reports/header-after.php:17
#: includes/modules/analytics/views/email-reports/header.php:16
#: includes/modules/analytics/views/email-reports/header.php:21
msgid "SEO Report of Your Website"
msgstr "Báo cáo SEO của trang web của bạn"

#. Translators: placeholder is "click here" as a link.
#: includes/admin/wizard/views/your-site.php:32
#: includes/modules/analytics/class-email-reports.php:133
msgid "click here"
msgstr "nhấp vào đây"

#. Translators: placeholder is "rankmath.com" as a link.
#: includes/modules/analytics/class-email-reports.php:129
msgid "This email was sent to you as a registered member of %s."
msgstr "Email này được gửi cho bạn với tư cách là thành viên đã đăng ký của %s."

#. Translators: placeholder is the post type name.
#: includes/modules/sitemap/class-sitemap.php:138
msgid "Rank Math has detected a new post type: %1$s. You may want to check the settings of the <a href=\"%2$s\">Titles &amp; Meta page</a> and <a href=\"%3$s\">the Sitemap</a>."
msgstr "Xếp hạng Math đã phát hiện một loại nội dung mới: %1$s. Bạn có thể muốn kiểm tra cài đặt của trang <a href=\"%2$s\">Tiêu đề &amp; Meta</a> và <a href=\"%3$s\">Sơ đồ trang web</a> ."

#: includes/admin/class-notices.php:254
msgid "<b>Rank Math Warning:</b> Changing the permalinks on a live, indexed site may result in serious loss of traffic if done incorrectly. Consider adding a new redirection from the old URL format to the new one."
msgstr "<b>Cảnh báo Rank Math:</b> Thay đổi liên kết cố định trên một trang web trực tiếp, đã được lập chỉ mục có thể dẫn đến mất lưu lượng truy cập nghiêm trọng nếu thực hiện không đúng cách. Hãy cân nhắc thêm chuyển hướng mới từ định dạng URL cũ sang định dạng mới."

#. Translators: Code to add support for Rank Math Breadcrumbs.
#: includes/settings/general/breadcrumbs.php:26
msgid "This option cannot be changed since your theme has added the support for Rank Math Breadcrumbs using: %s"
msgstr "Không thể thay đổi tùy chọn này vì giao diện của bạn đã thêm hỗ trợ cho điều hướng trang Rank Math bằng cách sử dụng: %s"

#: includes/admin/importers/abstract-importer.php:152
msgid "Import all the redirections you have already set up in Yoast Premium."
msgstr "Nhập tất cả các chuyển hướng bạn đã thiết lập trong Yoast Premium."

#. Translators: the placeholder is for the sitemap base url.
#: includes/modules/sitemap/class-admin.php:339
msgid "Since you are using an NGINX server, you may need to add the following code to your %s <strong>if your Sitemap pages are not loading</strong>. If you are unsure how to do it, please contact your hosting provider."
msgstr "Vì bạn đang sử dụng máy chủ NGINX, bạn có thể cần thêm mã sau vào %s <strong>nếu các trang Sơ đồ trang web của bạn không tải</strong>. Nếu bạn không chắc chắn cách thực hiện, vui lòng liên hệ với nhà cung cấp dịch vụ lưu trữ của bạn."

#: includes/modules/seo-analysis/class-seo-analyzer.php:435
msgid "Rank Math SEO Analyzer error: "
msgstr "Lỗi Trình phân tích SEO Rank Math: "

#. Translators: placeholder is the post type names separated with commas.
#: includes/admin/class-notices.php:113
msgid "Rank Math has detected new post types: %1$s. You may want to check the settings of the <a href=\"%2$s\">Titles &amp; Meta page</a>."
msgstr "Rank Math đã phát hiện các loại nội dung mới: %1$s. Bạn có thể muốn kiểm tra cài đặt của trang <a href=\"%2$s\">Tiêu đề &amp; Meta</a> ."

#. Translators: placeholder is the post type name.
#: includes/admin/class-notices.php:109
msgid "Rank Math has detected a new post type: %1$s. You may want to check the settings of the <a href=\"%2$s\">Titles &amp; Meta page</a>."
msgstr "Rank Math đã phát hiện một loại nội dung mới: %1$s. Bạn có thể muốn kiểm tra cài đặt của trang <a href=\"%2$s\">Tiêu đề &amp; Meta</a> ."

#: includes/admin/views/dashboard-help.php:115
msgid "Affiliate Program"
msgstr "Chương trình liên kết"

#: includes/modules/analytics/class-analytics.php:307
msgid "Are you sure you want to disconnect Google services from your site?"
msgstr "Bạn có chắc chắn muốn ngắt kết nối các dịch vụ của Google khỏi trang web của mình không?"

#: includes/modules/analytics/class-analytics.php:304
msgid "Are you sure you want to do this?"
msgstr "Bạn có chắc chắn muốn làm điều này?"

#: includes/modules/database-tools/class-database-tools.php:163
msgid "No 404 log data found."
msgstr "Không tìm thấy dữ liệu nhật ký 404."

#: includes/modules/database-tools/class-database-tools.php:182
msgid "No Redirections found."
msgstr "Không tìm thấy Chuyển hướng."

#: includes/modules/analytics/class-analytics.php:305
msgid "You are about to delete all the previously imported data."
msgstr "Bạn sắp xóa tất cả dữ liệu đã nhập trước đó."

#: includes/modules/analytics/class-analytics.php:306
msgid "You are about to delete your 90 days cache."
msgstr "Bạn sắp xóa bộ nhớ cache 90 ngày của mình."

#: includes/admin/wizard/views/ready.php:51
msgid "Subscribe to Our YouTube Channel"
msgstr "Đăng ký Kênh YouTube của chúng tôi"

#: includes/admin/views/dashboard-help.php:116
msgid "Earn flat 30% on every sale!"
msgstr "Kiếm 30% hoa hồng cho mỗi lần bán!"

#: includes/admin/views/dashboard-help.php:42
msgid "Advanced Schema, Analytics and much more..."
msgstr "Schema nâng cao, Phân tích và nhiều hơn nữa..."

#: includes/admin/views/dashboard-help.php:41
#: includes/modules/analytics/assets/js/admin-bar.js:1
msgid "Upgrade to PRO"
msgstr "Nâng cấp lên PRO"

#: includes/modules/database-tools/class-database-tools.php:143
msgid "No Internal Links data found."
msgstr "Không tìm thấy dữ liệu Liên kết nội bộ."

#: includes/modules/database-tools/class-database-tools.php:123
msgid "SEO Analyzer data has already been cleared."
msgstr "Dữ liệu Trình phân tích SEO đã được xóa."

#: includes/modules/database-tools/class-database-tools.php:101
msgid "No Rank Math transients found."
msgstr "Không tìm thấy transient Rank Math."

#: includes/modules/analytics/class-analytics.php:308
msgid "Cache deleted."
msgstr "Bộ nhớ cache đã bị xóa."

#. translators: 1. separator, 2. blogname
#: includes/modules/sitemap/class-stylesheet.php:56
msgid "Locations Sitemap %1$s %2$s"
msgstr "Sơ đồ trang web địa điểm %1$s %2$s"

#: includes/replace-variables/class-advanced-variables.php:56
msgid "Focus Keywords of the current post"
msgstr "Từ khoá chính của bài viết hiện tại"

#: includes/modules/status/class-system-status.php:145
msgid "(none)"
msgstr "(không có)"

#: includes/admin/wizard/views/search-console-ui.php:299
msgid "Self-Hosted Analytics JS File"
msgstr "Tệp JS Phân tích tự lưu trữ"

#: includes/modules/redirections/views/options.php:28
msgid "If nothing similar is found, this behavior will be applied. <strong>Note</strong>: If the requested URL ends with <code>/login</code>, <code>/admin</code>, or <code>/dashboard</code>, WordPress will automatically redirect to respective locations within the WordPress admin area."
msgstr "Nếu không tìm thấy gì tương tự, hành vi này sẽ được áp dụng. <strong>Lưu ý</strong>: Nếu URL được yêu cầu kết thúc bằng <code>/login</code>, <code>/admin</code> hoặc <code>/dashboard</code>, WordPress sẽ tự động chuyển hướng đến các vị trí tương ứng trong khu vực quản trị WordPress."

#: includes/modules/status/class-system-status.php:144
msgid "Active modules"
msgstr "Module hoạt động"

#: includes/class-update-email.php:70
msgid "https://support.rankmath.com/"
msgstr "https://support.rankmath.com/"

#: includes/class-update-email.php:143
msgid "Rank Math Free"
msgstr "Rank Math miễn phí"

#: includes/class-update-email.php:72
msgid "Rank Math Team"
msgstr "Nhóm Rank Math"

#. Translators: placeholders are the old and new version numbers.
#: includes/class-update-email.php:164
msgid "%1$s: Old %2$s -> New %3$s | Changelog: %4$s"
msgstr "%1$s: Cũ %2$s -> Mới %3$s | Nhật ký thay đổi: %4$s"

#: includes/class-update-email.php:69
msgid "If you have any questions or experience any issues – our support team is at your disposal:"
msgstr "Nếu bạn có bất kỳ câu hỏi nào hoặc gặp bất kỳ sự cố nào - nhóm hỗ trợ của chúng tôi luôn sẵn sàng hỗ trợ bạn:"

#: includes/settings/general/others.php:29
msgid "Show SEO Score to Visitors"
msgstr "Hiển thị điểm SEO cho khách truy cập"

#: includes/settings/general/others.php:30
msgid "Proudly display the calculated SEO Score as a badge on the front end. It can be disabled for specific posts in the post editor."
msgstr "Hiển thị tự hào Điểm SEO đã tính toán dưới dạng huy hiệu ở giao diện người dùng. Nó có thể bị vô hiệu hóa đối với các bài viết cụ thể trong trình chỉnh sửa bài viết."

#. translators: post type name
#: includes/settings/titles/post-types.php:304
msgid "Choose which taxonomy you want to use with the Primary Term feature. This will also be the taxonomy shown in the Breadcrumbs when a single %1$s is being viewed."
msgstr "Chọn phân loại bạn muốn sử dụng với tính năng thuật ngữ chính. Đây cũng sẽ là phân loại được hiển thị trong điều hướng trang khi một %1$s duy nhất đang được xem."

#: includes/class-update-email.php:56
msgid "Hello,"
msgstr "Xin chào,"

#. Translators: placeholder is the new admin page URL.
#: includes/class-update-email.php:66
msgid "To ensure your site is always on the latest, most up-to-date version of Rank Math - we recommend logging into the admin area to update the plugin as soon as possible: %s"
msgstr "Để đảm bảo trang web của bạn luôn ở phiên bản mới nhất, cập nhật nhất của Rank Math - chúng tôi khuyên bạn nên đăng nhập vào khu vực quản trị để cập nhật plugin càng sớm càng tốt: %s"

#. Translators: placeholder is the site URL.
#: includes/class-update-email.php:59
msgid "This is an automated email to let you know that there is an update available for the Rank Math SEO plugin installed on: %s"
msgstr "Đây là email tự động để cho bạn biết rằng có bản cập nhật dành cho plugin Rank Math SEO được cài đặt trên: %s"

#. Translators: placeholder is the site title.
#: includes/class-update-email.php:52
msgid "[%s] An update is available for Rank Math"
msgstr "[%s] Có bản cập nhật dành cho Rank Math"

#. Translators: placeholder is the plugin name.
#: includes/admin/class-ask-review.php:171
msgctxt "plugin name inside the review notice"
msgid "Rank Math SEO"
msgstr "Rank Math SEO"

#: includes/admin/class-ask-review.php:179
msgid "I already did"
msgstr "Tôi đã làm rồi"

#: includes/admin/class-ask-review.php:178
#: includes/admin/class-pro-notice.php:244
msgid "No, maybe later"
msgstr "Không, có thể sau"

#: includes/admin/class-ask-review.php:177
msgid "Yes, you deserve it"
msgstr "Vâng, bạn xứng đáng với nó"

#: includes/admin/class-ask-review.php:175
msgid "Co-founder of Rank Math"
msgstr "Đồng sáng lập của Rank Math"

#: includes/admin/class-ask-review.php:174
msgid "Could you please do us a BIG favor and give it a rating on WordPress.org to help us spread the word and boost our motivation?"
msgstr "Bạn có thể giúp chúng tôi một việc LỚN và đánh giá nó trên WordPress.org để giúp chúng tôi lan truyền và tăng động lực của chúng tôi không?"

#. Translators: placeholder is the plugin name.
#: includes/admin/class-ask-review.php:171
msgid "Hey, we noticed you've been using %s for more than a week now – that's awesome!"
msgstr "Này, chúng tôi nhận thấy bạn đã sử dụng %s được hơn một tuần rồi - thật tuyệt vời!"

#: includes/modules/version-control/views/auto-update-panel.php:53
msgid "Update Notification Email"
msgstr "Email thông báo cập nhật"

#: includes/modules/version-control/views/auto-update-panel.php:48
msgid "When auto-updates are turned off, you can enable update notifications, to send an email to the site administrator when an update is available for Rank Math."
msgstr "Khi cập nhật tự động bị tắt, bạn có thể bật thông báo cập nhật, để gửi email cho quản trị viên trang web khi có bản cập nhật cho Rank Math."

#: includes/admin/class-dashboard-widget.php:120
msgid "NEW"
msgstr "MỚI"

#: includes/modules/analytics/google/class-permissions.php:128
msgid "Warning:"
msgstr "Cảnh báo:"

#: includes/modules/status/class-system-status.php:152
msgid "Google Permission"
msgstr "Quyền của Google"

#: includes/module/class-manager.php:167
#: includes/modules/instant-indexing/class-instant-indexing.php:249
#: includes/modules/instant-indexing/class-instant-indexing.php:250
msgid "Instant Indexing"
msgstr "Lập chỉ mục tức thì"

#: includes/modules/instant-indexing/class-instant-indexing.php:213
#: includes/modules/instant-indexing/views/console.php:28
msgid "Submit URLs"
msgstr "Gửi URL"

#: includes/modules/database-tools/class-database-tools.php:213
msgid "Table re-creation started. It might take a couple of minutes."
msgstr "Việc tạo lại bảng đã bắt đầu. Có thể mất vài phút."

#. translators: %s is the reconnect link.
#: includes/modules/analytics/google/class-permissions.php:132
#: assets/admin/js/components.js:1
msgid "You have not given the permission to fetch this data. Please <a href=\"%s\">reconnect</a> with all required permissions."
msgstr "Bạn chưa cấp quyền để lấy dữ liệu này. Vui lòng <a href=\"%s\">kết nối lại</a> với tất cả các quyền cần thiết."

#: includes/modules/analytics/google/class-permissions.php:118
msgid "Not Given"
msgstr "Chưa được cấp"

#: includes/modules/analytics/google/class-permissions.php:118
msgid "Given"
msgstr "Đã được cấp"

#: includes/modules/analytics/class-analytics.php:202
msgid "Cancel Fetch"
msgstr "Hủy lấy dữ liệu"

#: includes/modules/analytics/class-ajax.php:363
msgid "Data fetching started in the background."
msgstr "Việc lấy dữ liệu đã bắt đầu trong nền."

#: includes/modules/analytics/class-ajax.php:329
msgid "Data fetching cancelled."
msgstr "Việc lấy dữ liệu đã bị hủy bỏ."

#: includes/modules/analytics/views/options.php:81
msgid "Cancel Fetching"
msgstr "Hủy lấy dữ liệu"

#. translators: Link to kb article
#: includes/modules/instant-indexing/class-instant-indexing.php:222
msgid "Instant Indexing module settings. %s."
msgstr "Cài đặt module Lập chỉ mục tức thì. %s."

#: includes/replace-variables/class-basic-variables.php:240
msgid "Organization URL added in Local SEO Settings."
msgstr "URL tổ chức được thêm trong Cài đặt SEO địa phương."

#. translators: Auth URL
#: includes/modules/analytics/class-analytics.php:140
msgid "It seems like the connection with your Google account & Rank Math needs to be made again. <a href=\"%s\" class=\"rank-math-reconnect-google\">Please click here.</a>"
msgstr "Có vẻ như kết nối với tài khoản Google của bạn & Rank Math cần được thực hiện lại. <a href=\"%s\" class=\"rank-math-reconnect-google\">Vui lòng nhấp vào đây.</a>"

#: includes/modules/database-tools/class-database-tools.php:354
msgid "Check if required tables exist and create them if not."
msgstr "Kiểm tra xem các bảng bắt buộc có tồn tại hay không và tạo chúng nếu không."

#: includes/modules/database-tools/class-database-tools.php:353
msgid "Re-create Missing Database Tables"
msgstr "Tạo lại Bảng cơ sở dữ liệu bị thiếu"

#: includes/admin/importers/class-aioseo.php:73
msgid "Import meta information of your terms like the titles, descriptions, robots meta, OpenGraph info, etc."
msgstr "Nhập siêu dữ liệu của các thuật ngữ của bạn như tiêu đề, mô tả, siêu dữ liệu rô bốt, thông tin OpenGraph, v.v."

#. translators: reconnect link
#: includes/modules/analytics/google/class-request.php:182
msgid "There is a problem with the Google auth token. Please <a href=\"%1$s\" class=\"button button-link rank-math-reconnect-google\">reconnect your app</a>"
msgstr "Có sự cố với mã thông báo xác thực Google. Vui lòng <a href=\"%1$s\" class=\"button button-link rank-math-reconnect-google\">kết nối lại ứng dụng của bạn</a>"

#: includes/module/class-manager.php:412
#: includes/modules/status/class-system-status.php:141
#: includes/modules/content-ai/assets/js/content-ai.js:1
msgid "Free"
msgstr "Miễn phí"

#: includes/modules/status/class-system-status.php:132
msgid "Version"
msgstr "Phiên bản"

#: includes/modules/status/class-system-status.php:178
msgid "Database Table: 404 Log"
msgstr "Bảng cơ sở dữ liệu: Nhật ký 404"

#: includes/modules/status/class-system-status.php:186
msgid "Database Table: Google AdSense"
msgstr "Bảng cơ sở dữ liệu: Google AdSense"

#: includes/modules/status/class-system-status.php:185
msgid "Database Table: Google Analytics"
msgstr "Bảng cơ sở dữ liệu: Google Analytics"

#: includes/modules/status/class-system-status.php:183
msgid "Database Table: Google Search Console"
msgstr "Bảng cơ sở dữ liệu: Google Search Console"

#: includes/modules/status/class-system-status.php:181
msgid "Database Table: Internal Link"
msgstr "Bảng cơ sở dữ liệu: Liên kết nội bộ"

#: includes/modules/status/class-system-status.php:187
msgid "Database Table: Keyword Manager"
msgstr "Bảng cơ sở dữ liệu: Trình quản lý từ khóa"

#: includes/modules/status/class-system-status.php:179
msgid "Database Table: Redirection"
msgstr "Bảng cơ sở dữ liệu: Chuyển hướng"

#: includes/modules/status/class-system-status.php:136
msgid "Database version"
msgstr "Phiên bản cơ sở dữ liệu"

#: includes/modules/status/class-system-status.php:140
msgid "Plugin subscription plan"
msgstr "Gói đăng ký plugin"

#: includes/modules/database-tools/class-database-tools.php:355
msgid "Re-create Tables"
msgstr "Tạo lại bảng"

#: includes/modules/status/class-system-status.php:149
msgid "Token exists"
msgstr "Mã thông báo tồn tại"

#: includes/modules/status/class-system-status.php:149
msgid "No token"
msgstr "Không có mã thông báo"

#: includes/modules/status/class-system-status.php:148
msgid "Google Refresh token"
msgstr "Mã thông báo làm mới của Google"

#: includes/modules/status/class-system-status.php:182
msgid "Database Table: Internal Link Meta"
msgstr "Bảng cơ sở dữ liệu: Siêu dữ liệu liên kết nội bộ"

#: includes/modules/status/class-system-status.php:180
msgid "Database Table: Redirection Cache"
msgstr "Bảng cơ sở dữ liệu: Bộ nhớ cache chuyển hướng"

#: includes/modules/status/class-system-status.php:184
msgid "Database Table: Flat Posts"
msgstr "Bảng cơ sở dữ liệu: Bài viết phẳng"

#: includes/modules/schema/class-snippet-shortcode.php:260
msgid "Days"
msgstr "Ngày"

#. Translators: placeholder is a link to the Pro version
#: includes/modules/local-seo/views/titles-options.php:334
msgid "Multiple Locations are available in the %s."
msgstr "Nhiều vị trí có sẵn trong %s."

#. translators: Option Description
#: includes/admin/wizard/class-compatibility.php:52
msgid "Select this if you have a custom Rank Math settings file you want to use."
msgstr "Chọn điều này nếu bạn có tệp cài đặt Rank Math tùy chỉnh mà bạn muốn sử dụng."

#: includes/admin/importers/class-status.php:108
msgid "Video Settings imported successfully."
msgstr "Cài đặt video đã được nhập thành công."

#: includes/admin/class-dashboard-widget.php:174
#: includes/modules/content-ai/assets/js/content-ai-page.js:1
#: includes/modules/content-ai/assets/js/content-ai.js:1
msgid "Blog"
msgstr "Blog"

#: includes/admin/importers/abstract-importer.php:155
msgid "Import News Settings from Yoast News Add-on."
msgstr "Nhập Cài đặt Tin tức từ Yoast News Add-on."

#: includes/admin/class-dashboard-widget.php:185
msgid "Go Pro"
msgstr "Chuyển sang Pro"

#: includes/admin/class-dashboard-widget.php:175
#: includes/admin/class-dashboard-widget.php:180
#: includes/admin/class-dashboard-widget.php:186
msgid "(opens in a new window)"
msgstr "(mở trong cửa sổ mới)"

#: includes/admin/class-dashboard-widget.php:54
msgid "Rank Math Overview"
msgstr "Tổng quan về Rank Math"

#: includes/frontend/class-head.php:414
msgid "Search Engine Optimization by Rank Math PRO - https://rankmath.com/"
msgstr "Tối ưu hóa công cụ tìm kiếm bởi Rank Math PRO - https://rankmath.com/"

#: includes/modules/redirections/class-redirections.php:81
msgid "Total number of hits received by all the Redirections."
msgstr "Tổng số lượt truy cập nhận được bởi tất cả các Chuyển hướng."

#: includes/modules/redirections/class-redirections.php:74
msgid "Total number of Redirections created in the Rank Math."
msgstr "Tổng số Chuyển hướng được tạo trong Rank Math."

#: includes/modules/404-monitor/class-monitor.php:80
msgid "Total number visits received on all the 404 pages."
msgstr "Tổng số lượt truy cập nhận được trên tất cả các trang 404."

#: includes/modules/404-monitor/class-monitor.php:79
msgid "URL Hits"
msgstr "Lượt truy cập URL"

#: includes/modules/404-monitor/class-monitor.php:73
msgid "Total number of 404 pages opened by the users."
msgstr "Tổng số trang 404 được người dùng mở."

#: includes/modules/404-monitor/class-monitor.php:72
msgid "Log Count"
msgstr "Số lượng nhật ký"

#: includes/modules/analytics/class-analytics-common.php:319
#: includes/modules/analytics/assets/js/stats.js:1
msgid "Total number of keywords your site ranking below 100 position."
msgstr "Tổng số từ khóa trang web của bạn xếp hạng dưới vị trí 100."

#: includes/modules/404-monitor/class-monitor.php:67
#: includes/modules/analytics/class-analytics-common.php:74
#: includes/modules/redirections/class-redirections.php:68
msgid "View Report"
msgstr "Xem báo cáo"

#: includes/modules/analytics/class-analytics-common.php:73
msgid "Last 30 Days"
msgstr "30 ngày qua"

#: includes/admin/class-dashboard-widget.php:76
msgid "Latest Blog Posts from Rank Math"
msgstr "Bài viết trên blog mới nhất từ Rank Math"

#: includes/admin/importers/abstract-importer.php:156
msgid "Import Video Sitemap Settings from Yoast Video Add-on."
msgstr "Nhập cài đặt sơ đồ trang web Video từ Yoast Video Add-on."

#: includes/admin/importers/abstract-importer.php:156
msgid "Import Video Sitemap Settings"
msgstr "Nhập cài đặt sơ đồ trang web Video"

#: includes/admin/wizard/views/search-console-ui.php:378
msgid "Google AdSense support is only available in Rank Math Pro's Advanced Analytics module."
msgstr "Hỗ trợ Google AdSense chỉ khả dụng trong module Phân tích nâng cao của Rank Math Pro."

#: includes/admin/wizard/views/search-console-ui.php:81
msgid "Reconnect"
msgstr "Kết nối lại"

#: includes/admin/wizard/views/search-console-ui.php:260
msgid "Enable this option only if you are not using any other plugin/theme to install Google Analytics code."
msgstr "Chỉ bật tùy chọn này nếu bạn không sử dụng bất kỳ plugin / giao diện nào khác để cài đặt mã Google Analytics."

#: includes/modules/analytics/class-posts.php:40
msgid "Sorry, no post found for given id."
msgstr "Xin lỗi, không tìm thấy bài viết cho id đã cho."

#: includes/modules/analytics/views/email-reports/sections/positions.php:44
msgid "11-50 Positions"
msgstr "Vị trí 11-50"

#: includes/modules/analytics/views/email-reports/sections/positions.php:32
#: includes/modules/analytics/assets/js/stats.js:1
msgid "4-10 Positions"
msgstr "Vị trí 4-10"

#: includes/modules/analytics/views/email-reports/sections/positions.php:20
#: includes/modules/analytics/assets/js/stats.js:1
msgid "Top 3 Positions"
msgstr "3 Vị trí hàng đầu"

#: includes/modules/analytics/class-analytics-common.php:300
msgid "Search Traffic"
msgstr "Lưu lượng tìm kiếm"

#: includes/admin/metabox/class-screen.php:170
#: includes/modules/seo-analysis/views/competitor-analysis.php:217
#: includes/modules/analytics/assets/js/admin-bar.js:1
#: includes/modules/analytics/assets/js/stats.js:1
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Upgrade"
msgstr "Nâng cấp"

#: includes/opengraph/class-slack.php:361
#: includes/modules/analytics/assets/js/stats.js:1
msgid "Posts"
msgstr "Bài viết"

#: includes/modules/analytics/class-analytics-common.php:307
#: includes/modules/analytics/assets/js/stats.js:1
msgid "How many times your site showed up in the search results."
msgstr "Số lần trang web của bạn hiển thị trong kết quả tìm kiếm."

#: includes/modules/analytics/class-analytics-common.php:313
msgid "This is the number of pageviews carried out by visitors from Google."
msgstr "Đây là số lượt xem trang được thực hiện bởi khách truy cập từ Google."

#: includes/admin/wizard/views/search-console-ui.php:86
msgid "Disconnect"
msgstr "Ngắt kết nối"

#: includes/admin/wizard/views/search-console-ui.php:278
#: includes/admin/wizard/views/search-console-ui.php:303
#: includes/admin/wizard/views/search-console-ui.php:378
#: includes/module/class-manager.php:420
#: includes/modules/analytics/views/options.php:149
#: assets/admin/js/components.js:1
msgid "PRO"
msgstr "PRO"

#: includes/admin/wizard/views/search-console-ui.php:259
msgid "Install analytics code"
msgstr "Cài đặt mã phân tích"

#: includes/admin/wizard/views/search-console-ui.php:274
msgid "Anonymize IP addresses"
msgstr "Ẩn danh địa chỉ IP"

#: includes/admin/wizard/views/search-console-ui.php:323
msgid "Exclude Logged-in users"
msgstr "Loại trừ người dùng đã đăng nhập"

#: includes/admin/wizard/views/google-connect.php:23
msgid "Benefits of Connecting Google Account"
msgstr "Lợi ích của việc kết nối tài khoản Google"

#: includes/admin/importers/abstract-importer.php:155
msgid "Import News Settings"
msgstr "Nhập cài đặt tin tức"

#: includes/admin/wizard/views/search-console-ui.php:352
msgid "AdSense"
msgstr "AdSense"

#: includes/modules/redirections/class-table.php:279
msgid "Created"
msgstr "Đã tạo"

#: includes/admin/wizard/views/search-console-ui.php:231
#: includes/modules/404-monitor/class-table.php:108
#: includes/modules/404-monitor/class-table.php:147
#: includes/modules/redirections/class-table.php:255
#: assets/admin/js/common.js:1
msgid "View"
msgstr "Xem"

#: includes/admin/wizard/views/search-console-ui.php:219
msgid "Property"
msgstr "Thuộc tính"

#: includes/admin/wizard/views/search-console-ui.php:139
msgid "Site"
msgstr "Trang web"

#. Translators: placeholder is the KB link.
#: includes/admin/wizard/views/google-connect.php:37
#: includes/admin/wizard/views/rank-math-connect.php:48
#: includes/admin/wizard/views/search-console-ui.php:389
#: assets/admin/js/components.js:1
msgid "We do not store any of the data from your Google account on our servers, everything is processed & stored on your server. We take your privacy extremely seriously and ensure it is never misused. %s"
msgstr "Chúng tôi không lưu trữ bất kỳ dữ liệu nào từ tài khoản Google của bạn trên máy chủ của chúng tôi, mọi thứ đều được xử lý & lưu trữ trên máy chủ của bạn. Chúng tôi rất coi trọng quyền riêng tư của bạn và đảm bảo rằng nó không bao giờ bị sử dụng sai mục đích. %s"

#: includes/admin/wizard/views/google-connect.php:16
msgid "Connect Google Services"
msgstr "Kết nối dịch vụ Google"

#: includes/admin/importers/abstract-importer.php:154
msgid "Import Locations Settings from Yoast plugin."
msgstr "Nhập cài đặt vị trí từ plugin Yoast."

#: includes/admin/importers/class-status.php:107
msgid "News Settings imported successfully."
msgstr "Cài đặt tin tức đã được nhập thành công."

#: includes/helpers/class-choices.php:665
msgid "Argentina"
msgstr "Argentina"

#: includes/helpers/class-choices.php:667
msgid "Australia"
msgstr "Úc"

#: includes/helpers/class-choices.php:668
msgid "Austria"
msgstr "Áo"

#: includes/helpers/class-choices.php:675
msgid "Brazil"
msgstr "Brazil"

#: includes/helpers/class-choices.php:678
msgid "Canada"
msgstr "Canada"

#: includes/helpers/class-choices.php:679
msgid "Chile"
msgstr "Chile"

#: includes/helpers/class-choices.php:680
msgid "Colombia"
msgstr "Colombia"

#: includes/helpers/class-choices.php:696
msgid "Hong Kong"
msgstr "Hồng Kông"

#: includes/helpers/class-choices.php:697
msgid "Hungary"
msgstr "Hungary"

#: includes/helpers/class-choices.php:699
msgid "Indonesia"
msgstr "Indonesia"

#: includes/helpers/class-choices.php:700
msgid "Ireland"
msgstr "Ireland"

#: includes/helpers/class-choices.php:706
msgid "Kenya"
msgstr "Kenya"

#: includes/helpers/class-choices.php:711
msgid "Malaysia"
msgstr "Malaysia"

#: includes/helpers/class-choices.php:713
msgid "Mexico"
msgstr "Mexico"

#: includes/helpers/class-choices.php:717
msgid "New Zealand"
msgstr "New Zealand"

#: includes/helpers/class-choices.php:719
msgid "Nigeria"
msgstr "Nigeria"

#: includes/helpers/class-choices.php:727
msgid "Romania"
msgstr "Romania"

#: includes/helpers/class-choices.php:732
msgid "Singapore"
msgstr "Singapore"

#: includes/helpers/class-choices.php:735
msgid "South Africa"
msgstr "Nam Phi"

#: includes/modules/analytics/class-analytics.php:530
msgid "Rebuild Index"
msgstr "Xây dựng lại chỉ mục"

#. Translators: placeholder is a link to rankmath.com, with "free version" as
#. the anchor text.
#: includes/modules/analytics/views/options.php:86
msgid "free version"
msgstr "phiên bản miễn phí"

#: includes/admin/wizard/views/google-connect.php:25
#: includes/admin/wizard/views/rank-math-connect.php:31
msgid "Verify site ownership on Google Search Console in a single click"
msgstr "Xác minh quyền sở hữu trang web trên Google Search Console chỉ bằng một cú nhấp chuột"

#: includes/admin/wizard/views/google-connect.php:29
#: includes/admin/wizard/views/rank-math-connect.php:37
msgid "Learn more about the benefits of connecting your account here."
msgstr "Tìm hiểu thêm về lợi ích của việc kết nối tài khoản của bạn tại đây."

#: includes/modules/analytics/rest/class-rest.php:120
msgid "Sorry, no preference found."
msgstr "Xin lỗi, không tìm thấy tùy chọn."

#: includes/admin/wizard/class-search-console.php:38
msgid "Connect Google&trade; Services"
msgstr "Kết nối các dịch vụ của Google ™"

#: includes/modules/analytics/views/options.php:79
msgid "Delete data"
msgstr "Xóa dữ liệu"

#: includes/helpers/class-choices.php:690
msgid "Finland"
msgstr "Phần Lan"

#: includes/helpers/class-choices.php:694
msgid "Greece"
msgstr "Hy Lạp"

#: includes/helpers/class-choices.php:698
msgid "India"
msgstr "Ấn Độ"

#: includes/helpers/class-choices.php:703
msgid "Japan"
msgstr "Nhật Bản"

#: includes/helpers/class-choices.php:720
msgid "Norway"
msgstr "Na Uy"

#: includes/helpers/class-choices.php:726
msgid "Portugal"
msgstr "Bồ Đào Nha"

#: includes/helpers/class-choices.php:729
msgid "Saudi Arabia"
msgstr "Ả Rập Saudi"

#: includes/helpers/class-choices.php:738
msgid "Sweden"
msgstr "Thụy Điển"

#: includes/helpers/class-choices.php:744
msgid "Ukraine"
msgstr "Ukraine"

#: includes/helpers/class-choices.php:746
msgid "United Kingdom"
msgstr "Vương quốc Anh"

#: includes/modules/content-ai/views/options.php:136
#: includes/modules/content-ai/views/options.php:184
msgid "Russian"
msgstr "Tiếng Nga"

#: includes/modules/analytics/class-analytics.php:528
msgid "Rebuild Index for Analytics"
msgstr "Xây dựng lại Chỉ mục cho Phân tích"

#: includes/admin/wizard/views/rank-math-connect.php:36
msgid "Use our revolutionary SEO Analyzer to scan your website for SEO errors"
msgstr "Sử dụng Trình phân tích SEO mang tính cách mạng của chúng tôi để quét trang web của bạn để tìm lỗi SEO"

#: includes/admin/wizard/views/rank-math-connect.php:35
msgid "Free keyword suggestions when entering a focus keyword"
msgstr "Gợi ý từ khóa miễn phí khi nhập từ khóa trọng tâm"

#: includes/admin/wizard/views/rank-math-connect.php:29
#: includes/modules/content-ai/views/options.php:28
msgid "Benefits of Connecting Rank Math Account"
msgstr "Lợi ích của việc kết nối tài khoản Rank Math"

#: includes/admin/wizard/views/rank-math-connect.php:25
#: includes/modules/content-ai/views/options.php:23
msgid "Connect Your Rank Math Account"
msgstr "Kết nối tài khoản Rank Math của bạn"

#: includes/admin/wizard/views/google-connect.php:28
#: includes/admin/wizard/views/rank-math-connect.php:34
msgid "Automatically submit sitemaps to the Google Search Console"
msgstr "Tự động gửi sơ đồ trang web tới Google Search Console"

#: includes/admin/wizard/views/google-connect.php:27
#: includes/admin/wizard/views/rank-math-connect.php:33
msgid "Easily set up Google Analytics without using another 3rd party plugin"
msgstr "Dễ dàng thiết lập Google Analytics mà không cần sử dụng plugin bên thứ 3 khác"

#. translators: Link to How to Setup Google Search Console KB article
#: includes/admin/wizard/class-search-console.php:42
msgid "Rank Math automates everything, use below button to connect your site with Google Search Console and Google Analytics. It will verify your site and submit sitemaps automatically. %s"
msgstr "Rank Math tự động hóa mọi thứ, hãy sử dụng nút bên dưới để kết nối trang web của bạn với Google Search Console và Google Analytics. Nó sẽ xác minh trang web của bạn và gửi sơ đồ trang web tự động. %s"

#: includes/modules/analytics/class-analytics.php:525
msgid "Clear Cache"
msgstr "Xóa bộ nhớ cache"

#: includes/modules/analytics/class-analytics.php:524
msgid "Clear analytics cache to re-calculate all the stats again."
msgstr "Xóa bộ nhớ cache phân tích để tính toán lại tất cả các số liệu thống kê."

#: includes/modules/analytics/class-analytics.php:523
msgid "Purge Analytics Cache"
msgstr "Xóa bộ nhớ cache phân tích"

#: includes/modules/analytics/class-analytics.php:270
msgid "Now!"
msgstr "Ngay bây giờ!"

#. translators: %s: amount of time
#: includes/modules/analytics/class-analytics.php:265
msgid "%s second"
msgid_plural "%s seconds"
msgstr[0] "%s giây"

#. translators: %s: amount of time
#: includes/modules/analytics/class-analytics.php:260
msgid "%s minute"
msgid_plural "%s minutes"
msgstr[0] "%s phút"

#. translators: %s: amount of time
#: includes/modules/analytics/class-analytics.php:255
msgid "%s hour"
msgid_plural "%s hours"
msgstr[0] "%s giờ"

#. translators: %s: amount of time
#: includes/modules/analytics/class-analytics.php:250
msgid "%s day"
msgid_plural "%s days"
msgstr[0] "%s ngày"

#. translators: %s: amount of time
#: includes/modules/analytics/class-analytics.php:245
msgid "%s week"
msgid_plural "%s weeks"
msgstr[0] "%s tuần"

#. translators: %s: amount of time
#: includes/modules/analytics/class-analytics.php:240
msgid "%s month"
msgid_plural "%s months"
msgstr[0] "%s tháng"

#. translators: %s: amount of time
#: includes/modules/analytics/class-analytics.php:235
msgid "%s year"
msgid_plural "%s years"
msgstr[0] "%s năm"

#: includes/modules/analytics/class-analytics.php:201
msgid "Rank Math is importing latest data from connected Google Services, %1$s remaining."
msgstr "Rank Math đang nhập dữ liệu mới nhất từ ​​Dịch vụ Google được kết nối, còn lại %1$s."

#: includes/modules/analytics/class-analytics-common.php:179
msgid "Post re-index in progress."
msgstr "Đang tiến hành lập chỉ mục lại bài viết."

#: includes/modules/analytics/class-analytics-common.php:158
msgid "Analytics cache cleared."
msgstr "Bộ nhớ cache phân tích đã được xóa."

#: includes/modules/analytics/rest/class-rest.php:145
msgid "Sorry, no post id found."
msgstr "Xin lỗi, không tìm thấy ID bài viết."

#. translators: number of days
#: includes/modules/analytics/views/options.php:30
msgid "Storage Days: %s"
msgstr "Số ngày lưu trữ: %s"

#. Translators: placeholder is a link to rankmath.com, with "free version" as
#. the anchor text.
#: includes/modules/analytics/views/options.php:86
msgid "Enter the number of days to keep Analytics data in your database. The maximum allowed days are 90 in the %s. Though, 2x data will be stored in the DB for calculating the difference properly."
msgstr "Nhập số ngày để giữ dữ liệu Phân tích trong cơ sở dữ liệu của bạn. Số ngày tối đa cho phép là 90 trong %s. Tuy nhiên, dữ liệu 2x sẽ được lưu trữ trong DB để tính toán chênh lệch chính xác."

#: includes/modules/analytics/views/options.php:94
msgid "Analytics Database"
msgstr "Cơ sở dữ liệu phân tích"

#: includes/modules/analytics/views/options.php:56
msgid "Next data fetch on %s"
msgstr "Lấy dữ liệu tiếp theo vào %s"

#: includes/helpers/class-choices.php:743
msgid "Turkey"
msgstr "Thổ Nhĩ Kỳ"

#: includes/helpers/class-choices.php:741
msgid "Thailand"
msgstr "Thái Lan"

#: includes/helpers/class-choices.php:740
msgid "Taiwan"
msgstr "Đài Loan"

#: includes/helpers/class-choices.php:739
msgid "Switzerland"
msgstr "Thụy Sĩ"

#: includes/helpers/class-choices.php:725
msgid "Poland"
msgstr "Ba Lan"

#: includes/helpers/class-choices.php:724
msgid "Philippines"
msgstr "Philippines"

#: includes/helpers/class-choices.php:716
msgid "Netherlands"
msgstr "Hà Lan"

#: includes/helpers/class-choices.php:702
msgid "Italy"
msgstr "Ý"

#: includes/helpers/class-choices.php:701
msgid "Israel"
msgstr "Israel"

#: includes/helpers/class-choices.php:692
msgid "Germany"
msgstr "Đức"

#: includes/helpers/class-choices.php:691
msgid "France"
msgstr "Pháp"

#: includes/helpers/class-choices.php:687
msgid "Egypt"
msgstr "Ai Cập"

#: includes/helpers/class-choices.php:685
msgid "Denmark"
msgstr "Đan Mạch"

#: includes/helpers/class-choices.php:684
msgid "Czechia"
msgstr "Séc"

#: includes/helpers/class-choices.php:673
msgid "Belgium"
msgstr "Bỉ"

#: includes/helpers/class-choices.php:663
msgid "Worldwide"
msgstr "Toàn thế giới"

#: includes/admin/wizard/views/google-connect.php:26
#: includes/admin/wizard/views/rank-math-connect.php:32
msgid "Track page and keyword rankings with the Advanced Analytics module"
msgstr "Theo dõi thứ hạng trang và từ khóa với module Phân tích nâng cao"

#: includes/modules/analytics/class-analytics.php:529
msgid "Missing some posts/pages in the Analytics data? Clear the index and build a new one for more accurate stats."
msgstr "Thiếu một số bài viết / trang trong dữ liệu Phân tích? Xóa chỉ mục và xây dựng một chỉ mục mới để có số liệu thống kê chính xác hơn."

#. translators: Link to kb article
#: includes/modules/analytics/class-analytics.php:501
msgid "See your Google Search Console, Analytics and AdSense data without leaving your WP dashboard. %s."
msgstr "Xem dữ liệu Google Search Console, Analytics và AdSense của bạn mà không cần rời khỏi bảng tin WP. %s."

#: includes/helpers/class-schema.php:90
#: includes/settings/titles/post-types.php:136
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "WooCommerce Product"
msgstr "Sản phẩm WooCommerce"

#: includes/helpers/class-schema.php:94
#: includes/settings/titles/post-types.php:136
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "EDD Product"
msgstr "Sản phẩm EDD"

#: includes/frontend/class-shortcodes.php:295
msgid "Telephone"
msgstr "Điện thoại"

#: includes/admin/views/dashboard-help.php:70
msgid "Improve SEO Score"
msgstr "Cải thiện điểm SEO"

#: includes/modules/redirections/class-admin.php:251
#: includes/modules/redirections/class-admin.php:278
msgid "No valid ID found."
msgstr "Không tìm thấy ID hợp lệ."

#: includes/modules/404-monitor/views/help-tab-overview.php:36
msgid "Fix 404 Errors"
msgstr "Sửa lỗi 404"

#: includes/admin/views/dashboard-help.php:60
msgid "Import Data"
msgstr "Nhập dữ liệu"

#: includes/admin/importers/abstract-importer.php:154
#: includes/admin/importers/class-aioseo.php:81
msgid "Import Locations"
msgstr "Nhập vị trí"

#: includes/modules/sitemap/settings/html-sitemap.php:101
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Modified Date"
msgstr "Ngày sửa đổi"

#: includes/modules/sitemap/settings/html-sitemap.php:100
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Published Date"
msgstr "Ngày xuất bản"

#: includes/replace-variables/class-basic-variables.php:229
msgid "Organization Logo added in Local SEO Settings."
msgstr "Logo tổ chức đã được thêm vào trong Cài đặt SEO Địa phương."

#: includes/replace-variables/class-basic-variables.php:218
msgid "The Organization Name added in Local SEO Settings."
msgstr "Tên tổ chức đã được thêm vào trong Cài đặt SEO Địa phương."

#: includes/replace-variables/class-basic-variables.php:217
msgid "Organization Name"
msgstr "Tên tổ chức"

#: includes/replace-variables/class-post-variables.php:117
msgid "Current Post Thumbnail"
msgstr "Hình thu nhỏ bài viết hiện tại"

#: includes/replace-variables/class-post-variables.php:116
msgid "Post Thumbnail"
msgstr "Hình thu nhỏ bài viết"

#: includes/replace-variables/class-post-variables.php:106
msgid "URL of the current post/page"
msgstr "URL của bài viết/trang hiện tại"

#: includes/replace-variables/class-post-variables.php:105
msgid "Post URL"
msgstr "URL bài viết"

#: includes/admin/wizard/views/compatibility.php:79
msgid "You are using the recommended WordPress version."
msgstr "Bạn đang sử dụng phiên bản WordPress được khuyến nghị."

#: includes/frontend/class-shortcodes.php:354
msgid "Email:"
msgstr "Email:"

#: includes/modules/redirections/class-admin.php:305
msgid "Export Options"
msgstr "Tùy chọn xuất"

#: includes/modules/404-monitor/views/help-tab-overview.php:20
msgid "Knowledge Base Articles:"
msgstr "Bài viết cơ sở kiến ​​thức:"

#: includes/modules/404-monitor/views/help-tab-overview.php:16
msgid "With the 404 monitor you can see where users and search engines are unable to find your content."
msgstr "Với trình theo dõi 404, bạn có thể xem nơi người dùng và công cụ tìm kiếm không thể tìm thấy nội dung của bạn."

#: includes/modules/404-monitor/views/help-tab-overview.php:31
msgid "404 Monitor Settings"
msgstr "Cài đặt theo dõi 404"

#: includes/modules/schema/class-snippet-shortcode.php:101
msgid "No schema found."
msgstr "Không tìm thấy schema."

#: includes/rest/class-shared.php:345
msgid "schemas to add or update data."
msgstr "schemas để thêm hoặc cập nhật dữ liệu."

#. translators: WordPress Version
#: rank-math.php:201
msgid "You are using the outdated WordPress, please update it to version %s or higher."
msgstr "Bạn đang sử dụng WordPress lỗi thời, vui lòng cập nhật lên phiên bản %s hoặc cao hơn."

#: includes/admin/views/import-export/plugins-panel.php:20
msgid "Other Plugins"
msgstr "Các plugin khác"

#: includes/admin/views/import-export/backup-panel.php:17
msgid "Take a backup of your plugin settings in case you wish to restore them in future. Use it as backup before making substantial changes to Rank Math settings. For taking a backup of the SEO data of your content, use the XML Export option."
msgstr "Sao lưu cài đặt plugin của bạn trong trường hợp bạn muốn khôi phục chúng trong tương lai. Sử dụng nó làm bản sao lưu trước khi thực hiện các thay đổi đáng kể đối với cài đặt Rank Math. Để sao lưu dữ liệu SEO của nội dung, hãy sử dụng tùy chọn Xuất XML."

#. translators: Link to learn about import export panel KB article
#: includes/admin/views/import-export/plugins-panel.php:25
msgid "If you were using another plugin to add important SEO information to your website before switching to Rank Math SEO, you can import the settings and data here. %s"
msgstr "Nếu bạn đang sử dụng một plugin khác để thêm thông tin SEO quan trọng vào trang web của mình trước khi chuyển sang Rank Math SEO, bạn có thể nhập cài đặt và dữ liệu tại đây. %s"

#: includes/admin/views/import-export/backup-panel.php:15
msgid "Settings Backup"
msgstr "Sao lưu cài đặt"

#: includes/admin/views/import-export/import-export-panel.php:14
msgid "Plugin Settings"
msgstr "Cài đặt plugin"

#: includes/modules/seo-analysis/class-seo-analyzer.php:557
msgid "Unexpected API response."
msgstr "Phản hồi API không mong muốn."

#. translators: Link to learn about import export panel KB article
#: includes/admin/views/import-export/import-export-panel.php:19
msgid "Import or export your Rank Math settings, This option is useful for replicating Rank Math settings across multiple websites. %s"
msgstr "Nhập hoặc xuất cài đặt Rank Math của bạn, Tùy chọn này hữu ích cho việc sao chép cài đặt Rank Math trên nhiều trang web. %s"

#. Translators: placeholder is a HTTP error code.
#: includes/modules/seo-analysis/class-seo-analyzer.php:550
msgid "HTTP %d error."
msgstr "Lỗi HTTP %d."

#: includes/module/class-manager.php:352
msgid "Google Web Stories"
msgstr "Google Web Stories"

#. Translators: placeholders are opening and closing tag for link.
#: includes/modules/seo-analysis/views/seo-analyzer.php:45
msgid "Analyze your site by %1$s linking your Rank Math account %2$s"
msgstr "Phân tích trang web của bạn bằng cách %1$s liên kết tài khoản Rank Math của bạn %2$s"

#: includes/admin/wizard/views/compatibility.php:110
msgid "PHP OpenSSL Extension missing"
msgstr "Thiếu tiện ích mở rộng PHP OpenSSL"

#: includes/admin/wizard/views/compatibility.php:110
msgid "PHP OpenSSL Extension installed"
msgstr "Tiện ích mở rộng PHP OpenSSL đã được cài đặt"

#: includes/admin/class-post-filters.php:170
msgid "SEO Score: Ok"
msgstr "Điểm SEO: Ok"

#: includes/module/class-manager.php:353
msgid "Make any Story created with the Web Stories WordPress plugin SEO-Ready with automatic support for Schema and Meta tags."
msgstr "Làm cho bất kỳ Câu chuyện nào được tạo bằng plugin Web Stories WordPress sẵn sàng SEO với hỗ trợ tự động cho Schema và thẻ Meta."

#: includes/module/class-manager.php:357
msgid "Please activate Web Stories plugin to use this module."
msgstr "Vui lòng kích hoạt plugin Web Stories để sử dụng module này."

#: includes/admin/views/plugin-activation.php:25
#: includes/admin/wizard/views/search-console-ui.php:208
#: includes/admin/wizard/views/search-console-ui.php:363
msgid "Account"
msgstr "Tài khoản"

#: includes/admin/views/plugin-activation.php:27 assets/admin/js/common.js:1
msgid "Connected"
msgstr "Đã kết nối"

#: includes/admin/views/plugin-activation.php:47
#: includes/admin/wizard/views/google-connect.php:17
msgid "Disconnect Account"
msgstr "Ngắt kết nối tài khoản"

#: includes/admin/wizard/views/compatibility.php:104
msgid "PHP MBstring Extension installed"
msgstr "Phần mở rộng PHP MBstring đã được cài đặt"

#: includes/admin/class-registration.php:305
msgid "Read more by following this link."
msgstr "Đọc thêm bằng cách theo liên kết này."

#: includes/admin/class-notices.php:138
msgid "Please activate the WPML String Translation plugin to convert Rank Math Setting values in different languages."
msgstr "Vui lòng kích hoạt plugin WPML String Translation để chuyển đổi các giá trị Cài đặt Rank Math sang các ngôn ngữ khác nhau."

#: includes/admin/views/plugin-activation.php:42 assets/admin/js/post-list.js:1
#: includes/modules/content-ai/assets/js/content-ai-media.js:1
#: includes/modules/content-ai/assets/js/content-ai-page.js:1
#: includes/modules/content-ai/assets/js/content-ai.js:1
#: includes/modules/content-ai/blocks/command/assets/js/index.js:1
#: includes/modules/content-ai/blocks/command/assets/src/edit.js:24
msgid "Connect Now"
msgstr "Kết nối ngay"

#. translators: variables used to wrap the text in the strong tag.
#: includes/admin/views/plugin-activation.php:40
msgid "The plugin is currently not connected with your Rank Math account. Click on the button below to login or register for FREE using your %1$sGoogle account, Facebook account%2$s or %1$syour email account%2$s."
msgstr "Plugin hiện không được kết nối với tài khoản Rank Math của bạn. Nhấp vào nút bên dưới để đăng nhập hoặc đăng ký MIỄN PHÍ bằng tài khoản %1$sGoogle, tài khoản Facebook%2$s hoặc tài khoản %1$semail%2$s của bạn."

#: includes/admin/views/plugin-activation.php:27
msgid "Not Connected"
msgstr "Chưa kết nối"

#. translators: Link to Free Account Benefits KB article
#: includes/admin/class-registration.php:304
msgid "By connecting your free account, you get keyword suggestions directly from Google when entering the focus keywords. Not only that, get access to our revolutionary Content AI, SEO Analyzer inside WordPress that scans your website for SEO errors and suggest improvements. %s"
msgstr "Bằng cách kết nối tài khoản miễn phí của bạn, bạn sẽ nhận được gợi ý từ khóa trực tiếp từ Google khi nhập các từ khóa trọng tâm. Không chỉ vậy, hãy truy cập Content AI, Trình phân tích SEO mang tính cách mạng của chúng tôi bên trong WordPress, quét trang web của bạn để tìm lỗi SEO và đề xuất các cải tiến. %s"

#: includes/admin/wizard/views/compatibility.php:104
msgid "PHP MBstring Extension missing"
msgstr "Thiếu phần mở rộng PHP MBstring"

#. translators: taxonomy name
#: includes/settings/titles/taxonomies.php:89
msgid "Custom values for robots meta tag on %s archives."
msgstr "Giá trị tùy chỉnh cho thẻ meta robots trên kho lưu trữ %s."

#. translators: post type name
#: includes/settings/titles/post-types.php:248
msgid "Custom values for robots meta tag on %s."
msgstr "Giá trị tùy chỉnh cho thẻ meta robots trên %s."

#. translators: Option Description
#: includes/admin/wizard/class-compatibility.php:52
msgid "Custom Mode %s"
msgstr "Chế độ tùy chỉnh %s"

#. translators: Option Description
#: includes/admin/wizard/class-compatibility.php:50
msgid "For the advanced users who want to control every SEO aspect of the website. You are offered options to change everything and have full control over the website’s SEO."
msgstr "Dành cho người dùng nâng cao muốn kiểm soát mọi khía cạnh SEO của trang web. Bạn được cung cấp các tùy chọn để thay đổi mọi thứ và kiểm soát hoàn toàn SEO của trang web."

#. translators: Option Description
#: includes/admin/wizard/class-compatibility.php:50
msgid "Advanced %s"
msgstr "Nâng cao %s"

#. translators: Link to KB article
#: includes/admin/class-option-center.php:175
msgid "Customize SEO meta settings of pages like search results, 404s, etc. %s."
msgstr "Tùy chỉnh cài đặt meta SEO của các trang như kết quả tìm kiếm, 404, v.v. %s."

#. translators: Option Description
#: includes/admin/wizard/class-compatibility.php:48
msgid "Easy %s"
msgstr "Dễ dàng %s"

#: includes/admin/class-cmb2-fields.php:226
msgid "Image Preview"
msgstr "Xem trước hình ảnh"

#: includes/admin/class-cmb2-fields.php:224
msgid "Snippet"
msgstr "Đoạn trích"

#: includes/admin/class-cmb2-fields.php:225
msgid "Video Preview"
msgstr "Xem trước video"

#. translators: Link to KB article
#: includes/admin/class-option-center.php:169
msgid "Change SEO options related to the author archives. %s."
msgstr "Thay đổi các tùy chọn SEO liên quan đến kho lưu trữ tác giả. %s."

#. translators: Option Description
#: includes/admin/wizard/class-compatibility.php:48
msgid "For websites where you only want to change the basics and let Rank Math do most of the heavy lifting. Most settings are set to default as per industry best practices. One just has to set it and forget it."
msgstr "Dành cho các trang web mà bạn chỉ muốn thay đổi những điều cơ bản và để Rank Math thực hiện hầu hết các công việc nặng nhọc. Hầu hết các cài đặt được đặt mặc định theo các phương pháp hay nhất của ngành. Bạn chỉ cần đặt nó và quên nó đi."

#. translators: Link to KB article
#: includes/admin/class-option-center.php:161
msgid "Add SEO meta and OpenGraph details to your homepage. %s."
msgstr "Thêm meta SEO và chi tiết OpenGraph vào trang chủ của bạn. %s."

#: includes/admin/class-option-center.php:148
msgid "Use the <code>[rank_math_contact_info]</code> shortcode to display contact information in a nicely formatted way. You should also claim your business on Google if you have not already."
msgstr "Sử dụng mã ngắn <code>[rank_math_contact_info]</code> để hiển thị thông tin liên hệ theo cách được định dạng đẹp mắt. Bạn cũng nên xác nhận doanh nghiệp của mình trên Google nếu bạn chưa làm như vậy."

#. translators: 1. post type name 2. link
#. translators: 1. taxonomy name 2. link
#: includes/admin/class-option-center.php:240
#: includes/admin/class-option-center.php:294
msgid "Change Global SEO, Schema, and other settings for %1$s. %2$s"
msgstr "Thay đổi SEO toàn cầu, Schema và các cài đặt khác cho %1$s. %2$s"

#: includes/module/class-manager.php:158
msgid "Image SEO"
msgstr "SEO hình ảnh"

#: includes/modules/sitemap/settings/general.php:20
msgid "read here"
msgstr "đọc ở đây"

#: includes/modules/status/class-error-log.php:158
msgid "The error log cannot be retrieved: Error log file is too large."
msgstr "Không thể truy xuất nhật ký lỗi: Tệp nhật ký lỗi quá lớn."

#. translators: Link to kb article
#: includes/admin/class-option-center.php:87
msgid "Edit the contents of your .htaccess file easily. %s."
msgstr "Chỉnh sửa nội dung tệp .htaccess của bạn một cách dễ dàng. %s."

#. translators: Link to kb article
#: includes/admin/class-option-center.php:54
msgid "Change how some of the links open and operate on your website. %s."
msgstr "Thay đổi cách một số liên kết mở và hoạt động trên trang web của bạn. %s."

#. translators: 1. Link to KB article 2. Link to redirection setting scree
#: includes/modules/404-monitor/class-admin.php:199
msgid "Monitor broken pages that ruin user-experience and affect SEO. %s."
msgstr "Theo dõi các trang bị hỏng làm hỏng trải nghiệm người dùng và ảnh hưởng đến SEO. %s."

#. translators: Link to kb article
#: includes/modules/woocommerce/class-admin.php:76
msgid "Choose how you want Rank Math to handle your WooCommerce SEO. %s."
msgstr "Chọn cách bạn muốn Rank Math xử lý WooCommerce SEO của bạn. %s."

#: includes/admin/class-admin-header.php:109
msgid "Advanced Mode"
msgstr "Chế độ nâng cao"

#: includes/modules/database-tools/class-database-tools.php:281
#: includes/modules/database-tools/class-database-tools.php:300
msgid "Conversion started. A success message will be shown here once the process completes. You can close this page."
msgstr "Quá trình chuyển đổi đã bắt đầu. Một thông báo thành công sẽ được hiển thị tại đây sau khi quá trình hoàn tất. Bạn có thể đóng trang này."

#. translators: Link to kb article
#: includes/modules/redirections/class-admin.php:221
msgid "Easily create redirects without fiddling with tedious code. %s."
msgstr "Dễ dàng tạo chuyển hướng mà không cần mày mò với mã tẻ nhạt. %s."

#: includes/modules/status/class-error-log.php:147
msgid "The error log cannot be retrieved."
msgstr "Không thể truy xuất nhật ký lỗi."

#: includes/modules/status/class-error-log.php:76
msgid "Copy Log to Clipboard"
msgstr "Sao chép nhật ký vào bảng tạm"

#. Translators: placeholder is a link to WP_DEBUG documentation.
#: includes/modules/status/class-error-log.php:51
msgid "If you have %s enabled, errors will be stored in a log file. Here you can find the last 100 lines in reversed order so that you or the Rank Math support team can view it easily. The file cannot be edited here."
msgstr "Nếu bạn đã bật %s, các lỗi sẽ được lưu trữ trong tệp nhật ký. Tại đây, bạn có thể tìm thấy 100 dòng cuối cùng theo thứ tự đảo ngược để bạn hoặc nhóm hỗ trợ Rank Math có thể xem dễ dàng. Tệp không thể được chỉnh sửa ở đây."

#. Translators: placeholder is an activate button.
#: includes/modules/seo-analysis/seo-analysis-tests.php:195
msgid "Automatic updates are not enabled on your site. %s"
msgstr "Cập nhật tự động không được bật trên trang web của bạn. %s"

#: includes/admin/class-admin-header.php:108
msgid "Easy Mode"
msgstr "Chế độ dễ dàng"

#. translators: Redirection page url
#: includes/admin/class-option-center.php:147
msgid "Optimize for local searches and Knowledge Graph using these settings. %s."
msgstr "Tối ưu hóa cho tìm kiếm địa phương và Đồ thị tri thức bằng cách sử dụng các cài đặt này. %s."

#. translators: Link to KB article
#: includes/admin/class-option-center.php:141
msgid "Change Global meta settings that take effect across your website. %s."
msgstr "Thay đổi cài đặt meta toàn cầu có hiệu lực trên toàn bộ trang web của bạn. %s."

#. translators: Link to kb article
#: includes/admin/class-option-center.php:74
msgid "Change some uncommon but essential settings here. %s."
msgstr "Thay đổi một số cài đặt không phổ biến nhưng cần thiết tại đây. %s."

#. translators: Link to kb article
#: includes/admin/class-option-center.php:68
msgid "Enter verification codes for third-party webmaster tools. %s"
msgstr "Nhập mã xác minh cho các công cụ quản trị web của bên thứ ba. %s"

#. translators: Link to AMP plugin
#: includes/module/class-manager.php:305
msgid "Install %s to make Rank Math work with Accelerated Mobile Pages. Rank Math automatically adds required meta tags in all the AMP pages."
msgstr "Cài đặt %s để Rank Math hoạt động với Accelerated Mobile Pages. Rank Math tự động thêm các thẻ meta bắt buộc trong tất cả các trang AMP."

#. translators: Link to kb article
#: includes/admin/wizard/class-role.php:60
#: includes/module/class-manager.php:226
msgid "The Role Manager allows you to use WordPress roles to control which of your site users can have edit or view access to Rank Math's settings."
msgstr "Trình quản lý vai trò cho phép bạn sử dụng các vai trò WordPress để kiểm soát người dùng trang web nào của bạn có thể chỉnh sửa hoặc xem quyền truy cập vào cài đặt của Rank Math."

#: includes/module/class-manager.php:159
msgid "Advanced Image SEO options to supercharge your website. Automate the task of adding the ALT and Title tags to your images on the fly."
msgstr "Tùy chọn SEO hình ảnh nâng cao để tăng cường trang web của bạn. Tự động hóa nhiệm vụ thêm thẻ ALT và Tiêu đề vào hình ảnh của bạn một cách nhanh chóng."

#: includes/module/class-manager.php:152
msgid "Counts the total number of internal, external links, to and from links inside your posts. You can also see the same count in the Posts List Page."
msgstr "Đếm tổng số liên kết nội bộ, liên kết bên ngoài, đến và từ các liên kết bên trong bài viết của bạn. Bạn cũng có thể thấy số lượng tương tự trong Trang Danh sách Bài viết."

#: includes/module/class-manager.php:144
msgid "Enable Rank Math's sitemap feature, which helps search engines intelligently crawl your website's content. It also supports hreflang tag."
msgstr "Kích hoạt tính năng sơ đồ trang web của Rank Math, giúp các công cụ tìm kiếm thu thập thông tin nội dung trang web của bạn một cách thông minh. Nó cũng hỗ trợ thẻ hreflang."

#: includes/module/class-manager.php:126
msgid "Redirect non-existent content easily with 301 and 302 status code. This can help improve your site ranking. Also supports many other response codes."
msgstr "Chuyển hướng nội dung không tồn tại dễ dàng với mã trạng thái 301 và 302. Điều này có thể giúp cải thiện thứ hạng trang web của bạn. Cũng hỗ trợ nhiều mã phản hồi khác."

#: includes/module/class-manager.php:117
msgid "Dominate the search results for the local audiences by optimizing your website for Local SEO and it also helps you to aquire the Knowledge Graph."
msgstr "Thống trị kết quả tìm kiếm cho đối tượng địa phương bằng cách tối ưu hóa trang web của bạn cho SEO địa phương và nó cũng giúp bạn có được Đồ thị tri thức."

#: includes/module/class-manager.php:333
msgid "Optimize WooCommerce Pages for Search Engines by adding required metadata and Product Schema which will make your site stand out in the SERPs."
msgstr "Tối ưu hóa Trang WooCommerce cho Công cụ Tìm kiếm bằng cách thêm siêu dữ liệu bắt buộc và Schema Sản phẩm, điều này sẽ làm cho trang web của bạn nổi bật trong SERP."

#. translators: hreflang tags documentation link
#: includes/modules/sitemap/settings/general.php:19
msgid "Rank Math generates the default Sitemaps only and WPML takes care of the rest. When a search engine bot visits any post/page, it is shown hreflang tag that helps it crawl the translated pages. This is one of the recommended methods by Google. Please %s"
msgstr "Rank Math chỉ tạo Sơ đồ trang web mặc định và WPML sẽ lo phần còn lại. Khi bot của công cụ tìm kiếm truy cập vào bất kỳ bài viết / trang nào, nó sẽ hiển thị thẻ hreflang giúp nó thu thập dữ liệu các trang đã dịch. Đây là một trong những phương pháp được Google khuyến nghị. Xin vui lòng %s"

#. translators: %1$s: thing, %2$s: Learn more link.
#: includes/modules/sitemap/class-admin.php:164
#: includes/modules/sitemap/class-admin.php:230
msgid "Change Sitemap settings of %1$s. %2$s."
msgstr "Thay đổi cài đặt sơ đồ trang web của %1$s. %2$s."

#: includes/modules/woocommerce/views/options-general.php:22
msgid "WordPress Dashboard > Settings > Permalinks > Product permalinks Example: default: /product/accessories/action-figures/acme/ - becomes: /accessories/action-figures/acme/"
msgstr "Bảng tin WordPress> Cài đặt> Liên kết cố định> Ví dụ về liên kết cố định sản phẩm: mặc định: /product/accessories/action-figures/acme/ - trở thành: /accessories/action-figures/acme/"

#: includes/admin/class-option-center.php:62
msgid "Use the following code in your theme template files to display breadcrumbs."
msgstr "Sử dụng đoạn mã sau trong các tệp mẫu giao diện của bạn để hiển thị điều hướng trang."

#. translators: Link to kb article
#: includes/admin/class-option-center.php:61
msgid "Here you can set up the breadcrumbs function. %s"
msgstr "Tại đây bạn có thể thiết lập chức năng điều hướng trang. %s"

#: includes/modules/status/class-error-log.php:44
msgid "Error Log"
msgstr "Nhật ký lỗi"

#. translators: Link to kb article
#: includes/modules/robots-txt/class-robots-txt.php:69
msgid "Edit your robots.txt file to control what bots see. %s."
msgstr "Chỉnh sửa tệp robots.txt của bạn để kiểm soát những gì bot nhìn thấy. %s."

#: includes/settings/titles/homepage.php:27
msgid "Edit Page: "
msgstr "Chỉnh sửa trang: "

#. translators: something
#: includes/settings/titles/homepage.php:26
msgid "Static page is set as the front page (WP Dashboard > Settings > Reading). To add SEO title, description, and meta for the homepage, please click here: %s"
msgstr "Trang tĩnh được đặt làm trang chủ (Bảng tin WP> Cài đặt> Đọc). Để thêm tiêu đề SEO, mô tả và meta cho trang chủ, vui lòng nhấp vào đây: %s"

#. translators: plugin name
#: includes/admin/watcher/class-watcher.php:125
msgid "You are not allowed to deactivate this plugin: %s."
msgstr "Bạn không được phép hủy kích hoạt plugin này: %s."

#: includes/admin/watcher/class-watcher.php:153
#: includes/admin/watcher/class-watcher.php:160
msgid "Click here to Deactivate"
msgstr "Nhấn vào đây để Hủy kích hoạt"

#: includes/admin/watcher/class-watcher.php:49
msgid "Sorry, you are not allowed to deactivate plugins for this site."
msgstr "Xin lỗi, bạn không được phép hủy kích hoạt plugin cho trang web này."

#. translators: 1. Example text 2. Example text
#: includes/modules/woocommerce/views/options-general.php:20
msgid "Remove prefix like %1$s from product URL chosen at %2$s"
msgstr "Xóa tiền tố như %1$s khỏi URL sản phẩm được chọn tại %2$s"

#. Translators: %s is the word "nofollow" code tag and second one for the
#. filter link
#: includes/settings/general/others.php:90
msgid "filter"
msgstr "bộ lọc"

#. Translators: %s is the word "nofollow" code tag and second one for the
#. filter link
#: includes/settings/general/others.php:90
msgid "If you are showing the SEO scores on the front end, this option will insert a %1$s backlink to RankMath.com to show your support. You can change the link & the text by using this %2$s."
msgstr "Nếu bạn hiển thị điểm SEO ở giao diện người dùng, tùy chọn này sẽ chèn liên kết ngược %1$s đến RankMath.com để thể hiện sự ủng hộ của bạn. Bạn có thể thay đổi liên kết & văn bản bằng cách sử dụng %2$s này."

#. Translators: placeholder is an activate button.
#: includes/modules/seo-analysis/seo-analysis-tests.php:196
msgid "Enable Auto Updates"
msgstr "Bật cập nhật tự động"

#: includes/modules/seo-analysis/seo-analysis-tests.php:157
msgid "Automatic Updates"
msgstr "Cập nhật tự động"

#: includes/modules/seo-analysis/seo-analysis-tests.php:158
msgid "Enable automatic updates to ensure you are always using the latest version of Rank Math."
msgstr "Bật cập nhật tự động để đảm bảo bạn luôn sử dụng phiên bản Rank Math mới nhất."

#: includes/modules/seo-analysis/class-seo-analyzer.php:636
msgid "Priority"
msgstr "Ưu tiên"

#: includes/modules/seo-analysis/class-seo-analyzer.php:485
#: includes/modules/seo-analysis/seo-analysis-tests.php:187
#: includes/modules/seo-analysis/seo-analysis-tests.php:199
msgid "Rank Math auto-update option is enabled on your site."
msgstr "Tùy chọn tự động cập nhật Rank Math được bật trên trang web của bạn."

#: includes/rest/class-rest-helper.php:133
msgid "Invalid post ID."
msgstr "ID bài viết không hợp lệ."

#: includes/rest/class-rest-helper.php:118
msgid "Sorry, you are not allowed to edit this post."
msgstr "Xin lỗi, bạn không được phép chỉnh sửa bài viết này."

#: includes/rest/class-rest-helper.php:50
msgid "Sorry, you are not allowed to create/update redirection."
msgstr "Xin lỗi, bạn không được phép tạo/cập nhật chuyển hướng."

#: includes/modules/sitemap/class-admin.php:345
msgid "Click here to see the code."
msgstr "Nhấp vào đây để xem mã."

#: includes/modules/sitemap/class-admin.php:340
msgid "configuration file"
msgstr "tệp cấu hình"

#: includes/rest/class-rest-helper.php:102
msgid "Sorry, you are not allowed to edit this post type."
msgstr "Xin lỗi, bạn không được phép chỉnh sửa loại nội dung này."

#: includes/modules/schema/shortcode/event.php:37
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Event Attendance Mode"
msgstr "Chế độ tham dự sự kiện"

#: includes/modules/schema/shortcode/event.php:21
msgid "Online + Offline"
msgstr "Trực tuyến + Ngoại tuyến"

#: includes/modules/schema/shortcode/event.php:71
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Online Event URL"
msgstr "URL sự kiện trực tuyến"

#. translators: placeholders are units of time, e.g. '1 hour and 30 minutes'
#: includes/modules/schema/blocks/class-block-howto.php:495
msgid "%1$s and %2$s"
msgstr "%1$s và %2$s"

#. translators: placeholders are units of time, e.g. '1 day, 8 hours and 30
#. minutes'
#: includes/modules/schema/blocks/class-block-howto.php:497
msgid "%1$s, %2$s and %3$s"
msgstr "%1$s, %2$s và %3$s"

#: includes/modules/schema/blocks/class-block-howto.php:503
msgid "Total Time:"
msgstr "Tổng thời gian:"

#: includes/admin/importers/abstract-importer.php:153
msgid "Import Blocks"
msgstr "Nhập khối"

#. translators: %d is the number of minutes.
#. translators: %d: minutes
#: includes/modules/schema/blocks/class-block-howto.php:488
#: includes/opengraph/class-slack.php:311
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d phút"

#. translators: %d is the number of hours.
#: includes/modules/schema/blocks/class-block-howto.php:483
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d giờ"

#. translators: %d is the number of days.
#: includes/modules/schema/blocks/class-block-howto.php:478
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d ngày"

#: includes/admin/importers/class-status.php:131
msgid "Blocks import failed."
msgstr "Nhập khối thất bại."

#. translators: start, end, total
#: includes/admin/importers/class-status.php:119
msgid "Imported blocks from posts %1$s - %2$s out of %3$s "
msgstr "Đã nhập khối từ bài viết %1$s - %2$s trong tổng số %3$s "

#: includes/admin/importers/class-yoast.php:1268
msgid "No post found."
msgstr "Không tìm thấy bài viết."

#: includes/admin/importers/abstract-importer.php:153
msgid "Import and convert all compatible blocks in post contents."
msgstr "Nhập và chuyển đổi tất cả các khối tương thích trong nội dung bài viết."

#. translators: sitemap url
#: includes/modules/sitemap/class-admin.php:87
msgid "Your sitemap index can be found here: %s"
msgstr "Chỉ mục sơ đồ trang web của bạn có thể được tìm thấy tại đây: %s"

#: includes/modules/schema/blocks/views/options-general.php:33
#: assets/admin/js/blocks.js:1
msgid "Unordered"
msgstr "Không có thứ tự"

#: includes/modules/database-tools/class-database-tools.php:372
#: includes/modules/database-tools/class-database-tools.php:382
msgid "Convert Blocks"
msgstr "Chuyển đổi khối"

#: includes/modules/database-tools/class-database-tools.php:371
msgid "Are you sure you want to convert Yoast blocks into Rank Math blocks? This action is irreversible."
msgstr "Bạn có chắc chắn muốn chuyển đổi các khối Yoast thành các khối Rank Math không? Hành động này là không thể đảo ngược."

#: includes/modules/database-tools/class-database-tools.php:380
msgid "Convert TOC block created using AIOSEO. Use this option to easily move your previous blocks into Rank Math."
msgstr "Chuyển đổi khối TOC được tạo bằng AIOSEO. Sử dụng tùy chọn này để dễ dàng di chuyển các khối trước đó của bạn vào Rank Math."

#: includes/modules/schema/blocks/views/options-general.php:32
#: assets/admin/js/blocks.js:1
msgid "Numbered"
msgstr "Có đánh số"

#: includes/modules/database-tools/class-database-tools.php:369
msgid "Yoast Block Converter"
msgstr "Bộ chuyển đổi khối Yoast"

#: includes/modules/database-tools/class-database-tools.php:275
#: includes/modules/database-tools/class-database-tools.php:294
msgid "No posts found to convert."
msgstr "Không tìm thấy bài viết nào để chuyển đổi."

#: includes/modules/version-control/views/version-control-panel.php:27
msgid "Your Version"
msgstr "Phiên bản của bạn"

#: includes/modules/version-control/views/version-control-panel.php:51
msgid "This is the latest version of the plugin."
msgstr "Đây là phiên bản mới nhất của plugin."

#: includes/modules/version-control/views/auto-update-panel.php:31
msgid "Auto Update Plugin"
msgstr "Tự động cập nhật plugin"

#. translators: Version number.
#: includes/modules/version-control/views/version-control-panel.php:74
msgid "Install Version %s"
msgstr "Cài đặt phiên bản %s"

#. translators: Placeholder is version number.
#: includes/modules/version-control/class-version-control.php:167
msgid "Are you sure you want to install version %s?"
msgstr "Bạn có chắc chắn muốn cài đặt phiên bản %s không?"

#: includes/module/class-manager.php:278
#: includes/modules/database-tools/class-database-tools.php:314
msgid "Database Tools"
msgstr "Công cụ cơ sở dữ liệu"

#: includes/modules/version-control/views/version-control-panel.php:20
msgid "If you are facing issues after an update, you can reinstall a previous version with this tool."
msgstr "Nếu bạn gặp sự cố sau khi cập nhật, bạn có thể cài đặt lại phiên bản trước bằng công cụ này."

#. translators: Version number.
#: includes/modules/version-control/views/version-control-panel.php:74
msgid "Install Selected Version"
msgstr "Cài đặt phiên bản đã chọn"

#: includes/modules/version-control/class-rollback-version.php:143
msgid "Installing the rollback version&#8230;"
msgstr "Đang cài đặt phiên bản khôi phục…"

#: includes/modules/version-control/views/version-control-panel.php:45
msgid "Latest Stable Version"
msgstr "Phiên bản ổn định mới nhất"

#: includes/modules/version-control/class-rollback-version.php:139
msgid "Plugin rollback successful."
msgstr "Khôi phục plugin thành công."

#: includes/modules/version-control/views/version-control-panel.php:56
msgid "Rollback Version"
msgstr "Phiên bản khôi phục"

#: includes/modules/version-control/views/version-control-panel.php:39
msgid "This is the version you are using on this site."
msgstr "Đây là phiên bản bạn đang sử dụng trên trang web này."

#: includes/modules/version-control/class-beta-optin.php:243
#: includes/modules/version-control/class-beta-optin.php:268
msgid "This update will install a beta version of Rank Math."
msgstr "Bản cập nhật này sẽ cài đặt phiên bản beta của Rank Math."

#: includes/module/class-manager.php:272
#: includes/modules/version-control/class-version-control.php:245
#: includes/modules/version-control/class-version-control.php:261
msgid "Version Control"
msgstr "Kiểm soát phiên bản"

#: includes/modules/version-control/views/auto-update-panel.php:26
msgid "Turn on auto-updates to automatically update to stable versions of Rank Math as soon as they are released. The beta versions will never install automatically."
msgstr "Bật tự động cập nhật để tự động cập nhật lên các phiên bản ổn định của Rank Math ngay khi chúng được phát hành. Các phiên bản beta sẽ không bao giờ tự động cài đặt."

#: includes/modules/version-control/views/beta-optin-panel.php:34
msgid "Beta Tester"
msgstr "Người kiểm tra bản beta"

#. translators: Warning.
#: includes/modules/version-control/views/beta-optin-panel.php:29
msgid "%s It is not recommended to use the beta version on live production sites."
msgstr "%s Không nên sử dụng phiên bản beta trên các trang web sản xuất trực tiếp."

#: includes/modules/version-control/views/beta-optin-panel.php:27
msgid "You can take part in shaping Rank Math by test-driving the newest features and letting us know what you think. Turn on the Beta Tester feature to get notified about new beta releases. The beta version will not install automatically and you always have the option to ignore it."
msgstr "Bạn có thể tham gia định hình Rank Math bằng cách lái thử các tính năng mới nhất và cho chúng tôi biết suy nghĩ của bạn. Bật tính năng Người kiểm tra bản beta để nhận thông báo về các bản phát hành bản beta mới. Phiên bản beta sẽ không tự động cài đặt và bạn luôn có tùy chọn bỏ qua nó."

#: includes/modules/version-control/views/beta-optin-panel.php:17
msgid "Beta Opt-in"
msgstr "Chọn tham gia bản beta"

#: includes/modules/version-control/views/version-control-panel.php:76
msgid "Reinstalling, please wait..."
msgstr "Đang cài đặt lại, vui lòng đợi..."

#: includes/modules/version-control/views/version-control-panel.php:65
msgid "Roll back to this version."
msgstr "Khôi phục về phiên bản này."

#: includes/modules/version-control/views/version-control-panel.php:49
msgid "Update Now"
msgstr "Cập nhật ngay"

#: includes/modules/version-control/views/version-control-panel.php:37
msgid "You are using the latest version of the plugin."
msgstr "Bạn đang sử dụng phiên bản mới nhất của plugin."

#. Translators: placeholder is "Rolled Back Version:".
#: includes/modules/version-control/views/version-control-panel.php:34
msgid "Rolled Back Version: "
msgstr "Phiên bản đã khôi phục: "

#. Translators: placeholder is "Rolled Back Version:".
#: includes/modules/version-control/views/version-control-panel.php:34
msgid "%s Auto updates will not work, please update the plugin manually."
msgstr "%s Tự động cập nhật sẽ không hoạt động, vui lòng cập nhật plugin theo cách thủ công."

#. translators: Warning.
#. translators: placeholder is the word "warning".
#: includes/modules/version-control/views/beta-optin-panel.php:29
#: includes/modules/version-control/views/version-control-panel.php:22
#: assets/admin/js/components.js:1
msgid "Warning: "
msgstr "Cảnh báo: "

#. translators: placeholder is the word "warning".
#: includes/modules/version-control/views/version-control-panel.php:22
msgid "%s Previous versions may not be secure or stable. Proceed with caution and always create a backup."
msgstr "%s Các phiên bản trước có thể không an toàn hoặc ổn định. Hãy tiến hành thận trọng và luôn tạo bản sao lưu."

#: includes/modules/version-control/views/version-control-panel.php:17
msgid "Rollback to Previous Version"
msgstr "Khôi phục về phiên bản trước"

#: includes/modules/version-control/views/auto-update-panel.php:73
msgid "Rank Math will not auto-update because you have rolled back to a previous version. Update to the latest version manually to make this option work again."
msgstr "Rank Math sẽ không tự động cập nhật vì bạn đã khôi phục về phiên bản trước. Cập nhật lên phiên bản mới nhất theo cách thủ công để tùy chọn này hoạt động trở lại."

#: includes/modules/version-control/class-rollback-version.php:76
msgid "Rollback Plugin"
msgstr "Khôi phục plugin"

#: includes/admin/class-import-export.php:58
msgid "Import & Export"
msgstr "Nhập & Xuất"

#: includes/modules/status/class-error-log.php:78
#: includes/modules/status/class-system-status.php:57
#: includes/modules/content-ai/assets/js/content-ai-page.js:1
#: includes/modules/content-ai/assets/js/content-ai.js:1
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Copied!"
msgstr "Đã sao chép!"

#: includes/modules/status/class-system-status.php:55
msgid "Copy System Info to Clipboard"
msgstr "Sao chép thông tin hệ thống vào bảng tạm"

#: includes/modules/status/class-system-status.php:49
msgid "System Info"
msgstr "Thông tin hệ thống"

#: includes/settings/general/links.php:48
msgid "Redirect Orphan Attachments"
msgstr "Chuyển hướng tệp đính kèm mồ côi"

#: includes/modules/role-manager/class-capability-manager.php:68
msgid "On-Page Schema Settings"
msgstr "Cài đặt Schema trên trang"

#: includes/admin/class-option-center.php:237
#: includes/modules/sitemap/class-admin.php:161
msgid "Attachments"
msgstr "Tệp đính kèm"

#. translators: Post type name
#: includes/admin/wizard/class-schema-markup.php:147
msgid "Schema Type for %s"
msgstr "Loại schema cho %s"

#: includes/admin/wizard/class-schema-markup.php:58
#: includes/settings/titles/post-types.php:131
#: includes/settings/titles/post-types.php:159
msgid "Schema Type"
msgstr "Loại schema"

#: includes/module/class-manager.php:134
msgid "Schema (Structured Data)"
msgstr "Schema (Dữ liệu có cấu trúc)"

#: includes/admin/importers/class-aio-rich-snippet.php:192
#: includes/admin/importers/class-wp-schema-pro.php:601
msgid "Import Schemas"
msgstr "Nhập Schemas"

#: includes/modules/buddypress/class-admin.php:47
msgid "This tab contains SEO options for BuddyPress Group pages."
msgstr "Thẻ này chứa các tùy chọn SEO cho các trang Nhóm BuddyPress."

#: includes/helpers/class-choices.php:452
msgid "Review (Unsupported)"
msgstr "Đánh giá (Không được hỗ trợ)"

#: includes/rest/class-post.php:156
msgid "No meta rows found to update."
msgstr "Không tìm thấy hàng meta nào để cập nhật."

#: includes/replace-variables/class-post-variables.php:94
msgid "Custom or Generated SEO Description of the current post/page"
msgstr "Mô tả SEO Tùy chỉnh hoặc Được tạo của bài viết/trang hiện tại"

#: includes/replace-variables/class-post-variables.php:93
#: includes/modules/content-ai/assets/js/content-ai-page.js:1
#: includes/modules/content-ai/assets/js/content-ai.js:1
msgid "SEO Description"
msgstr "Mô tả SEO"

#: includes/replace-variables/class-post-variables.php:82
msgid "Custom or Generated SEO Title of the current post/page"
msgstr "Tiêu đề SEO Tùy chỉnh hoặc Được tạo tự động của bài viết/trang hiện tại"

#. translators: 1. Bold text 2. Bold text
#: rank-math.php:468
msgid "%1$s A filter to remove the Rank Math data from the database is present. Deactivating & Deleting this plugin will remove everything related to the Rank Math plugin. %2$s"
msgstr "%1$s Bộ lọc để xóa dữ liệu Rank Math khỏi cơ sở dữ liệu hiện có. Việc hủy kích hoạt & Xóa plugin này sẽ xóa mọi thứ liên quan đến plugin Rank Math. %2$s"

#: includes/modules/database-tools/class-update-score.php:292
#: assets/admin/js/rank-math-app.js:1
#: includes/modules/content-ai/assets/js/content-ai-page.js:1
#: includes/modules/content-ai/assets/js/content-ai.js:1
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Close"
msgstr "Đóng"

#: includes/modules/redirections/class-metabox.php:88
msgid "Can't update redirection."
msgstr "Không thể cập nhật chuyển hướng."

#: includes/rest/class-shared.php:218
msgid "Meta to add or update data."
msgstr "Meta để thêm hoặc cập nhật dữ liệu."

#: includes/admin/class-registration.php:129
msgid "Unable to connect Rank Math."
msgstr "Không thể kết nối Rank Math."

#. Translators: placeholder is a link to "Read more".
#: includes/modules/seo-analysis/class-result.php:98
#: includes/settings/general/others.php:20 assets/admin/js/rank-math-app.js:1
msgid "Read more"
msgstr "Đọc thêm"

#: includes/rest/class-shared.php:204 includes/rest/class-shared.php:340
msgid "Object unique id"
msgstr "ID duy nhất của đối tượng"

#: includes/rest/class-shared.php:198 includes/rest/class-shared.php:334
msgid "Object Type i.e. post, term, user"
msgstr "Loại đối tượng, ví dụ: bài viết, thuật ngữ, người dùng"

#: includes/modules/woocommerce/views/options-general.php:69
msgid "Remove Schema Markup Data from WooCommerce Shop archive pages."
msgstr "Xóa dữ liệu đánh dấu Schema khỏi các trang lưu trữ của cửa hàng WooCommerce."

#: includes/modules/woocommerce/views/options-general.php:68
msgid "Remove Schema Markup on Shop Archives"
msgstr "Xóa đánh dấu Schema trên lưu trữ cửa hàng"

#: includes/modules/redirections/class-metabox.php:110
msgid "Redirection updated successfully."
msgstr "Chuyển hướng đã được cập nhật thành công."

#: includes/admin/metabox/class-screen.php:279
msgid "username"
msgstr "tên người dùng"

#: includes/admin/class-registration.php:123
msgid "Rank Math plugin could not be connected."
msgstr "Plugin Rank Math không thể được kết nối."

#: includes/module/class-manager.php:324
msgid "Enable the BuddyPress module for Rank Math SEO to make your BuddyPress forum SEO friendly by adding proper meta tags to all forum pages."
msgstr "Kích hoạt module BuddyPress cho Rank Math SEO để làm cho diễn đàn BuddyPress của bạn thân thiện với SEO bằng cách thêm các thẻ meta phù hợp vào tất cả các trang diễn đàn."

#: includes/modules/database-tools/class-database-tools.php:345
#: includes/modules/database-tools/class-database-tools.php:348
msgid "Clear 404 Log"
msgstr "Xóa nhật ký 404"

#: includes/modules/database-tools/class-database-tools.php:346
msgid "Is the 404 error log getting out of hand? Use this option to clear ALL 404 logs generated by your website in the Rank Math 404 Monitor."
msgstr "Nhật ký lỗi 404 có đang vượt khỏi tầm tay? Sử dụng tùy chọn này để xóa TẤT CẢ nhật ký 404 được tạo bởi trang web của bạn trong Trình theo dõi Rank Math 404."

#: includes/modules/database-tools/class-database-tools.php:398
msgid "Getting a redirection loop or need a fresh start? Delete all the redirections using this tool. Note: This process is irreversible and will delete ALL your redirection rules."
msgstr "Bạn đang gặp phải vòng lặp chuyển hướng hoặc cần bắt đầu lại? Xóa tất cả các chuyển hướng bằng công cụ này. Lưu ý: Quá trình này là không thể đảo ngược và sẽ xóa TẤT CẢ các quy tắc chuyển hướng của bạn."

#: includes/modules/database-tools/class-database-tools.php:389
msgid "In some instances, the internal links data might show an inflated number or no number at all. Deleting the internal links data might fix the issue."
msgstr "Trong một số trường hợp, dữ liệu liên kết nội bộ có thể hiển thị số lượng bị tăng cao hoặc không có số nào cả. Việc xóa dữ liệu liên kết nội bộ có thể khắc phục sự cố."

#: includes/modules/database-tools/class-database-tools.php:332
msgid "Need a clean slate or not able to run the SEO Analyzer tool? Flushing the analysis data might fix the issue. Flushing SEO Analyzer data is entirely safe and doesn't remove any critical data from your website."
msgstr "Cần một bảng sạch hoặc không thể chạy công cụ SEO Analyzer? Việc xóa dữ liệu phân tích có thể khắc phục sự cố. Việc xóa dữ liệu SEO Analyzer hoàn toàn an toàn và không xóa bất kỳ dữ liệu quan trọng nào khỏi trang web của bạn."

#: includes/modules/database-tools/class-database-tools.php:331
msgid "Flush SEO Analyzer Data"
msgstr "Xóa dữ liệu SEO Analyzer"

#: includes/modules/database-tools/class-database-tools.php:340
msgid "Remove transients"
msgstr "Xóa transient"

#: includes/modules/database-tools/class-database-tools.php:339
msgid "If you see any issue while using Rank Math or one of its options - clearing the Rank Math transients fixes the problem in most cases. Deleting transients does not delete ANY data added using Rank Math."
msgstr "Nếu bạn gặp bất kỳ sự cố nào khi sử dụng Rank Math hoặc một trong các tùy chọn của nó - việc xóa transient Rank Math sẽ khắc phục sự cố trong hầu hết các trường hợp. Việc xóa transient không xóa BẤT KỲ dữ liệu nào được thêm bằng Rank Math."

#: includes/modules/database-tools/class-database-tools.php:338
msgid "Remove Rank Math Transients"
msgstr "Xóa transient Rank Math"

#: includes/replace-variables/class-variable.php:95 assets/admin/js/common.js:1
msgid "Example"
msgstr "Ví dụ"

#: includes/modules/buddypress/views/options-titles.php:72
msgid "Group Advanced Robots Meta"
msgstr "Meta Robot nâng cao nhóm"

#: includes/admin/class-cmb2-fields.php:226
msgid "Specify a maximum size of image preview to be shown for images on this page."
msgstr "Chỉ định kích thước tối đa của bản xem trước hình ảnh sẽ được hiển thị cho hình ảnh trên trang này."

#: includes/admin/class-cmb2-fields.php:225
msgid "Specify a maximum duration in seconds of an animated video preview."
msgstr "Chỉ định thời lượng tối đa tính bằng giây của bản xem trước video hoạt hình."

#: includes/admin/class-cmb2-fields.php:224
msgid "Specify a maximum text-length, in characters, of a snippet for your page."
msgstr "Chỉ định độ dài văn bản tối đa, tính bằng ký tự, của đoạn trích cho trang của bạn."

#: includes/replace-variables/class-base.php:188
msgid "Example Post title"
msgstr "Ví dụ tiêu đề bài viết"

#: includes/settings/titles/post-types.php:382
msgid "Custom Fields"
msgstr "Trường tùy chỉnh"

#: includes/admin/class-cmb2-fields.php:295 assets/admin/js/rank-math-app.js:1
msgid "Large"
msgstr "Lớn"

#: includes/settings/general/breadcrumbs.php:164
msgid "Show Blog Page"
msgstr "Hiển thị trang blog"

#: includes/admin/class-cmb2-fields.php:296 assets/admin/js/rank-math-app.js:1
msgid "Standard"
msgstr "Tiêu chuẩn"

#. translators: variable name
#: includes/replace-variables/class-variable.php:100
msgid "The $%1$s is required for variable %2$s."
msgstr "Yêu cầu $%1$s cho biến %2$s."

#: includes/replace-variables/class-variable.php:90
msgid "The $id variable is required."
msgstr "Biến $id là bắt buộc."

#. translators: 1. Credits left 2. Buy more credits link
#: includes/modules/content-ai/views/options.php:223
msgid "here"
msgstr "đây"

#. translators: %1$s link to the customize settings, %2$s closing tag for the
#. link
#: includes/modules/seo-analysis/seo-analysis-tests.php:217
msgid "You have not entered a tagline yet. It is a good idea to choose one. %1$sYou can fix this in the customizer%2$s."
msgstr "Bạn chưa nhập khẩu hiệu. Nên chọn một. %1$sBạn có thể sửa lỗi này trong trình tùy chỉnh%2$s."

#: includes/settings/titles/homepage.php:96
msgid "Homepage Advanced Robots"
msgstr "Robot nâng cao trang chủ"

#. translators: taxonomy name
#: includes/settings/titles/taxonomies.php:103
msgid "%s Archives Advanced Robots Meta"
msgstr "Meta Robot nâng cao kho lưu trữ %s"

#: includes/settings/titles/post-types.php:383
msgid "List of custom fields name to include in the Page analysis. Add one per line."
msgstr "Danh sách tên trường tùy chỉnh để đưa vào phân tích Trang. Thêm một trường trên mỗi dòng."

#. translators: post type name
#: includes/settings/titles/post-types.php:262
msgid "%s Advanced Robots Meta"
msgstr "Meta Robot nâng cao %s"

#: includes/settings/titles/misc.php:82
msgid "Date Advanced Robots"
msgstr "Robot nâng cao ngày tháng"

#: includes/settings/titles/author.php:79
msgid "Author Advanced Robots"
msgstr "Robot nâng cao tác giả"

#: includes/admin/class-assets.php:208
msgid "Add <code>rel=\"sponsored\"</code>"
msgstr "Thêm <code>rel=\"sponsored\"</code>"

#: includes/settings/titles/global.php:30 assets/admin/js/rank-math-app.js:1
msgid "Advanced Robots Meta"
msgstr "Meta Robot nâng cao"

#: includes/replace-variables/class-post-variables.php:72
msgid "Post Excerpt Only"
msgstr "Chỉ trích dẫn bài viết"

#: includes/settings/general/breadcrumbs.php:165
msgid "Show Blog Page in Breadcrumb."
msgstr "Hiển thị trang blog trong điều hướng trang."

#: includes/module/class-manager.php:343
msgid "ACF"
msgstr "ACF"

#: includes/modules/database-tools/class-database-tools.php:399
msgid "Are you sure you want to delete all the Redirection Rules? This action is irreversible."
msgstr "Bạn có chắc chắn muốn xóa tất cả các Quy tắc chuyển hướng không? Hành động này là không thể đảo ngược."

#: includes/modules/database-tools/class-database-tools.php:347
msgid "Are you sure you want to delete the 404 log? This action is irreversible."
msgstr "Bạn có chắc chắn muốn xóa nhật ký 404 không? Hành động này là không thể đảo ngược."

#: includes/modules/database-tools/class-database-tools.php:388
msgid "Delete Internal Links Data"
msgstr "Xóa dữ liệu liên kết nội bộ"

#: includes/modules/database-tools/class-database-tools.php:400
msgid "Delete Redirections"
msgstr "Xóa chuyển hướng"

#: includes/modules/status/class-status.php:78
msgid "Status & Tools"
msgstr "Trạng thái & Công cụ"

#: includes/modules/status/class-status.php:141
msgid "System Status"
msgstr "Trạng thái hệ thống"

#: includes/modules/database-tools/class-database-tools.php:189
msgid "Redirection rules successfully deleted."
msgstr "Các quy tắc chuyển hướng đã được xóa thành công."

#: includes/modules/database-tools/class-database-tools.php:169
msgid "404 Log successfully deleted."
msgstr "Nhật ký 404 đã được xóa thành công."

#: includes/modules/database-tools/class-database-tools.php:150
msgid "Internal Links successfully deleted."
msgstr "Các liên kết nội bộ đã được xóa thành công."

#: includes/modules/database-tools/class-database-tools.php:130
msgid "SEO Analyzer data successfully deleted."
msgstr "Dữ liệu SEO Analyzer đã được xóa thành công."

#. Translators: placeholder is the number of transients deleted.
#: includes/modules/database-tools/class-database-tools.php:112
msgid "%d Rank Math transient cleared."
msgid_plural "%d Rank Math transients cleared."
msgstr[0] "%d transient Rank Math đã được xóa."

#: includes/modules/database-tools/class-database-tools.php:397
msgid "Delete Redirections Rules"
msgstr "Xóa các quy tắc chuyển hướng"

#: includes/modules/database-tools/class-database-tools.php:391
msgid "Delete Internal Links"
msgstr "Xóa liên kết nội bộ"

#: includes/modules/database-tools/class-database-tools.php:390
msgid "Are you sure you want to delete Internal Links Data? This action is irreversible."
msgstr "Bạn có chắc chắn muốn xóa Dữ liệu liên kết nội bộ không? Hành động này là không thể đảo ngược."

#: includes/modules/database-tools/class-database-tools.php:333
msgid "Clear SEO Analyzer"
msgstr "Xóa SEO Analyzer"

#: includes/module/class-manager.php:348
msgid "Please activate ACF plugin to use this module."
msgstr "Vui lòng kích hoạt plugin ACF để sử dụng module này."

#: includes/module/class-manager.php:344
msgid "ACF support helps Rank Math SEO read and analyze content written in the Advanced Custom Fields. If your theme uses ACF, you should enable this option."
msgstr "Hỗ trợ ACF giúp Rank Math SEO đọc và phân tích nội dung được viết trong Trường tùy chỉnh nâng cao. Nếu giao diện của bạn sử dụng ACF, bạn nên bật tùy chọn này."

#. Translators: placeholder is the Settings page URL.
#: includes/modules/robots-txt/options.php:79
msgid "<strong>Warning:</strong> your site's search engine visibility is set to Hidden in <a href=\"%1$s\" target=\"_blank\">Settings &gt; Reading</a>. This means that the changes you make here will not take effect. Set the search engine visibility to Public to be able to change the robots.txt content."
msgstr "<strong>Cảnh báo:</strong> khả năng hiển thị công cụ tìm kiếm của trang web của bạn được đặt thành Ẩn trong <a href=\"%1$s\" target=\"_blank\">Cài đặt&gt; Đọc</a> . Điều này có nghĩa là những thay đổi bạn thực hiện ở đây sẽ không có hiệu lực. Đặt chế độ hiển thị của công cụ tìm kiếm thành Công khai để có thể thay đổi nội dung robots.txt."

#: includes/modules/buddypress/views/options-titles.php:31
msgid "BuddyPress group description"
msgstr "Mô tả nhóm BuddyPress"

#: includes/modules/buddypress/class-admin.php:40
msgid "BuddyPress:"
msgstr "BuddyPress:"

#: includes/modules/buddypress/class-buddypress.php:120
msgid "Group description of the current group"
msgstr "Mô tả nhóm của nhóm hiện tại"

#: includes/modules/buddypress/class-buddypress.php:119
msgid "Group Description."
msgstr "Mô tả nhóm."

#: includes/modules/buddypress/class-buddypress.php:109
msgid "Group name of the current group"
msgstr "Tên nhóm của nhóm hiện tại"

#: includes/modules/buddypress/class-buddypress.php:108
msgid "Group name."
msgstr "Tên nhóm."

#: includes/modules/buddypress/class-admin.php:46
msgid "Groups"
msgstr "Các nhóm"

#: includes/modules/buddypress/views/options-titles.php:18
msgid "Title tag for groups"
msgstr "Thẻ tiêu đề cho các nhóm"

#: includes/modules/buddypress/views/options-titles.php:60
msgid "Custom values for robots meta tag on groups page."
msgstr "Giá trị tùy chỉnh cho thẻ meta robot trên trang nhóm."

#: includes/modules/buddypress/views/options-titles.php:45
msgid "Select custom robots meta for Group archive pages. Otherwise the default meta will be used, as set in the Global Meta tab."
msgstr "Chọn meta robot tùy chỉnh cho các trang lưu trữ Nhóm. Nếu không, meta mặc định sẽ được sử dụng, như được đặt trong tab Meta chung."

#: includes/modules/buddypress/views/options-titles.php:44
#: includes/modules/buddypress/views/options-titles.php:59
msgid "Group Robots Meta"
msgstr "Meta Robot nhóm"

#: includes/modules/buddypress/views/options-titles.php:30
msgid "Group Description"
msgstr "Mô tả nhóm"

#: includes/modules/buddypress/views/options-titles.php:17
msgid "Group Title"
msgstr "Tiêu đề nhóm"

#: includes/modules/robots-txt/options.php:42
msgid "robots.txt file is not writable."
msgstr "Tệp robots.txt không thể ghi."

#: includes/admin/wizard/class-schema-markup.php:34
msgid "Schema adds metadata to your website, resulting in rich search results and more traffic."
msgstr "Schema thêm siêu dữ liệu vào trang web của bạn, dẫn đến kết quả tìm kiếm phong phú và nhiều lưu lượng truy cập hơn."

#: includes/admin/wizard/class-schema-markup.php:33
msgid "Schema Markup "
msgstr "Đánh dấu schema "

#: includes/admin/class-setup-wizard.php:151
msgid "Schema Markup"
msgstr "Đánh dấu schema"

#: includes/module/class-manager.php:328
msgid "Please activate BuddyPress plugin to use this module."
msgstr "Vui lòng kích hoạt plugin BuddyPress để sử dụng module này."

#: includes/module/class-manager.php:323
msgid "BuddyPress"
msgstr "BuddyPress"

#: includes/module/class-manager.php:266
msgid "Robots Txt"
msgstr "Txt rô bốt"

#: includes/admin/class-options.php:307
#: includes/modules/version-control/views/auto-update-panel.php:81
#: includes/modules/version-control/views/beta-optin-panel.php:51
#: assets/admin/js/common.js:1
msgid "Save Changes"
msgstr "Lưu thay đổi"

#: includes/admin/class-options.php:308
msgid "Reset Options"
msgstr "Đặt lại tùy chọn"

#: includes/admin/class-admin-bar-menu.php:397
msgid "Google Rich Results Test - Googlebot Desktop"
msgstr "Kiểm tra kết quả phong phú của Google - Googlebot trên máy tính để bàn"

#: includes/admin/class-admin-bar-menu.php:395
msgid "Google Rich Results (Desktop)"
msgstr "Kết quả phong phú của Google (Máy tính để bàn)"

#: includes/admin/class-admin-bar-menu.php:391
msgid "Google Rich Results Test - Googlebot Smartphone"
msgstr "Kiểm tra kết quả phong phú của Google - Googlebot trên điện thoại thông minh"

#: includes/admin/class-admin-bar-menu.php:389
msgid "Google Rich Results (Mobile)"
msgstr "Kết quả phong phú của Google (Di động)"

#: includes/replace-variables/class-term-variables.php:59
msgid "Custom Term (advanced)"
msgstr "Thuật ngữ tùy chỉnh (nâng cao)"

#: includes/replace-variables/class-term-variables.php:70
msgid "Custom Term description"
msgstr "Mô tả thuật ngữ tùy chỉnh"

#: includes/admin/wizard/views/ready.php:24
msgid "Enable auto update of the plugin"
msgstr "Bật tự động cập nhật plugin"

#: includes/modules/version-control/views/auto-update-panel.php:16
msgid "Auto Update"
msgstr "Tự động cập nhật"

#: includes/replace-variables/class-term-variables.php:62
msgid "Custom term value"
msgstr "Giá trị thuật ngữ tùy chỉnh"

#: includes/replace-variables/class-term-variables.php:60
msgid "Custom term value."
msgstr "Giá trị thuật ngữ tùy chỉnh."

#: includes/settings/titles/author.php:20
msgid "Enables or disables Author Archives. If disabled, the Author Archives are redirected to your homepage. To avoid duplicate content issues, noindex author archives if you keep them enabled."
msgstr "Bật hoặc tắt Lưu trữ tác giả. Nếu bị tắt, Lưu trữ tác giả sẽ được chuyển hướng đến trang chủ của bạn. Để tránh các vấn đề nội dung trùng lặp, hãy bỏ chỉ mục lưu trữ tác giả nếu bạn giữ chúng được bật."

#: includes/replace-variables/class-manager.php:109
msgid "The variable has already been registered."
msgstr "Biến đã được đăng ký."

#: includes/replace-variables/class-manager.php:104
msgid "Variable names can only contain alphanumeric characters, underscores and dashes."
msgstr "Tên biến chỉ có thể chứa các ký tự chữ và số, dấu gạch dưới và dấu gạch ngang."

#. Translators: placeholder is an example URL.
#: includes/settings/titles/misc.php:22
msgid "Enable or disable the date archives (e.g: %s). If this option is disabled, the date archives will be redirected to the homepage."
msgstr "Bật hoặc tắt lưu trữ ngày tháng (ví dụ: %s). Nếu tùy chọn này bị tắt, lưu trữ ngày tháng sẽ được chuyển hướng đến trang chủ."

#: includes/replace-variables/class-manager.php:302
#: includes/replace-variables/class-term-variables.php:71
#: includes/replace-variables/class-term-variables.php:73
msgid "Custom Term description."
msgstr "Mô tả thuật ngữ tùy chỉnh."

#: includes/replace-variables/class-manager.php:291
msgid "Custom Term title."
msgstr "Tiêu đề thuật ngữ tùy chỉnh."

#. translators: Taxonomy name.
#: includes/replace-variables/class-manager.php:285
msgid "%s Description"
msgstr "Mô tả %s"

#. translators: Taxonomy name.
#: includes/replace-variables/class-manager.php:283
msgid "%s Title"
msgstr "Tiêu đề %s"

#: includes/3rdparty/divi/class-divi.php:156
#: includes/admin/class-assets.php:131
msgid "Please use the correct format."
msgstr "Vui lòng sử dụng đúng định dạng."

#: includes/modules/sitemap/sitemap-xsl.php:172
msgid "Phone number"
msgstr "Số điện thoại"

#: includes/modules/schema/shortcode/course.php:27
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Course Provider Name"
msgstr "Tên nhà cung cấp khóa học"

#: includes/modules/sitemap/sitemap-xsl.php:124
msgid "KML File"
msgstr "Tệp KML"

#: includes/modules/sitemap/sitemap-xsl.php:173
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Latitude"
msgstr "Vĩ độ"

#: includes/modules/sitemap/sitemap-xsl.php:174
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Longitude"
msgstr "Kinh độ"

#: includes/modules/schema/shortcode/jobposting.php:34
msgid "Payroll"
msgstr "Lương bổng"

#: includes/modules/schema/shortcode/event.php:86
msgid "Performer Name"
msgstr "Tên người biểu diễn"

#: includes/modules/schema/shortcode/event.php:93
msgid "Performer URL"
msgstr "URL của người biểu diễn"

#. translators: %s is a Rank Math link.
#: includes/class-frontend-seo-score.php:163
msgid "Powered by %s"
msgstr "Được cung cấp bởi %s"

#: includes/settings/general/others.php:50
msgid "SEO Score Template"
msgstr "Mẫu điểm SEO"

#: includes/admin/class-import-export.php:397
msgid "Settings could not be imported: Upload failed."
msgstr "Không thể nhập cài đặt: Tải lên không thành công."

#: includes/rest/class-front.php:150
msgid "Site token"
msgstr "Mã thông báo trang web"

#: includes/modules/schema/shortcode/jobposting.php:83
#: includes/replace-variables/class-basic-variables.php:228
msgid "Organization Logo"
msgstr "Logo tổ chức"

#: includes/settings/general/others.php:51
msgid "Change the styling for the front end SEO score badge."
msgstr "Thay đổi kiểu dáng cho huy hiệu điểm SEO giao diện người dùng."

#: includes/3rdparty/divi/class-divi.php:159
#: includes/admin/class-assets.php:134
msgid "Please enter a valid URL."
msgstr "Vui lòng nhập URL hợp lệ."

#: includes/3rdparty/divi/class-divi.php:158
#: includes/admin/class-assets.php:133
msgid "Please enter a valid email address."
msgstr "Vui lòng nhập địa chỉ email hợp lệ."

#. translators: link to rankmath.com
#: includes/modules/sitemap/sitemap-xsl.php:145
msgid "Learn more about <a href=\"%s\" target=\"_blank\">KML File</a>."
msgstr "Tìm hiểu thêm về <a href=\"%s\" target=\"_blank\">Tệp KML</a>."

#. translators: link to rankmath.com
#: includes/modules/sitemap/sitemap-xsl.php:132
msgid "This KML File is generated by <a href=\"%s\" target=\"_blank\">Rank Math WordPress SEO Plugin</a>. It is used to provide location information to Google."
msgstr "Tệp KML này được tạo bởi <a href=\"%s\" target=\"_blank\">Plugin Rank Math WordPress SEO</a>. Nó được sử dụng để cung cấp thông tin vị trí cho Google."

#: includes/modules/woocommerce/views/options-general.php:80
msgid "Select Product Brand Taxonomy to use in Schema.org & OpenGraph markup."
msgstr "Chọn phân loại thương hiệu sản phẩm để sử dụng trong đánh dấu Schema.org & OpenGraph."

#: includes/modules/woocommerce/views/options-general.php:79
msgid "Brand"
msgstr "Thương hiệu"

#: includes/modules/sitemap/settings/html-sitemap.php:30
#: includes/modules/sitemap/settings/html-sitemap.php:42
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Shortcode"
msgstr "Mã ngắn"

#: includes/modules/schema/shortcode/person.php:41
#: includes/modules/content-ai/assets/js/content-ai-page.js:1
#: includes/modules/content-ai/assets/js/content-ai.js:1
msgid "Job Title"
msgstr "Chức danh công việc"

#: includes/modules/schema/shortcode/jobposting.php:76
#: includes/replace-variables/class-basic-variables.php:239
msgid "Organization URL"
msgstr "URL của tổ chức"

#: includes/modules/schema/shortcode/jobposting.php:69
msgid "Hiring Organization "
msgstr "Tổ chức tuyển dụng "

#: includes/modules/schema/shortcode/jobposting.php:62
msgid "Employment Type "
msgstr "Loại hình công việc "

#: includes/modules/schema/shortcode/jobposting.php:20
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Salary"
msgstr "Lương"

#: includes/modules/schema/shortcode/book.php:41
#: includes/modules/content-ai/assets/js/content-ai-page.js:1
#: includes/modules/content-ai/assets/js/content-ai.js:1
msgid "Format"
msgstr "Định dạng"

#: includes/settings/general/others.php:88
msgid "Support Us with a Link"
msgstr "Ủng hộ chúng tôi bằng liên kết"

#: includes/settings/general/others.php:65
msgid "SEO Score Position"
msgstr "Vị trí điểm SEO"

#: includes/settings/general/others.php:54
msgid "Square"
msgstr "Hình vuông"

#: includes/settings/general/others.php:53
msgid "Circle"
msgstr "Hình tròn"

#: includes/settings/titles/author.php:63
msgid "Custom values for robots meta tag on author page."
msgstr "Giá trị tùy chỉnh cho thẻ meta robot trên trang tác giả."

#: includes/settings/titles/author.php:46
msgid "Select custom robots meta for author page, such as <code>nofollow</code>, <code>noarchive</code>, etc. Otherwise the default meta will be used, as set in the Global Meta tab."
msgstr "Chọn meta robot tùy chỉnh cho trang tác giả, chẳng hạn như <code>nofollow</code>, <code>noarchive</code>, v.v. Nếu không, meta mặc định sẽ được sử dụng, như được đặt trong tab Meta chung."

#. translators: post type name
#: includes/settings/titles/author.php:45
#: includes/settings/titles/author.php:62
msgid "Author Robots Meta"
msgstr "Meta Robot tác giả"

#: includes/settings/titles/misc.php:70
msgid "Custom values for robots meta tag on date page."
msgstr "Giá trị tùy chỉnh cho thẻ meta robot trên trang ngày tháng."

#. translators: post type name
#: includes/settings/titles/misc.php:69
msgid "Date Robots Meta"
msgstr "Meta Robot ngày tháng"

#: includes/rest/class-front.php:88
msgid "Site disconnected successfully."
msgstr "Trang web đã được ngắt kết nối thành công."

#: includes/module/class-manager.php:313
msgid "bbPress"
msgstr "bbPress"

#. translators: 1.SEO Score Shortcode 2. SEO Score function
#: includes/settings/general/others.php:68
msgid "Display the badges automatically, or insert the %1$s shortcode in your posts and the %2$s template tag in your theme template files."
msgstr "Hiển thị huy hiệu tự động hoặc chèn mã ngắn %1$s vào bài viết của bạn và thẻ mẫu %2$s vào tệp mẫu giao diện của bạn."

#: includes/module/class-manager.php:318
msgid "Please activate bbPress plugin to use this module."
msgstr "Vui lòng kích hoạt plugin bbPress để sử dụng module này."

#: includes/module/class-manager.php:314
msgid "Add proper Meta tags to your bbPress forum posts, categories, profiles, etc. Get more options to take control of what search engines see and how they see it."
msgstr "Thêm các thẻ Meta phù hợp vào bài viết diễn đàn bbPress, danh mục, hồ sơ, v.v. của bạn. Nhận thêm tùy chọn để kiểm soát những gì công cụ tìm kiếm nhìn thấy và cách họ nhìn thấy nó."

#: includes/settings/general/others.php:39
msgid "SEO Score Post Types"
msgstr "Loại nội dung điểm SEO"

#: includes/helpers/class-choices.php:73
msgid "Instructs search engines to index and show these pages in the search results."
msgstr "Chỉ thị cho công cụ tìm kiếm lập chỉ mục và hiển thị các trang này trong kết quả tìm kiếm."

#: includes/modules/analytics/class-analytics-common.php:145
msgid "Review analytics and sitemaps"
msgstr "Xem xét phân tích và sơ đồ trang web"

#: includes/helpers/class-choices.php:73 assets/admin/js/rank-math-app.js:1
msgid "Index"
msgstr "Chỉ mục"

#. translators: 1 is plugin name
#: includes/admin/wizard/class-import.php:186
msgid "Import settings and meta data from the %1$s plugin."
msgstr "Nhập cài đặt và siêu dữ liệu từ plugin %1$s."

#. translators: 1 is plugin name
#: includes/admin/wizard/class-import.php:186
msgid "Import meta data from the %1$s plugin."
msgstr "Nhập siêu dữ liệu từ plugin %1$s."

#. translators: 1 is plugin name
#: includes/admin/wizard/class-import.php:193
msgid " %1$s plugin will be disabled automatically moving forward to avoid conflicts. <strong>It is thus recommended to import the data you need now.</strong>"
msgstr " Plugin %1$s sẽ tự động bị vô hiệu hóa để tránh xung đột. <strong>Do đó, bạn nên nhập dữ liệu bạn cần ngay bây giờ.</strong>"

#. translators: 2 is link to Knowledge Base article
#: includes/admin/wizard/class-import.php:189
msgid "The process may take a few minutes if you have a large number of posts or pages <a href=\"%2$s\" target=\"_blank\">Learn more about the import process here.</a>"
msgstr "Quá trình có thể mất vài phút nếu bạn có một lượng lớn bài viết hoặc trang <a href=\"%2$s\" target=\"_blank\">Tìm hiểu thêm về quy trình nhập ở đây.</a>"

#. translators: xsl value count
#: includes/modules/sitemap/sitemap-xsl.php:162
#: includes/modules/sitemap/sitemap-xsl.php:298
msgid "<a href=\"%s\">&#8592; Sitemap Index</a>"
msgstr "<a href=\"%s\">← Chỉ mục Sơ đồ trang web</a>"

#: includes/modules/sitemap/sitemap-xsl.php:260
msgid "Last Modified"
msgstr "Sửa đổi lần cuối"

#: includes/modules/redirections/views/debugging.php:47
msgid "or"
msgstr "hoặc"

#: includes/modules/sitemap/sitemap-xsl.php:212
msgid "XML Sitemap"
msgstr "Sơ đồ trang web XML"

#: includes/modules/sitemap/sitemap-xsl.php:310
msgid "Last Mod."
msgstr "Sửa đổi lần cuối."

#. translators: xsl value count
#: includes/modules/sitemap/sitemap-xsl.php:288
msgid "This XML Sitemap contains <strong>%s</strong> URLs."
msgstr "Sơ đồ trang web XML này chứa <strong>%s</strong> URL."

#. translators: xsl value count
#: includes/modules/sitemap/sitemap-xsl.php:249
msgid "This XML Sitemap Index file contains <strong>%s</strong> sitemaps."
msgstr "Tệp chỉ mục Sơ đồ trang web XML này chứa <strong>%s</strong> sơ đồ trang web."

#. translators: link to rankmath.com
#: includes/modules/sitemap/sitemap-xsl.php:233
msgid "Learn more about <a href=\"%s\" target=\"_blank\">XML Sitemaps</a>."
msgstr "Tìm hiểu thêm về <a href=\"%s\" target=\"_blank\">Sơ đồ trang web XML</a>."

#. translators: link to rankmath.com
#: includes/modules/sitemap/sitemap-xsl.php:220
msgid "This XML Sitemap is generated by <a href=\"%s\" target=\"_blank\">Rank Math WordPress SEO Plugin</a>. It is what search engines like Google use to crawl and re-crawl posts/pages/products/images/archives on your website."
msgstr "Sơ đồ trang web XML này được tạo bởi <a href=\"%s\" target=\"_blank\">Plugin Rank Math WordPress SEO</a>. Đó là những gì công cụ tìm kiếm như Google sử dụng để thu thập dữ liệu và thu thập dữ liệu lại các bài viết/trang/sản phẩm/hình ảnh/lưu trữ trên trang web của bạn."

#: includes/frontend/class-head.php:412
msgid "Search Engine Optimization by Rank Math - https://rankmath.com/"
msgstr "Tối ưu hóa công cụ tìm kiếm bởi Rank Math - https://rankmath.com/"

#: includes/settings/titles/post-types.php:316
msgid "Thumbnail for Facebook"
msgstr "Hình thu nhỏ cho Facebook"

#: includes/settings/titles/post-types.php:317
msgid "Image displayed when your page is shared on Facebook and other social networks. Use images that are at least 1200 x 630 pixels for the best display on high resolution devices."
msgstr "Hình ảnh được hiển thị khi trang của bạn được chia sẻ trên Facebook và các mạng xã hội khác. Sử dụng hình ảnh có kích thước ít nhất 1200 x 630 pixel để hiển thị tốt nhất trên thiết bị có độ phân giải cao."

#: includes/modules/schema/class-admin.php:70 assets/admin/js/blocks.js:1
#: includes/modules/analytics/assets/js/stats.js:1
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
msgid "Schema"
msgstr "Schema"

#: includes/settings/general/breadcrumbs.php:74
msgid "Homepage Link"
msgstr "Liên kết trang chủ"

#: includes/settings/titles/global.php:65
msgid "Rewrite Titles"
msgstr "Viết lại tiêu đề"

#. translators: API error
#: includes/modules/seo-analysis/class-seo-analyzer.php:402
msgid "<strong>API Error:</strong> %s"
msgstr "<strong>Lỗi API:</strong> %s"

#: includes/rest/class-rest-helper.php:227 includes/rest/class-shared.php:209
msgid "Sorry, field is empty which is not allowed."
msgstr "Xin lỗi, trường trống là không được phép."

#: includes/rest/class-admin.php:350
msgid "Module state either on or off"
msgstr "Trạng thái module bật hoặc tắt"

#: includes/rest/class-admin.php:344
msgid "Module slug"
msgstr "Tên module"

#: includes/settings/titles/global.php:66
msgid "Your current theme doesn't support title-tag. Enable this option to rewrite page, post, category, search and archive page titles."
msgstr "Giao diện hiện tại của bạn không hỗ trợ thẻ tiêu đề. Bật tùy chọn này để viết lại tiêu đề trang, bài viết, danh mục, tìm kiếm và lưu trữ trang."

#: includes/settings/general/breadcrumbs.php:75
msgid "Link to use for homepage (first item) in breadcrumbs."
msgstr "Liên kết để sử dụng cho trang chủ (mục đầu tiên) trong điều hướng trang."

#: includes/settings/general/others.php:78
msgid "Custom (use shortcode)"
msgstr "Tùy chỉnh (sử dụng mã ngắn)"

#: includes/modules/schema/class-snippet-shortcode.php:353
msgid "Editor's Rating:"
msgstr "Xếp hạng của biên tập viên:"

#: includes/settings/general/others.php:77
msgid "Above & Below Content"
msgstr "Trên & Dưới nội dung"

#: includes/settings/general/others.php:76
msgid "Above Content"
msgstr "Trên nội dung"

#: includes/settings/general/others.php:75
msgid "Below Content"
msgstr "Dưới nội dung"

#. translators: link to general setting screen
#: includes/modules/seo-analysis/seo-analysis-tests.php:46
msgid "Your site may not be visible to search engine."
msgstr "Trang web của bạn có thể không hiển thị với công cụ tìm kiếm."

#: includes/modules/seo-analysis/seo-analysis-tests.php:522
msgid "Your site is accessible by search engine."
msgstr "Trang web của bạn có thể được truy cập bởi công cụ tìm kiếm."

#: includes/modules/seo-analysis/seo-analysis-tests.php:511
msgid "Attention: Search Engines can't see your website."
msgstr "Chú ý: Công cụ tìm kiếm không thể xem trang web của bạn."

#. translators: %1$s link to the reading settings, %2$s closing tag for the
#. link
#: includes/modules/seo-analysis/seo-analysis-tests.php:50
msgid "You must %1$sgo to your Reading Settings%2$s and uncheck the box for Search Engine Visibility."
msgstr "Bạn phải %1$sđi tới Cài đặt đọc của bạn%2$s và bỏ chọn hộp cho Khả năng hiển thị của công cụ tìm kiếm."

#: includes/modules/seo-analysis/seo-analysis-tests.php:44
msgid "Blog Public"
msgstr "Blog công khai"

#. translators: %s expands to the current page number
#: includes/frontend/class-breadcrumbs.php:584
msgid "Page %s"
msgstr "Trang %s"

#: includes/modules/schema/shortcode/product.php:48
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Price Valid Until"
msgstr "Giá hợp lệ cho đến khi"

#: includes/admin/class-post-columns.php:235
msgid "Is Pillar"
msgstr "Là nội dung trụ cột"

#: includes/modules/schema/shortcode/event.php:79
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Performer"
msgstr "Người biểu diễn"

#: includes/admin/class-post-columns.php:242
msgid "Not Set"
msgstr "Chưa đặt"

#: includes/modules/instant-indexing/views/history.php:15
msgid "Time"
msgstr "Thời gian"

#: includes/settings/titles/post-types.php:327
#: includes/settings/titles/post-types.php:365
msgid "Bulk Editing"
msgstr "Chỉnh sửa hàng loạt"

#. translators: post type name
#: includes/settings/titles/post-types.php:116
msgid "Description for %s archive pages."
msgstr "Mô tả cho các trang lưu trữ %s."

#. translators: numeric app ID link
#: includes/settings/titles/social.php:49
msgid "Enter %s. Alternatively, you can enter a user ID above."
msgstr "Nhập %s. Ngoài ra, bạn có thể nhập ID người dùng ở trên."

#. translators: numeric user ID link
#: includes/settings/titles/social.php:39
msgid "Enter %s. Use a comma to separate multiple IDs. Alternatively, you can enter an app ID below."
msgstr "Nhập %s. Sử dụng dấu phẩy để phân tách nhiều ID. Ngoài ra, bạn có thể nhập ID ứng dụng bên dưới."

#: includes/settings/titles/social.php:37
msgid "Facebook Admin"
msgstr "Quản trị viên Facebook"

#: includes/settings/titles/social.php:47
msgid "Facebook App"
msgstr "Ứng dụng Facebook"

#: includes/settings/titles/social.php:27
msgid "Facebook Authorship"
msgstr "Quyền tác giả trên Facebook"

#: includes/settings/titles/social.php:17
#: includes/settings/titles/social.php:68
msgid "Facebook Page URL"
msgstr "URL trang Facebook"

#: includes/settings/titles/social.php:57
msgid "Facebook Secret"
msgstr "Bí mật Facebook"

#: includes/settings/titles/post-types.php:284
msgid "Link Suggestion Titles"
msgstr "Tiêu đề đề xuất liên kết"

#: includes/settings/titles/post-types.php:302
msgid "Primary Taxonomy"
msgstr "Phân loại chính"

#: includes/settings/titles/post-types.php:332
#: includes/settings/titles/post-types.php:370
msgid "Read Only"
msgstr "Chỉ đọc"

#. translators: post type name
#: includes/settings/titles/post-types.php:101
msgid "Title for %s archive pages."
msgstr "Tiêu đề cho các trang lưu trữ %s."

#: includes/settings/titles/post-types.php:287
msgid "Titles"
msgstr "Tiêu đề"

#: includes/settings/titles/social.php:78
msgid "Twitter Username"
msgstr "Tên người dùng Twitter"

#. translators: PHP Version
#: rank-math.php:207
msgid "Rank Math requires PHP version %s or above. Please update PHP to run this plugin."
msgstr "Rank Math yêu cầu PHP phiên bản %s trở lên. Vui lòng cập nhật PHP để chạy plugin này."

#: includes/traits/class-ajax.php:44
msgid "Error: Nonce verification failed"
msgstr "Lỗi: Xác minh Nonce không thành công"

#: includes/settings/titles/taxonomies.php:136
msgid "Remove Snippet Data"
msgstr "Xóa dữ liệu đoạn trích"

#: includes/settings/titles/taxonomies.php:126
msgid "Add the SEO Controls for the term editor screen to customize SEO options for individual terms in this taxonomy."
msgstr "Thêm Bộ điều khiển SEO cho màn hình trình chỉnh sửa thuật ngữ để tùy chỉnh các tùy chọn SEO cho các thuật ngữ riêng lẻ trong phân loại này."

#. translators: taxonomy name
#: includes/settings/titles/taxonomies.php:72
msgid "Select custom robots meta, such as <code>nofollow</code>, <code>noarchive</code>, etc. for %s archive pages. Otherwise the default meta will be used, as set in the Global Meta tab."
msgstr "Chọn meta robot tùy chỉnh, chẳng hạn như <code>nofollow</code>, <code>noarchive</code>, v.v. cho các trang lưu trữ %s. Nếu không, meta mặc định sẽ được sử dụng, như được đặt trong tab Meta chung."

#. translators: taxonomy name
#: includes/settings/titles/taxonomies.php:70
#: includes/settings/titles/taxonomies.php:87
msgid "%s Archives Robots Meta"
msgstr "Meta Robot kho lưu trữ %s"

#. translators: taxonomy name
#: includes/settings/titles/taxonomies.php:53
msgid "Description for %s archives"
msgstr "Mô tả cho kho lưu trữ %s"

#. translators: taxonomy name
#: includes/settings/titles/taxonomies.php:51
msgid "%s Archive Descriptions"
msgstr "Mô tả kho lưu trữ %s"

#. translators: taxonomy name
#: includes/settings/titles/taxonomies.php:38
msgid "Title tag for %s archives"
msgstr "Thẻ tiêu đề cho kho lưu trữ %s"

#. translators: taxonomy name
#: includes/settings/titles/taxonomies.php:36
msgid "%s Archive Titles"
msgstr "Tiêu đề kho lưu trữ %s"

#. translators: Learn more link
#: includes/settings/titles/social.php:59
msgid "Enter alphanumeric secret ID. %s."
msgstr "Nhập ID bí mật chữ và số. %s."

#: includes/settings/titles/social.php:28
msgid "Insert personal Facebook profile URL to show Facebook Authorship when your articles are being shared on Facebook. eg:"
msgstr "Chèn URL hồ sơ Facebook cá nhân để hiển thị quyền tác giả trên Facebook khi bài viết của bạn được chia sẻ trên Facebook. ví dụ:"

#: includes/settings/titles/social.php:18
#: includes/settings/titles/social.php:69
msgid "Enter your complete Facebook page URL here. eg:"
msgstr "Nhập URL trang Facebook đầy đủ của bạn tại đây. ví dụ:"

#. translators: post type name
#: includes/settings/titles/post-types.php:231
msgid "Select custom robots meta, such as <code>nofollow</code>, <code>noarchive</code>, etc. for single %s pages. Otherwise the default meta will be used, as set in the Global Meta tab."
msgstr "Chọn meta robot tùy chỉnh, chẳng hạn như <code>nofollow</code>, <code>noarchive</code>, v.v. cho các trang %s đơn lẻ. Nếu không, meta mặc định sẽ được sử dụng, như được đặt trong tab Meta chung."

#. translators: post type name
#: includes/settings/titles/post-types.php:229
#: includes/settings/titles/post-types.php:246
msgid "%s Robots Meta"
msgstr "Meta Robot %s"

#. translators: post type name
#: includes/settings/titles/post-types.php:114
msgid "%s Archive Description"
msgstr "Mô tả kho lưu trữ %s"

#. translators: post type name
#: includes/settings/titles/post-types.php:99
msgid "%s Archive Title"
msgstr "Tiêu đề kho lưu trữ %s"

#. translators: post type name
#: includes/settings/titles/post-types.php:80
msgid "Single %s Description"
msgstr "Mô tả %s đơn lẻ"

#. translators: post type name
#: includes/settings/titles/post-types.php:65
msgid "Single %s Title"
msgstr "Tiêu đề %s đơn lẻ"

#: includes/settings/titles/misc.php:152
msgid "Noindex Password Protected Pages"
msgstr "Không lập chỉ mục các trang được bảo vệ bằng mật khẩu"

#: includes/settings/titles/misc.php:131
msgid "Prevent all paginated pages from getting indexed by search engines."
msgstr "Ngăn tất cả các trang được phân trang khỏi bị lập chỉ mục bởi các công cụ tìm kiếm."

#: includes/settings/titles/misc.php:120
msgid "Prevent search results pages from getting indexed by search engines. Search results could be considered to be thin content and prone to duplicate content issues."
msgstr "Ngăn các trang kết quả tìm kiếm khỏi bị lập chỉ mục bởi các công cụ tìm kiếm. Kết quả tìm kiếm có thể được coi là nội dung mỏng và dễ gặp phải các vấn đề nội dung trùng lặp."

#: includes/settings/titles/misc.php:119
msgid "Noindex Search Results"
msgstr "Không lập chỉ mục kết quả tìm kiếm"

#. translators: taxonomy name
#: includes/settings/titles/taxonomies.php:138
msgid "Remove schema data from %s."
msgstr "Xóa dữ liệu schema khỏi %s."

#: includes/settings/titles/social.php:79
msgid "Enter the Twitter username of the author to add <code>twitter:creator</code> tag to posts. eg: <code>RankMathSEO</code>"
msgstr "Nhập tên người dùng Twitter của tác giả để thêm thẻ <code>twitter:creator</code> vào bài viết. ví dụ: <code>RankMathSEO</code>"

#: includes/settings/titles/post-types.php:328
#: includes/settings/titles/post-types.php:366
msgid "Add bulk editing columns to the post listing screen."
msgstr "Thêm cột chỉnh sửa hàng loạt vào màn hình danh sách bài viết."

#. translators: post type name
#: includes/settings/titles/post-types.php:82
msgid "Default description for single %s pages. This can be changed on a per-post basis on the post editor screen."
msgstr "Mô tả mặc định cho các trang %s đơn lẻ. Điều này có thể được thay đổi trên cơ sở mỗi bài viết trên màn hình trình chỉnh sửa bài viết."

#. translators: post type name
#: includes/settings/titles/post-types.php:67
msgid "Default title tag for single %s pages. This can be changed on a per-post basis on the post editor screen."
msgstr "Thẻ tiêu đề mặc định cho các trang %s đơn lẻ. Điều này có thể được thay đổi trên cơ sở mỗi bài viết trên màn hình chỉnh sửa bài viết."

#: includes/settings/titles/misc.php:153
msgid "Prevent password protected pages & posts from getting indexed by search engines."
msgstr "Ngăn các trang và bài viết được bảo vệ bằng mật khẩu khỏi bị lập chỉ mục bởi các công cụ tìm kiếm."

#: includes/settings/titles/post-types.php:285
msgid "Use the Focus Keyword as the default text for the links instead of the post titles."
msgstr "Sử dụng từ khoá chính làm văn bản mặc định cho các liên kết thay vì tiêu đề bài viết."

#: includes/settings/titles/post-types.php:355
msgid "Add SEO controls for the editor screen to customize SEO options for posts in this post type."
msgstr "Thêm bộ điều khiển SEO cho màn hình trình chỉnh sửa để tùy chỉnh các tùy chọn SEO cho bài viết trong loại nội dung này."

#: includes/settings/titles/post-types.php:274
msgid "Enable Link Suggestions meta box for this post type, along with the Pillar Content feature."
msgstr "Bật hộp meta đề xuất liên kết cho loại nội dung này, cùng với tính năng nội dung cốt lõi."

#: includes/settings/general/breadcrumbs.php:119
msgid "404 label"
msgstr "Nhãn 404"

#: includes/admin/class-setup-wizard.php:146
msgid "404 + Redirection"
msgstr "404 + Chuyển hướng"

#: includes/helpers/class-choices.php:477 assets/admin/js/rank-math-app.js:1
#: includes/modules/redirections/assets/js/redirections.js:1
msgid "301 Permanent Move"
msgstr "301 Di chuyển vĩnh viễn"

#: includes/helpers/class-choices.php:479 assets/admin/js/rank-math-app.js:1
#: includes/modules/redirections/assets/js/redirections.js:1
msgid "307 Temporary Redirect"
msgstr "307 Chuyển hướng tạm thời"

#: includes/modules/404-monitor/class-table.php:228
msgid "Access Time"
msgstr "Thời gian truy cập"

#: includes/modules/local-seo/views/titles-options.php:208
msgid "Add number"
msgstr "Thêm số"

#: includes/modules/local-seo/views/titles-options.php:166
msgid "Add time"
msgstr "Thêm thời gian"

#: includes/admin/class-setup-wizard.php:202
msgid "Advanced Options"
msgstr "Tùy chọn nâng cao"

#: includes/modules/schema/shortcode/book.php:27
#: includes/modules/schema/shortcode/book.php:38
msgid "Author"
msgstr "Tác giả"

#: includes/admin/wizard/views/navigation.php:17 rank-math.php:488
msgid "Getting Started"
msgstr "Bắt đầu"

#: includes/modules/local-seo/views/titles-options.php:24
#: includes/settings/titles/local.php:18
msgid "Organization"
msgstr "Tổ chức"

#: includes/admin/views/dashboard-help.php:84
msgid "Product Support"
msgstr "Hỗ trợ sản phẩm"

#: includes/admin/class-admin-helper.php:355 assets/admin/js/components.js:1
msgid "Share"
msgstr "Chia sẻ"

#: includes/admin/views/import-export/backup-panel.php:32
#: includes/admin/views/import-export/backup-panel.php:42
#: includes/modules/redirections/class-table.php:232
#: includes/modules/redirections/class-table.php:309
msgid "Restore"
msgstr "Khôi phục"

#: includes/admin/views/import-export/import-export-panel.php:61
#: includes/admin/wizard/class-monitor-redirection.php:76
#: includes/admin/wizard/class-monitor-redirection.php:84
#: includes/module/class-manager.php:125
#: includes/modules/redirections/class-admin.php:154
#: includes/modules/redirections/class-admin.php:219
#: includes/modules/redirections/class-redirections.php:67
#: includes/modules/redirections/class-redirections.php:130
#: includes/modules/role-manager/class-capability-manager.php:61
msgid "Redirections"
msgstr "Chuyển hướng"

#: includes/modules/schema/shortcode/softwareapplication.php:34
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Operating System"
msgstr "Hệ điều hành"

#: includes/admin/wizard/class-sitemap.php:76
msgid "Include Images"
msgstr "Bao gồm hình ảnh"

#: includes/admin/wizard/class-schema-markup.php:80
#: includes/settings/titles/post-types.php:212
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Article Type"
msgstr "Loại bài viết"

#: includes/admin/wizard/class-schema-markup.php:162
#: includes/helpers/class-choices.php:442
#: includes/replace-variables/class-advanced-variables.php:127
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Product"
msgstr "Sản phẩm"

#: includes/admin/wizard/class-monitor-redirection.php:76
msgid "Learn more about Redirections."
msgstr "Tìm hiểu thêm về Chuyển hướng."

#: includes/admin/class-setup-wizard.php:134
msgid "Ready"
msgstr "Sẵn sàng"

#: includes/admin/class-setup-wizard.php:129
msgid "Optimization"
msgstr "Tối ưu hóa"

#: includes/admin/class-setup-wizard.php:106
#: includes/modules/content-ai/assets/js/content-ai-page.js:1
#: includes/modules/content-ai/assets/js/content-ai.js:1
msgid "Requirements"
msgstr "Yêu cầu"

#: includes/admin/wizard/views/ready.php:17
msgid "Your site is ready!"
msgstr "Trang web của bạn đã sẵn sàng!"

#: includes/admin/wizard/class-import.php:50
msgid "Continue"
msgstr "Tiếp tục"

#. translators: Link to How to Setup Google Search Console KB article
#: includes/admin/wizard/class-search-console.php:42
msgid "Read more about it here."
msgstr "Đọc thêm về nó ở đây."

#. translators: Search query term
#. translators: placeholder
#: includes/class-installer.php:345
#: includes/settings/general/breadcrumbs.php:110
msgid "Results for %s"
msgstr "Kết quả cho %s"

#. translators: Archive title
#. translators: placeholder
#: includes/class-installer.php:343
#: includes/settings/general/breadcrumbs.php:98
msgid "Archives for %s"
msgstr "Lưu trữ cho %s"

#: includes/admin/class-admin-bar-menu.php:401
msgid "Facebook Debugger"
msgstr "Trình gỡ lỗi Facebook"

#: includes/admin/class-admin-bar-menu.php:383
msgid "Google PageSpeed"
msgstr "Google PageSpeed"

#: includes/module/class-module.php:96
#: includes/modules/404-monitor/class-admin.php:219
#: includes/modules/instant-indexing/class-instant-indexing.php:220
#: includes/modules/redirections/class-admin.php:315 rank-math.php:430
#: assets/admin/js/rank-math-app.js:1
#: includes/modules/schema/blocks/toc/assets/js/index.js:1
#: includes/modules/schema/blocks/toc/assets/src/inspectControls.js:20
msgid "Settings"
msgstr "Cài đặt"

#: includes/modules/schema/shortcode/book.php:40
#: includes/replace-variables/class-post-variables.php:137
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Date Published"
msgstr "Ngày xuất bản"

#: includes/admin/class-admin-bar-menu.php:316
msgid "Mark this page"
msgstr "Đánh dấu trang này"

#: includes/admin/class-admin-bar-menu.php:301
msgid "SEO Settings for Search Page"
msgstr "Cài đặt SEO cho Trang Tìm kiếm"

#. translators: Link to kb article
#. translators: Link to social setting KB article
#. translators: Link to How to Optimization KB article
#: includes/admin/class-option-center.php:54
#: includes/admin/class-option-center.php:62
#: includes/admin/class-option-center.php:154
#: includes/admin/wizard/class-optimization.php:37
#: includes/modules/404-monitor/class-admin.php:224
#: includes/modules/redirections/class-admin.php:310
#: assets/admin/js/post-list.js:1
#: includes/modules/analytics/assets/js/admin-bar.js:1
#: includes/modules/content-ai/assets/js/content-ai-media.js:1
#: includes/modules/content-ai/assets/js/content-ai-page.js:1
#: includes/modules/content-ai/assets/js/content-ai.js:1
msgid "Learn More"
msgstr "Tìm hiểu thêm"

#: includes/frontend/class-shortcodes.php:195
msgid "Hours:"
msgstr "Giờ:"

#: includes/helpers/class-choices.php:441 includes/helpers/class-schema.php:110
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Music"
msgstr "Âm nhạc"

#: includes/helpers/class-choices.php:445 includes/helpers/class-schema.php:98
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Video"
msgstr "Video"

#: includes/helpers/class-choices.php:605
msgid "Customer Service"
msgstr "Dịch vụ khách hàng"

#: includes/helpers/class-choices.php:607
msgid "Billing Support"
msgstr "Hỗ trợ thanh toán"

#: includes/helpers/class-choices.php:609
msgid "Sales"
msgstr "Bán hàng"

#: includes/helpers/class-choices.php:612
msgid "Emergency"
msgstr "Khẩn cấp"

#: includes/module/class-manager.php:306
msgid "AMP plugin"
msgstr "Plugin AMP"

#: includes/replace-variables/class-advanced-variables.php:67
msgid "Custom field value."
msgstr "Giá trị trường tùy chỉnh."

#: includes/replace-variables/class-advanced-variables.php:69
msgid "Custom field value"
msgstr "Giá trị trường tùy chỉnh"

#: includes/modules/sitemap/settings/html-sitemap.php:31
#: includes/modules/sitemap/settings/html-sitemap.php:76
#: includes/replace-variables/class-advanced-variables.php:86
msgid "Page"
msgstr "Trang"

#: includes/replace-variables/class-advanced-variables.php:97
msgid "Page Number"
msgstr "Số trang"

#: includes/replace-variables/class-advanced-variables.php:98
msgid "Current page number"
msgstr "Số trang hiện tại"

#: includes/replace-variables/class-advanced-variables.php:109
msgid "Max pages number"
msgstr "Số trang tối đa"

#: includes/replace-variables/class-author-variables.php:46
msgid "Author ID"
msgstr "ID tác giả"

#: includes/replace-variables/class-author-variables.php:79
msgid "Author Description"
msgstr "Mô tả tác giả"

#: includes/replace-variables/class-basic-variables.php:72
msgid "example search"
msgstr "tìm kiếm ví dụ"

#: includes/replace-variables/class-basic-variables.php:115
msgid "Title of the site"
msgstr "Tiêu đề của trang web"

#: includes/replace-variables/class-basic-variables.php:126
msgid "Description of the site"
msgstr "Mô tả của trang web"

#: includes/replace-variables/class-basic-variables.php:152
msgid "Current Day"
msgstr "Ngày hiện tại"

#: includes/replace-variables/class-post-variables.php:173
msgid "Modified date with custom formatting pattern."
msgstr "Ngày sửa đổi với mẫu định dạng tùy chỉnh."

#: includes/replace-variables/class-post-variables.php:265
msgid "Tags (advanced)"
msgstr "Thẻ (nâng cao)"

#: includes/replace-variables/class-term-variables.php:49
msgid "Current term description"
msgstr "Mô tả thuật ngữ hiện tại"

#: includes/admin/importers/abstract-importer.php:150
msgid "Import data like category, tag, and CPT meta data from SEO."
msgstr "Nhập dữ liệu như danh mục, thẻ và dữ liệu meta CPT từ SEO."

#: includes/admin/views/dashboard-help.php:96
msgid "Understand all the capabilities of Rank Math"
msgstr "Hiểu tất cả các khả năng của Rank Math"

#: includes/admin/views/dashboard-help.php:105
msgid "Ticket Support"
msgstr "Hỗ trợ vé"

#. Translators: placeholder is the KB link.
#: includes/admin/wizard/class-sitemap.php:39
#: includes/admin/wizard/views/google-connect.php:37
#: includes/admin/wizard/views/rank-math-connect.php:48
#: includes/admin/wizard/views/search-console-ui.php:160
#: includes/admin/wizard/views/search-console-ui.php:389
#: assets/admin/js/components.js:1
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
msgid "Learn more."
msgstr "Tìm hiểu thêm."

#: includes/modules/404-monitor/class-table.php:38
msgid "The 404 error log is empty."
msgstr "Nhật ký lỗi 404 trống."

#: includes/modules/analytics/class-analytics-common.php:306
#: includes/modules/analytics/views/email-reports/sections/summary.php:20
msgid "Total Impressions"
msgstr "Tổng số lần hiển thị"

#: includes/modules/analytics/class-analytics-common.php:312
#: includes/modules/analytics/views/email-reports/sections/summary.php:34
msgid "Total Clicks"
msgstr "Tổng số lần nhấp"

#: includes/modules/analytics/class-analytics-common.php:318
#: includes/modules/analytics/views/email-reports/sections/summary.php:50
#: includes/modules/analytics/assets/js/stats.js:1
msgid "Total Keywords"
msgstr "Tổng số từ khóa"

#: includes/modules/analytics/class-analytics-common.php:324
#: includes/modules/analytics/views/email-reports/sections/summary.php:64
#: includes/modules/analytics/assets/js/admin-bar.js:1
msgid "Average Position"
msgstr "Vị trí trung bình"

#: includes/modules/image-seo/class-admin.php:45
#: includes/modules/sitemap/sitemap-xsl.php:309
#: assets/admin/js/rank-math-app.js:1
msgid "Images"
msgstr "Hình ảnh"

#: includes/modules/image-seo/options.php:49
msgid "Title attribute format"
msgstr "Định dạng thuộc tính tiêu đề"

#: includes/modules/links/class-links.php:108
#: includes/modules/analytics/assets/js/stats.js:1
msgid "Internal Links"
msgstr "Liên kết nội bộ"

#: includes/rest/class-admin.php:303
msgid "Please add at least one valid source URL."
msgstr "Vui lòng thêm ít nhất một URL nguồn hợp lệ."

#: includes/modules/redirections/class-import-export.php:95
msgid "Export to .htaccess"
msgstr "Xuất sang .htaccess"

#: includes/modules/redirections/class-import-export.php:96
msgid "Export to Nginx config file"
msgstr "Xuất sang tệp cấu hình Nginx"

#. translators: source pattern
#: includes/modules/redirections/class-redirection.php:392
msgid "Invalid regex pattern: %s"
msgstr "Mẫu regex không hợp lệ: %s"

#: includes/modules/redirections/class-redirections.php:163
msgid "Redirect the current URL"
msgstr "Chuyển hướng URL hiện tại"

#: includes/modules/redirections/class-table.php:37
msgid "No redirections found in Trash."
msgstr "Không tìm thấy chuyển hướng nào trong Thùng rác."

#: includes/modules/redirections/class-table.php:170
msgid "Hide details"
msgstr "Ẩn chi tiết"

#: includes/modules/redirections/class-table.php:251
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Edit"
msgstr "Chỉnh sửa"

#: includes/modules/redirections/class-table.php:275
msgid "From"
msgstr "Từ"

#: includes/modules/redirections/class-table.php:341
msgid "Inactive"
msgstr "Không hoạt động"

#: includes/modules/robots-txt/class-robots-txt.php:67
msgid "Edit robots.txt"
msgstr "Chỉnh sửa robots.txt"

#: includes/modules/role-manager/class-capability-manager.php:65
msgid "On-Page Analysis"
msgstr "Phân tích trên trang"

#: includes/modules/local-seo/views/titles-options.php:163
#: includes/modules/schema/class-snippet-shortcode.php:246
msgid "Opening Hours"
msgstr "Giờ mở cửa"

#: includes/modules/schema/class-snippet-shortcode.php:261
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Opening Time"
msgstr "Thời gian mở cửa"

#: includes/modules/schema/class-snippet-shortcode.php:262
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Closing Time"
msgstr "Thời gian đóng cửa"

#: includes/modules/seo-analysis/class-result.php:177
#: includes/modules/content-ai/assets/js/content-ai-media.js:1
#: includes/modules/content-ai/assets/js/content-ai-page.js:1
#: includes/modules/content-ai/assets/js/content-ai.js:1
msgid "Failed"
msgstr "Thất bại"

#: includes/modules/seo-analysis/class-result.php:178
msgid "Warning"
msgstr "Cảnh báo"

#: includes/modules/seo-analysis/class-result.php:179
msgid "Info"
msgstr "Thông tin"

#: includes/modules/seo-analysis/seo-analysis-tests.php:302
msgid "Google Search Console has been linked."
msgstr "Google Search Console đã được liên kết."

#: includes/modules/seo-analysis/seo-analysis-tests.php:302
msgid "You have not linked Google Search Console yet."
msgstr "Bạn chưa liên kết Google Search Console."

#: includes/modules/seo-analysis/seo-analysis-tests.php:502
msgid "Your site has one or more sitemaps."
msgstr "Trang web của bạn có một hoặc nhiều sơ đồ trang web."

#: includes/modules/sitemap/class-admin.php:192
msgid "Taxonomies:"
msgstr "Phân loại:"

#: includes/modules/schema/shortcode/book.php:36
#: includes/modules/sitemap/sitemap-xsl.php:170
#: includes/opengraph/class-slack.php:360
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Name"
msgstr "Tên"

#: includes/modules/woocommerce/class-wc-vars.php:35
msgid "Product's price of the current product"
msgstr "Giá sản phẩm của sản phẩm hiện tại"

#: includes/settings/general/breadcrumbs.php:63
msgid "Homepage label"
msgstr "Nhãn trang chủ"

#: includes/settings/general/breadcrumbs.php:96
msgid "Format the label used for archive pages."
msgstr "Định dạng nhãn được sử dụng cho các trang lưu trữ."

#: includes/settings/general/breadcrumbs.php:108
msgid "Format the label used for search results pages."
msgstr "Định dạng nhãn được sử dụng cho các trang kết quả tìm kiếm."

#: includes/settings/general/breadcrumbs.php:142
msgid "If category is a child category, show all ancestor categories."
msgstr "Nếu danh mục là danh mục con, hãy hiển thị tất cả các danh mục tổ tiên."

#: includes/settings/general/htaccess.php:22
msgid ".htaccess file not found."
msgstr "Không tìm thấy tệp .htaccess."

#: includes/settings/general/links.php:81
msgid "Nofollow Domains"
msgstr "Tên miền Nofollow"

#: includes/settings/general/rss-vars-table.php:14
msgid "Available variables"
msgstr "Các biến có sẵn"

#: includes/settings/general/webmaster.php:17
msgid "Google Search Console"
msgstr "Google Search Console"

#: includes/settings/general/webmaster.php:53
msgid "Yandex Verification ID"
msgstr "ID xác minh Yandex"

#. translators: Yandex webmaster link
#: includes/settings/general/webmaster.php:55
msgid "Yandex.Webmaster Page"
msgstr "Trang Yandex.Webmaster"

#: includes/settings/general/webmaster.php:66
msgid "Pinterest Verification ID"
msgstr "ID xác minh Pinterest"

#. translators: Pinterest webmaster link
#: includes/settings/general/webmaster.php:68
msgid "Pinterest Account"
msgstr "Tài khoản Pinterest"

#: includes/settings/titles/author.php:33
msgid "Author Base"
msgstr "Cơ sở tác giả"

#: includes/settings/titles/homepage.php:39
msgid "Homepage title tag."
msgstr "Thẻ tiêu đề trang chủ."

#: includes/settings/titles/homepage.php:107
msgid "Homepage Title for Facebook"
msgstr "Tiêu đề trang chủ cho Facebook"

#: includes/settings/titles/homepage.php:108
msgid "Title of your site when shared on Facebook, Twitter and other social networks."
msgstr "Tiêu đề trang web của bạn khi được chia sẻ trên Facebook, Twitter và các mạng xã hội khác."

#: includes/settings/titles/homepage.php:117
msgid "Homepage Description for Facebook"
msgstr "Mô tả trang chủ cho Facebook"

#: includes/settings/titles/homepage.php:118
msgid "Description of your site when shared on Facebook, Twitter and other social networks."
msgstr "Mô tả trang web của bạn khi được chia sẻ trên Facebook, Twitter và các mạng xã hội khác."

#: includes/settings/titles/homepage.php:127
msgid "Homepage Thumbnail for Facebook"
msgstr "Hình thu nhỏ trang chủ cho Facebook"

#: includes/modules/local-seo/views/titles-options.php:74
#: includes/settings/titles/local.php:58
msgid "Logo"
msgstr "Logo"

#: includes/settings/titles/misc.php:107
msgid "Title tag on search results page."
msgstr "Thẻ tiêu đề trên trang kết quả tìm kiếm."

#: includes/settings/titles/post-types.php:179
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Headline"
msgstr "Tiêu đề"

#: includes/admin/views/import-export/import-export-panel.php:58
msgid "Titles &amp; Metas"
msgstr "Tiêu đề &amp; Metas"

#. translators: php version
#: includes/admin/wizard/views/compatibility.php:55
msgid "Your PHP Version: %s"
msgstr "Phiên bản PHP của bạn: %s"

#: includes/modules/404-monitor/views/help-tab-screen-content.php:18
#: includes/modules/redirections/views/help-tab-screen-content.php:18
msgid "You can search items using the search form at the top."
msgstr "Bạn có thể tìm kiếm các mục bằng cách sử dụng biểu mẫu tìm kiếm ở trên cùng."

#: includes/modules/404-monitor/views/options.php:29
msgid "Mode"
msgstr "Chế độ"

#: includes/modules/404-monitor/views/options.php:32
msgid "Simple"
msgstr "Đơn giản"

#: includes/modules/local-seo/views/titles-options.php:103
msgid "Phone"
msgstr "Điện thoại"

#: includes/modules/local-seo/views/titles-options.php:148
msgid "Opening Hours Format"
msgstr "Định dạng giờ mở cửa"

#: includes/modules/local-seo/views/titles-options.php:205
#: includes/modules/schema/shortcode/restaurant.php:34
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Phone Number"
msgstr "Số điện thoại"

#: includes/modules/local-seo/views/titles-options.php:330
#: includes/modules/schema/shortcode/restaurant.php:27
msgid "Geo Coordinates"
msgstr "Tọa độ địa lý"

#: includes/modules/redirections/views/debugging.php:40
msgid "Continue redirecting"
msgstr "Tiếp tục chuyển hướng"

#: includes/modules/redirections/views/debugging.php:46
msgid "Manage This Redirection"
msgstr "Quản lý chuyển hướng này"

#: includes/modules/redirections/views/options.php:27
msgid "Fallback Behavior"
msgstr "Hành vi dự phòng"

#: includes/modules/redirections/views/options.php:30
msgid "Default 404"
msgstr "Mặc định 404"

#: includes/modules/schema/shortcode/book.php:37
msgid "Url"
msgstr "Url"

#: includes/modules/schema/shortcode/course.php:20
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Course Provider"
msgstr "Nhà cung cấp khóa học"

#: includes/modules/schema/shortcode/event.php:57
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Venue URL"
msgstr "URL địa điểm"

#: includes/modules/schema/shortcode/event.php:116
msgid "Ticket URL"
msgstr "URL vé"

#: includes/modules/schema/shortcode/jobposting.php:41
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Date Posted"
msgstr "Ngày đăng"

#: includes/modules/schema/shortcode/product.php:27
msgid "Product Brand"
msgstr "Thương hiệu sản phẩm"

#: includes/modules/schema/shortcode/product.php:41
msgid "Product Price"
msgstr "Giá sản phẩm"

#: includes/modules/schema/shortcode/recipe.php:27
#: includes/modules/content-ai/assets/js/content-ai-page.js:1
#: includes/modules/content-ai/assets/js/content-ai.js:1
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Cuisine"
msgstr "Ẩm thực"

#: includes/modules/schema/shortcode/recipe.php:55
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Preparation Time"
msgstr "Thời gian chuẩn bị"

#: includes/modules/schema/shortcode/recipe.php:69
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Total Time"
msgstr "Tổng thời gian"

#: includes/modules/seo-analysis/views/form.php:22
msgid "Selected page: %s"
msgstr "Trang đã chọn: %s"

#: includes/modules/seo-analysis/views/form.php:25
msgid "Change URL"
msgstr "Thay đổi URL"

#: includes/modules/seo-analysis/views/form.php:40
msgid "Complete"
msgstr "Hoàn thành"

#: includes/modules/seo-analysis/class-seo-analyzer.php:152
#: includes/modules/seo-analysis/views/graphs.php:49
msgid "Warnings"
msgstr "Cảnh báo"

#: includes/modules/sitemap/settings/general.php:31
msgid "Max number of links on each sitemap page."
msgstr "Số lượng liên kết tối đa trên mỗi trang sơ đồ trang web."

#: includes/modules/local-seo/views/titles-options.php:279
#: includes/modules/local-seo/views/titles-options.php:297
#: includes/modules/sitemap/settings/html-sitemap.php:58
msgid "Select Page"
msgstr "Chọn trang"

#: includes/modules/schema/shortcode/event.php:130
#: includes/modules/schema/shortcode/service.php:34
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Currency"
msgstr "Tiền tệ"

#: includes/modules/schema/shortcode/person.php:34
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Gender"
msgstr "Giới tính"

#: includes/modules/redirections/class-redirections.php:161
msgid "&raquo; Redirect this page"
msgstr "»Chuyển hướng trang này"

#: includes/helpers/class-choices.php:481 assets/admin/js/rank-math-app.js:1
#: includes/modules/redirections/assets/js/redirections.js:1
msgid "451 Content Unavailable for Legal Reasons"
msgstr "451 Nội dung không khả dụng vì lý do pháp lý"

#: includes/modules/image-seo/options.php:15
msgid "Add missing ALT attributes"
msgstr "Thêm các thuộc tính ALT bị thiếu"

#: includes/modules/image-seo/options.php:39
msgid "Add missing TITLE attributes"
msgstr "Thêm các thuộc tính TITLE bị thiếu"

#: includes/modules/redirections/class-admin.php:300
msgid "Add New"
msgstr "Thêm mới"

#: includes/modules/local-seo/views/titles-options.php:121
msgid "Address Format"
msgstr "Định dạng địa chỉ"

#: includes/module/class-manager.php:302
msgid "AMP"
msgstr "AMP"

#: includes/modules/seo-analysis/views/form.php:29
msgid "Analysing Page&hellip;"
msgstr "Đang phân tích trang…"

#: includes/modules/seo-analysis/views/form.php:34
msgid "Analysing Website&hellip;"
msgstr "Đang phân tích trang web…"

#: includes/modules/seo-analysis/class-seo-analysis.php:74
msgid "Analyze this Page"
msgstr "Phân tích trang này"

#: includes/settings/general/breadcrumbs.php:95
msgid "Archive Format"
msgstr "Định dạng lưu trữ"

#: includes/settings/titles/author.php:19
msgid "Author Archives"
msgstr "Lưu trữ tác giả"

#: includes/helpers/class-choices.php:613
msgid "Baggage Tracking"
msgstr "Theo dõi hành lý"

#: includes/helpers/class-choices.php:608
msgid "Bill Payment"
msgstr "Thanh toán hóa đơn"

#: includes/helpers/class-choices.php:437
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Book"
msgstr "Sách"

#: includes/replace-variables/class-post-variables.php:212
msgid "Categories (advanced)"
msgstr "Danh mục (nâng cao)"

#: includes/admin/views/import-export/import-export-panel.php:63
msgid "Choose the panels to export."
msgstr "Chọn các bảng để xuất."

#: includes/modules/local-seo/views/titles-options.php:26
#: includes/settings/titles/local.php:20
msgid "Choose whether the site represents a person or an organization."
msgstr "Chọn xem trang web đại diện cho một người hay một tổ chức."

#: includes/admin/views/import-export/plugins-panel.php:77
msgid "Clean"
msgstr "Dọn dẹp"

#: includes/modules/redirections/class-redirections.php:132
#: includes/modules/redirections/class-redirections.php:142
msgid "Create and edit redirections"
msgstr "Tạo và chỉnh sửa chuyển hướng"

#: includes/helpers/class-choices.php:611
msgid "Credit Card Support"
msgstr "Hỗ trợ thẻ tín dụng"

#: includes/replace-variables/class-term-variables.php:37
msgid "Current term name"
msgstr "Tên thuật ngữ hiện tại"

#: includes/replace-variables/class-basic-variables.php:201
msgid "Current Time (advanced)"
msgstr "Giờ hiện tại (nâng cao)"

#: includes/replace-variables/class-advanced-variables.php:66
msgid "Custom Field (advanced)"
msgstr "Trường tùy chỉnh (nâng cao)"

#: includes/modules/redirections/views/options.php:32
msgid "Custom Redirection"
msgstr "Chuyển hướng tùy chỉnh"

#. translators: number of rows
#: includes/modules/analytics/views/options.php:32
msgid "Data Rows: %s"
msgstr "Hàng dữ liệu: %s"

#: includes/settings/titles/misc.php:37
msgid "Date Archive Title"
msgstr "Tiêu đề lưu trữ ngày"

#: includes/replace-variables/class-post-variables.php:149
msgid "Date Modified"
msgstr "Ngày sửa đổi"

#: includes/replace-variables/class-post-variables.php:172
msgid "Date Modified (advanced)"
msgstr "Ngày sửa đổi (nâng cao)"

#: includes/replace-variables/class-post-variables.php:161
msgid "Date Published (advanced)"
msgstr "Ngày xuất bản (nâng cao)"

#: includes/modules/redirections/class-table.php:252
#: includes/modules/redirections/class-table.php:315
#: includes/modules/redirections/assets/js/redirections.js:1
msgid "Deactivate"
msgstr "Hủy kích hoạt"

#: rank-math.php:489
msgid "Documentation"
msgstr "Tài liệu"

#: includes/admin/class-option-center.php:85
#: includes/modules/role-manager/class-members.php:61
msgid "Edit .htaccess"
msgstr "Chỉnh sửa .htaccess"

#: includes/admin/class-admin-bar-menu.php:222
msgid "Edit Homepage SEO Settings"
msgstr "Chỉnh sửa Cài đặt SEO Trang chủ"

#: includes/admin/class-admin-bar-menu.php:303
msgid "Edit SEO settings for the search results page"
msgstr "Chỉnh sửa cài đặt SEO cho trang kết quả tìm kiếm"

#: includes/admin/class-admin-bar-menu.php:273
#: includes/admin/class-admin-bar-menu.php:288
msgid "Edit SEO settings for this archive page"
msgstr "Chỉnh sửa cài đặt SEO cho trang lưu trữ này"

#: includes/settings/titles/author.php:23 includes/settings/titles/misc.php:27
#: includes/settings/titles/post-types.php:331
#: includes/settings/titles/post-types.php:369
msgid "Enabled"
msgstr "Đã bật"

#: includes/modules/schema/shortcode/event.php:44
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Event Status"
msgstr "Trạng thái sự kiện"

#: includes/modules/schema/shortcode/event.php:30
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Event Type"
msgstr "Loại sự kiện"

#: includes/helpers/class-choices.php:494
#: includes/modules/redirections/assets/js/redirections.js:1
msgid "Exact"
msgstr "Chính xác"

#: includes/replace-variables/class-post-variables.php:268
msgid "Example Tag 1 | Example Tag 2"
msgstr "Thẻ ví dụ 1 | Thẻ ví dụ 2"

#: includes/replace-variables/class-post-variables.php:256
msgid "Example Tag 1, Example Tag 2"
msgstr "Thẻ ví dụ 1, Thẻ ví dụ 2"

#: includes/modules/sitemap/settings/authors.php:64
msgid "Exclude Users"
msgstr "Loại trừ người dùng"

#: includes/admin/views/import-export/import-export-panel.php:31
msgid "Export Settings"
msgstr "Xuất Cài đặt"

#: includes/admin/class-admin-bar-menu.php:374
msgid "External Tools"
msgstr "Công cụ bên ngoài"

#: includes/admin/class-admin-bar-menu.php:403
msgid "Facebook Sharing Debugger"
msgstr "Trình gỡ lỗi chia sẻ Facebook"

#: includes/replace-variables/class-basic-variables.php:92
msgid "File Name"
msgstr "Tên tệp"

#: includes/replace-variables/class-basic-variables.php:93
msgid "File Name of the attachment"
msgstr "Tên tệp của tệp đính kèm"

#: includes/helpers/class-choices.php:49
msgid "GIF icon"
msgstr "Biểu tượng GIF"

#: includes/settings/general/breadcrumbs.php:152
msgid "Hide Taxonomy Name"
msgstr "Ẩn tên phân loại"

#: includes/modules/404-monitor/class-table.php:227
#: includes/modules/redirections/class-table.php:278
msgid "Hits"
msgstr "Lượt truy cập"

#: includes/admin/class-option-center.php:158
msgid "Homepage"
msgstr "Trang chủ"

#: includes/settings/titles/homepage.php:51
msgid "Homepage Meta Description"
msgstr "Mô tả Meta Trang chủ"

#: includes/settings/titles/homepage.php:38
msgid "Homepage Title"
msgstr "Tiêu đề trang chủ"

#: includes/admin/views/dashboard-help.php:71
msgid "How to Make Your Posts Pass All the Tests"
msgstr "Cách làm cho bài viết của bạn vượt qua tất cả các bài kiểm tra"

#: includes/settings/general/htaccess.php:45
msgid "I understand the risks and I want to edit the file"
msgstr "Tôi hiểu những rủi ro và tôi muốn chỉnh sửa tệp"

#: includes/modules/sitemap/settings/post-types.php:61
msgid "Image Custom Fields"
msgstr "Trường tùy chỉnh hình ảnh"

#: includes/admin/importers/abstract-importer.php:152
#: includes/admin/importers/class-aioseo.php:77
msgid "Import Redirections"
msgstr "Nhập chuyển hướng"

#: includes/admin/views/import-export/import-export-panel.php:44
msgid "Import settings by locating settings file and clicking \"Import settings\"."
msgstr "Nhập cài đặt bằng cách định vị tệp cài đặt và nhấp vào \"Nhập cài đặt\"."

#. translators: total
#: includes/admin/importers/class-status.php:117
msgid "Imported %s redirections."
msgstr "Đã nhập %s chuyển hướng."

#: includes/modules/links/class-links.php:118
#: includes/modules/analytics/assets/js/stats.js:1
msgid "Incoming Links"
msgstr "Liên kết đến"

#: includes/modules/schema/shortcode/book.php:39
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "ISBN"
msgstr "ISBN"

#: includes/helpers/class-choices.php:440 includes/helpers/class-schema.php:102
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Job Posting"
msgstr "Đăng tuyển dụng"

#: includes/modules/schema/shortcode/recipe.php:34
#: includes/modules/analytics/assets/js/stats.js:1
#: includes/modules/content-ai/assets/js/content-ai.js:1
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Keywords"
msgstr "Từ khóa"

#. translators: Link to kb article
#. translators: Link to KB article
#. translators: Redirection page url
#. translators: 1. Link to KB article 2. Link to redirection setting scree
#. translators: Learn more link.
#. translators: %1$s: thing, %2$s: Learn more link.
#: includes/admin/class-option-center.php:61
#: includes/admin/class-option-center.php:68
#: includes/admin/class-option-center.php:74
#: includes/admin/class-option-center.php:87
#: includes/admin/class-option-center.php:141
#: includes/admin/class-option-center.php:147
#: includes/admin/class-option-center.php:162
#: includes/admin/class-option-center.php:169
#: includes/admin/class-option-center.php:175
#: includes/admin/class-option-center.php:213
#: includes/admin/class-option-center.php:214
#: includes/admin/class-option-center.php:215
#: includes/admin/class-option-center.php:216
#: includes/admin/class-option-center.php:266
#: includes/admin/class-option-center.php:267
#: includes/admin/class-option-center.php:268
#: includes/admin/class-option-center.php:269
#: includes/admin/wizard/views/ready.php:45
#: includes/modules/404-monitor/class-admin.php:199
#: includes/modules/analytics/class-analytics.php:501
#: includes/modules/content-ai/class-admin.php:90
#: includes/modules/image-seo/class-admin.php:47
#: includes/modules/instant-indexing/class-instant-indexing.php:214
#: includes/modules/instant-indexing/class-instant-indexing.php:222
#: includes/modules/redirections/class-admin.php:221
#: includes/modules/robots-txt/class-robots-txt.php:69
#: includes/modules/sitemap/class-admin.php:85
#: includes/modules/sitemap/class-admin.php:95
#: includes/modules/sitemap/class-admin.php:104
#: includes/modules/sitemap/class-admin.php:164
#: includes/modules/sitemap/class-admin.php:230
#: includes/modules/woocommerce/class-admin.php:76
#: includes/modules/redirections/assets/js/redirections.js:1
#: includes/modules/role-manager/assets/js/role-manager.js:1
msgid "Learn more"
msgstr "Tìm hiểu thêm"

#: includes/admin/wizard/views/compatibility.php:43
msgid "Less"
msgstr "Ít hơn"

#: includes/admin/wizard/views/your-site.php:22
msgid "Let us know a few things about your site&hellip;"
msgstr "Hãy cho chúng tôi biết một vài điều về trang web của bạn…"

#: includes/modules/role-manager/class-capability-manager.php:60
msgid "Link Builder"
msgstr "Trình tạo liên kết"

#: includes/admin/class-option-center.php:52
#: includes/modules/analytics/assets/js/stats.js:1
#: includes/modules/content-ai/assets/js/content-ai.js:1
msgid "Links"
msgstr "Liên kết"

#: includes/modules/404-monitor/views/options.php:43
msgid "Log Limit"
msgstr "Giới hạn nhật ký"

#: includes/modules/redirections/views/debugging.php:49
msgid "Manage All Redirections"
msgstr "Quản lý tất cả chuyển hướng"

#: includes/modules/redirections/class-redirections.php:140
msgid "Manage Redirections"
msgstr "Quản lý chuyển hướng"

#: includes/replace-variables/class-advanced-variables.php:108
msgid "Max Pages"
msgstr "Số trang tối đa"

#: includes/admin/wizard/views/compatibility.php:42
#: assets/admin/js/rank-math-app.js:1
msgid "More"
msgstr "Hơn"

#: includes/admin/wizard/views/compatibility.php:62
msgid "More information"
msgstr "Thêm thông tin"

#: includes/admin/views/dashboard-help.php:29
msgid "Next steps&hellip;"
msgstr "Bước tiếp theo…"

#: includes/modules/redirections/class-admin.php:286
msgid "No valid action found."
msgstr "Không tìm thấy hành động hợp lệ."

#: includes/settings/general/links.php:70
msgid "Nofollow Image File Links"
msgstr "Liên kết tệp hình ảnh Nofollow"

#: includes/modules/role-manager/class-capability-manager.php:67
msgid "On-Page Advanced Settings"
msgstr "Cài đặt nâng cao trên trang"

#: includes/modules/role-manager/class-capability-manager.php:66
msgid "On-Page General Settings"
msgstr "Cài đặt chung trên trang"

#: includes/admin/class-option-center.php:72
msgid "Others"
msgstr "Khác"

#: includes/frontend/paper/class-error-404.php:26
#: includes/frontend/paper/class-singular.php:128
#: includes/frontend/paper/class-taxonomy.php:31
msgid "Page not found"
msgstr "Không tìm thấy trang"

#: includes/modules/local-seo/views/titles-options.php:21
#: includes/settings/titles/local.php:15
msgid "Person or Company"
msgstr "Cá nhân hoặc Công ty"

#: includes/admin/views/import-export/import-export-panel.php:43
msgid "Please select a file to import."
msgstr "Vui lòng chọn một tệp để nhập."

#: includes/replace-variables/class-author-variables.php:57
#: includes/replace-variables/class-author-variables.php:68
msgid "Post Author"
msgstr "Tác giả bài viết"

#: includes/modules/local-seo/views/titles-options.php:239
#: includes/modules/schema/shortcode/restaurant.php:41
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Price Range"
msgstr "Phạm vi giá"

#: includes/admin/wizard/views/ready.php:76
msgid "Proceed to Settings"
msgstr "Tiếp tục đến Cài đặt"

#: includes/modules/schema/shortcode/product.php:55
msgid "Product In-Stock"
msgstr "Sản phẩm còn hàng"

#. translators: %s: product tag
#: includes/frontend/class-breadcrumbs.php:350
msgid "Products tagged &ldquo;%s&rdquo;"
msgstr "Sản phẩm được gắn thẻ “ %s”"

#: includes/replace-variables/class-post-variables.php:162
msgid "Publish date with custom formatting pattern."
msgstr "Ngày xuất bản với mẫu định dạng tùy chỉnh."

#: includes/modules/schema/shortcode/recipe.php:118
#: includes/modules/schema/shortcode/recipe.php:126
#: includes/modules/schema/shortcode/recipe.php:134
#: includes/modules/schema/shortcode/recipe.php:147
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Recipe Instructions"
msgstr "Hướng dẫn công thức"

#: includes/modules/schema/shortcode/recipe.php:83
msgid "Recipe Video Description"
msgstr "Mô tả video công thức"

#: includes/modules/schema/shortcode/recipe.php:76
msgid "Recipe Video Name"
msgstr "Tên video công thức"

#: includes/modules/schema/shortcode/recipe.php:41
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Recipe Yield"
msgstr "Năng suất công thức"

#: includes/admin/class-bulk-actions.php:89
#: includes/modules/404-monitor/class-table.php:212
#: includes/modules/404-monitor/class-table.php:275
#: assets/admin/js/rank-math-app.js:1
msgid "Redirect"
msgstr "Chuyển hướng"

#: includes/settings/general/links.php:36
msgid "Redirect Attachments"
msgstr "Chuyển hướng tệp đính kèm"

#: includes/modules/redirections/class-redirections.php:73
msgid "Redirection Count"
msgstr "Số lượng chuyển hướng"

#: includes/modules/redirections/views/debugging.php:25
msgid "Redirection Debugger"
msgstr "Trình gỡ lỗi chuyển hướng"

#: includes/modules/redirections/class-redirections.php:80
msgid "Redirection Hits"
msgstr "Lượt truy cập chuyển hướng"

#. translators: Redirection page url
#: includes/settings/general/links.php:28
msgid "Redirection Manager"
msgstr "Trình quản lý chuyển hướng"

#: includes/modules/redirections/class-redirections.php:150
#: includes/modules/redirections/class-redirections.php:152
msgid "Redirection Settings"
msgstr "Cài đặt chuyển hướng"

#: includes/modules/redirections/class-metabox.php:76
#: includes/modules/redirections/class-metabox.php:80
msgid "Redirection successfully deleted."
msgstr "Đã xóa chuyển hướng thành công."

#: includes/modules/redirections/class-admin.php:430
msgid "Redirection successfully restored."
msgstr "Đã khôi phục chuyển hướng thành công."

#: includes/modules/redirections/views/options.php:51
#: assets/admin/js/rank-math-app.js:1
#: includes/modules/redirections/assets/js/redirections.js:1
msgid "Redirection Type"
msgstr "Loại chuyển hướng"

#: includes/modules/redirections/views/help-tab-overview.php:18
msgid "Redirections can be exported to your .htaccess file for faster redirections, in SEO > Settings > Import/Export."
msgstr "Chuyển hướng có thể được xuất sang tệp .htaccess của bạn để chuyển hướng nhanh hơn, trong SEO> Cài đặt> Nhập / Xuất."

#: includes/settings/general/links.php:29
msgid "Redirections Manager"
msgstr "Trình quản lý chuyển hướng"

#. translators: link to plugin setting screen
#: includes/modules/seo-analysis/seo-analysis-tests.php:117
msgid "Register at Google Search Console and verificate your site by adding the code to <a href=\"%1$s\">Settings &gt; Verificate Tools</a>, then navigate to <a href=\"%2$s\">Settings &gt; Search Console</a> to authenticate and link your site."
msgstr "Đăng ký tại Google Search Console và xác minh trang web của bạn bằng cách thêm mã vào <a href=\"%1$s\">Cài đặt&gt; Công cụ xác minh</a> , sau đó điều hướng đến <a href=\"%2$s\">Cài đặt&gt; Search Console</a> để xác thực và liên kết trang web của bạn."

#: includes/modules/woocommerce/views/options-general.php:33
msgid "Remove category base"
msgstr "Xóa cơ sở danh mục"

#: includes/modules/woocommerce/views/options-general.php:57
msgid "Remove Generator Tag"
msgstr "Xóa thẻ trình tạo"

#: includes/modules/woocommerce/views/options-general.php:34
msgid "Remove prefix from category URL."
msgstr "Xóa tiền tố khỏi URL danh mục."

#: includes/modules/woocommerce/views/options-general.php:58
msgid "Remove WooCommerce generator tag from the source code."
msgstr "Xóa thẻ trình tạo WooCommerce khỏi mã nguồn."

#: includes/settings/general/others.php:109
msgid "RSS After Content"
msgstr "RSS sau nội dung"

#: includes/settings/general/others.php:100
msgid "RSS Before Content"
msgstr "RSS trước nội dung"

#: includes/modules/local-seo/views/titles-options.php:185
msgid "Saturday"
msgstr "Thứ bảy"

#: includes/modules/404-monitor/class-admin.php:155
#: includes/modules/redirections/class-admin.php:166
msgid "Screen Content"
msgstr "Nội dung màn hình"

#: includes/admin/wizard/views/your-site.php:52
#: includes/modules/404-monitor/views/main.php:27
#: includes/modules/redirections/views/main.php:45
msgid "Search"
msgstr "Tìm kiếm"

#. translators: Google Search Console Link
#: includes/settings/general/webmaster.php:19
msgid "Search Console Verification Page"
msgstr "Trang xác minh Search Console"

#: includes/replace-variables/class-basic-variables.php:69
msgid "Search Query"
msgstr "Truy vấn tìm kiếm"

#: includes/settings/general/breadcrumbs.php:107
msgid "Search Results Format"
msgstr "Định dạng kết quả tìm kiếm"

#: includes/settings/titles/misc.php:106
msgid "Search Results Title"
msgstr "Tiêu đề kết quả tìm kiếm"

#: includes/class-frontend-seo-score.php:173
#: includes/modules/seo-analysis/views/graphs.php:31
#: includes/modules/analytics/assets/js/admin-bar.js:1
#: includes/modules/analytics/assets/js/stats.js:1
msgid "SEO Score"
msgstr "Điểm SEO"

#: includes/admin/class-option-center.php:105
msgid "SEO Settings"
msgstr "Cài đặt SEO"

#. translators: Post Type Singular Name
#. translators: Taxonomy Singular Name
#: includes/admin/class-admin-bar-menu.php:248
#: includes/admin/class-admin-bar-menu.php:271
msgid "SEO Settings for %s"
msgstr "Cài đặt SEO cho %s"

#: includes/admin/class-option-center.php:189
msgid "SEO Titles &amp; Meta"
msgstr "Tiêu đề &amp; Meta SEO"

#: includes/admin/wizard/class-optimization.php:33
msgid "SEO Tweaks"
msgstr "Tinh chỉnh SEO"

#: includes/admin/importers/class-status.php:126
msgid "Settings import failed."
msgstr "Nhập cài đặt không thành công."

#: includes/admin/wizard/views/your-site.php:39
msgid "Setup Tutorial"
msgstr "Hướng dẫn thiết lập"

#: includes/settings/general/breadcrumbs.php:141
msgid "Show Category(s)"
msgstr "Hiển thị (các) danh mục"

#: includes/settings/general/breadcrumbs.php:50
msgid "Show Homepage Link"
msgstr "Hiển thị liên kết trang chủ"

#: includes/replace-variables/class-basic-variables.php:125
msgid "Site Description"
msgstr "Mô tả trang web"

#: includes/replace-variables/class-basic-variables.php:114
msgid "Site Title"
msgstr "Tiêu đề trang web"

#: includes/admin/wizard/class-sitemap.php:33
#: includes/module/class-manager.php:143
#: includes/modules/sitemap/sitemap-xsl.php:259
msgid "Sitemap"
msgstr "Sơ đồ trang web"

#. translators: Post Type Sitemap Url
#. translators: Taxonomy Sitemap Url
#: includes/modules/sitemap/class-admin.php:168
#: includes/modules/sitemap/class-admin.php:234
msgid "Sitemap URL: %s"
msgstr "URL sơ đồ trang web: %s"

#. translators: database size
#: includes/modules/analytics/views/options.php:34
msgid "Size: %s"
msgstr "Kích thước: %s"

#: includes/admin/class-setup-wizard.php:353
#: includes/admin/wizard/class-schema-markup.php:40
msgid "Skip Step"
msgstr "Bỏ qua bước"

#: includes/helpers/class-choices.php:448 includes/helpers/class-schema.php:106
msgid "Software Application"
msgstr "Ứng dụng phần mềm"

#: includes/modules/schema/shortcode/course.php:55
#: includes/modules/schema/shortcode/event.php:100
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Start Date"
msgstr "Ngày bắt đầu"

#: includes/module/class-manager.php:284
#: includes/modules/analytics/assets/js/stats.js:1
#: includes/modules/redirections/assets/js/redirections.js:1
msgid "Status"
msgstr "Trạng thái"

#: includes/modules/schema/shortcode/event.php:151
msgid "Stock Inventory"
msgstr "Hàng tồn kho"

#: includes/modules/redirections/views/debugging.php:37
msgid "Stop Redirection"
msgstr "Dừng chuyển hướng"

#: includes/settings/titles/global.php:101 assets/admin/js/rank-math-app.js:1
msgid "Summary Card"
msgstr "Thẻ tóm tắt"

#: includes/helpers/class-choices.php:606
msgid "Technical Support"
msgstr "Hỗ trợ kỹ thuật"

#: includes/replace-variables/class-term-variables.php:48
msgid "Term Description"
msgstr "Mô tả thuật ngữ"

#: includes/modules/404-monitor/views/options.php:30
msgid "The Simple mode only logs URI and access time, while the Advanced mode creates detailed logs including additional information such as the Referer URL."
msgstr "Chế độ Đơn giản chỉ ghi nhật ký URI và thời gian truy cập, trong khi chế độ Nâng cao tạo nhật ký chi tiết bao gồm thông tin bổ sung như URL giới thiệu."

#: includes/admin/views/import-export/backup-panel.php:51
msgid "There is no backup."
msgstr "Không có bản sao lưu."

#: includes/modules/seo-analysis/seo-analysis-tests.php:68
msgid "This looks nice for readers - and it gets your keywords into the URL (keywords in the URL is a ranking factor)."
msgstr "Điều này trông đẹp mắt đối với người đọc - và nó đưa từ khóa của bạn vào URL (từ khóa trong URL là một yếu tố xếp hạng)."

#: includes/modules/local-seo/views/titles-options.php:183
msgid "Thursday"
msgstr "Thứ năm"

#: includes/modules/local-seo/views/titles-options.php:153
msgid "Time format used in the contact shortcode."
msgstr "Định dạng thời gian được sử dụng trong mã ngắn liên hệ."

#: includes/modules/local-seo/views/titles-options.php:181
msgid "Tuesday"
msgstr "Thứ ba"

#: includes/settings/titles/global.php:97
msgid "Twitter Card Type"
msgstr "Loại thẻ Twitter"

#: includes/modules/schema/shortcode/jobposting.php:55
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Unpublish when expired"
msgstr "Hủy xuất bản khi hết hạn"

#: includes/modules/404-monitor/class-table.php:224
msgid "URI"
msgstr "URI"

#: includes/modules/instant-indexing/views/history.php:15
#: includes/modules/local-seo/views/titles-options.php:84
#: includes/modules/schema/shortcode/book.php:20
#: includes/modules/schema/shortcode/music.php:20
#: includes/modules/sitemap/sitemap-xsl.php:308
#: includes/settings/titles/local.php:68
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "URL"
msgstr "URL"

#: includes/modules/404-monitor/class-table.php:196
msgid "View Redirection"
msgstr "Xem chuyển hướng"

#: includes/module/class-manager.php:332
#: includes/modules/woocommerce/class-admin.php:74
msgid "WooCommerce"
msgstr "WooCommerce"

#: includes/admin/wizard/class-sitemap.php:67
msgid "XML Sitemaps help search engines index your website&#039;s content more effectively."
msgstr "Sơ đồ trang web XML giúp các công cụ tìm kiếm lập chỉ mục nội dung trang web của bạn hiệu quả hơn."

#: includes/admin/class-admin.php:369 includes/traits/class-ajax.php:59
msgid "You are not authorized to perform this action."
msgstr "Bạn không được phép thực hiện hành động này."

#: includes/modules/404-monitor/views/help-tab-bulk.php:13
msgid "You can also redirect or delete multiple items at once. Selecting multiple items to redirect allows you to redirect them to a single URL."
msgstr "Bạn cũng có thể chuyển hướng hoặc xóa nhiều mục cùng một lúc. Việc chọn nhiều mục để chuyển hướng cho phép bạn chuyển hướng chúng đến một URL duy nhất."

#: includes/modules/404-monitor/views/help-tab-screen-content.php:16
#: includes/modules/redirections/views/help-tab-screen-content.php:16
msgid "You can hide/display columns based on your needs."
msgstr "Bạn có thể ẩn / hiển thị các cột dựa trên nhu cầu của mình."

#: includes/admin/wizard/class-import.php:34
msgid "You can import SEO settings from the following plugins:"
msgstr "Bạn có thể nhập cài đặt SEO từ các plugin sau:"

#: includes/admin/wizard/views/compatibility.php:157
msgid "You can import settings in the next step."
msgstr "Bạn có thể nhập cài đặt trong bước tiếp theo."

#: includes/admin/wizard/views/ready.php:74
msgid "Your site is now optimized."
msgstr "Trang web của bạn hiện đã được tối ưu hóa."

#: includes/helpers/class-choices.php:444
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Restaurant"
msgstr "Nhà hàng"

#: includes/modules/local-seo/views/titles-options.php:184
msgid "Friday"
msgstr "Thứ sáu"

#: includes/frontend/class-shortcodes.php:178
msgid "Address:"
msgstr "Địa chỉ:"

#: includes/modules/local-seo/views/titles-options.php:252
msgid "Add"
msgstr "Thêm"

#: includes/modules/instant-indexing/views/history.php:13
#: includes/modules/redirections/class-table.php:339
#: includes/modules/seo-analysis/class-seo-analyzer.php:144
#: assets/admin/js/rank-math-app.js:1
#: includes/modules/content-ai/assets/js/content-ai-page.js:1
#: includes/modules/content-ai/assets/js/content-ai.js:1
msgid "All"
msgstr "Tất cả"

#: includes/modules/local-seo/views/titles-options.php:113
#: includes/modules/schema/shortcode/event.php:62
#: includes/modules/schema/shortcode/person.php:27
#: includes/modules/schema/shortcode/restaurant.php:20
#: includes/modules/sitemap/sitemap-xsl.php:171
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Address"
msgstr "Địa chỉ"

#: includes/settings/titles/misc.php:93
msgid "404 Title"
msgstr "Tiêu đề 404"

#: includes/modules/local-seo/views/titles-options.php:290
msgid "About Page"
msgstr "Trang giới thiệu"

#: includes/admin/wizard/views/compatibility.php:62
msgid "Rank Math is compatible with your PHP version but we recommend updating to PHP 7.4 for increased speed and security."
msgstr "Rank Math tương thích với phiên bản PHP của bạn nhưng chúng tôi khuyên bạn nên cập nhật lên PHP 7.4 để tăng tốc độ và bảo mật."

#: includes/admin/wizard/views/compatibility.php:63
msgid "This plugin is compatible with your PHP version but we recommend updating to PHP 7.4 for increased speed and security."
msgstr "Plugin này tương thích với phiên bản PHP của bạn nhưng chúng tôi khuyên bạn nên cập nhật lên PHP 7.4 để tăng tốc độ và bảo mật."

#: includes/modules/robots-txt/options.php:20
msgid "Contents are locked because a robots.txt file is present in the root folder."
msgstr "Nội dung bị khóa vì tệp robots.txt có trong thư mục gốc."

#: includes/opengraph/class-slack.php:334
msgid "Items"
msgstr "Mục"

#: includes/modules/status/class-system-status.php:202
msgid "Not found"
msgstr "Không tìm thấy"

#: includes/modules/instant-indexing/views/history.php:31
msgid "Response Code"
msgstr "Mã phản hồi"

#: includes/admin/class-admin-bar-menu.php:241
msgid "Pages"
msgstr "Trang"

#: includes/admin/class-admin-helper.php:345 assets/admin/js/components.js:1
msgid "SEO by Rank Math"
msgstr "SEO bởi Rank Math"

#: includes/helpers/class-choices.php:447
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Service"
msgstr "Dịch vụ"

#: includes/admin/wizard/views/compatibility.php:58
msgid "Recommended: PHP 7.4 or later"
msgstr "Khuyến nghị: PHP 7.4 trở lên"

#: includes/admin/class-option-center.php:304
msgid "Post Formats"
msgstr "Định dạng bài viết"

#: includes/admin/class-option-center.php:167
#: includes/modules/content-ai/views/options.php:102
#: includes/modules/sitemap/class-admin.php:102
#: includes/modules/sitemap/html-sitemap/class-authors.php:115
msgid "Authors"
msgstr "Tác giả"

#: includes/admin/class-setup-wizard.php:110
#: includes/admin/views/import-export/import-export-panel.php:50
#: includes/admin/views/import-export/plugins-panel.php:76
msgid "Import"
msgstr "Nhập"

#: includes/settings/titles/author.php:94
msgid "Author Archive Title"
msgstr "Tiêu đề lưu trữ tác giả"

#: includes/settings/titles/misc.php:51
msgid "Date Archive Description"
msgstr "Mô tả lưu trữ ngày"

#: includes/settings/titles/author.php:108
msgid "Author Archive Description"
msgstr "Mô tả lưu trữ tác giả"

#: includes/modules/sitemap/settings/general.php:42
msgid "Images in Sitemaps"
msgstr "Hình ảnh trong sơ đồ trang web"

#: includes/modules/buddypress/views/options-titles.php:47
#: includes/settings/titles/author.php:48
#: includes/settings/titles/homepage.php:70
#: includes/settings/titles/post-types.php:233
#: includes/settings/titles/taxonomies.php:74
msgid "Default"
msgstr "Mặc định"

#. translators: php version
#: includes/admin/wizard/views/compatibility.php:70
msgid "Your PHP Version: %s | Recommended version: 7.4 | Minimal required: 7.2"
msgstr "Phiên bản PHP của bạn: %s | Phiên bản được đề xuất: 7.4 | Yêu cầu tối thiểu: 7.2"

#: includes/settings/titles/misc.php:94
msgid "Title tag on 404 Not Found error page."
msgstr "Thẻ tiêu đề trên trang lỗi 404 Không tìm thấy."

#: includes/settings/titles/misc.php:52
msgid "Date archive description."
msgstr "Mô tả lưu trữ ngày."

#: includes/settings/titles/misc.php:38
msgid "Title tag on day/month/year based archives."
msgstr "Thẻ tiêu đề trên các kho lưu trữ dựa trên ngày/tháng/năm."

#: includes/settings/titles/misc.php:19
msgid "Date Archives"
msgstr "Lưu trữ ngày"

#: includes/settings/titles/homepage.php:128
msgid "Image displayed when your homepage is shared on Facebook and other social networks. Use images that are at least 1200 x 630 pixels for the best display on high resolution devices."
msgstr "Hình ảnh được hiển thị khi trang chủ của bạn được chia sẻ trên Facebook và các mạng xã hội khác. Sử dụng hình ảnh có kích thước ít nhất 1200 x 630 pixel để hiển thị tốt nhất trên các thiết bị có độ phân giải cao."

#: includes/settings/titles/homepage.php:83
msgid "Custom values for robots meta tag on homepage."
msgstr "Giá trị tùy chỉnh cho thẻ meta robots trên trang chủ."

#: includes/modules/buddypress/views/options-titles.php:48
#: includes/settings/titles/author.php:49
#: includes/settings/titles/homepage.php:71
#: includes/settings/titles/post-types.php:234
#: includes/settings/titles/taxonomies.php:75
msgid "Custom"
msgstr "Tùy chỉnh"

#: includes/settings/titles/homepage.php:68
msgid "Select custom robots meta for homepage, such as <code>nofollow</code>, <code>noarchive</code>, etc. Otherwise the default meta will be used, as set in the Global Meta tab."
msgstr "Chọn meta robots tùy chỉnh cho trang chủ, chẳng hạn như <code>nofollow</code>, <code>noarchive</code>, v.v. Nếu không, meta mặc định sẽ được sử dụng, như được đặt trong tab Meta toàn cầu."

#: includes/settings/titles/homepage.php:52
msgid "Homepage meta description."
msgstr "Mô tả meta trang chủ."

#: includes/settings/titles/global.php:86
msgid "OpenGraph Thumbnail"
msgstr "Hình thu nhỏ OpenGraph"

#: includes/settings/titles/global.php:76
msgid "Capitalize Titles"
msgstr "Viết hoa tiêu đề"

#: includes/settings/titles/global.php:52
msgid "You can use the separator character in titles by inserting <code>%separator%</code> or <code>%sep%</code> in the title fields."
msgstr "Bạn có thể sử dụng ký tự phân cách trong tiêu đề bằng cách chèn <code>%separator%</code> hoặc <code>%sep%</code> vào các trường tiêu đề."

#: includes/settings/titles/author.php:138
msgid "Add SEO Controls for user profile pages. Access to the Meta Box can be fine tuned with code, using a special filter hook."
msgstr "Thêm Kiểm soát SEO cho các trang hồ sơ người dùng. Quyền truy cập vào Hộp Meta có thể được tinh chỉnh bằng mã, sử dụng hook bộ lọc đặc biệt."

#: includes/settings/titles/author.php:109
msgid "Author archive meta description. SEO options for specific author archives can be set with the meta box in the user profiles."
msgstr "Mô tả meta kho lưu trữ tác giả. Tùy chọn SEO cho kho lưu trữ tác giả cụ thể có thể được đặt bằng hộp meta trong hồ sơ người dùng."

#: includes/settings/titles/author.php:95
msgid "Title tag on author archives. SEO options for specific authors can be set with the meta box available in the user profiles."
msgstr "Thẻ tiêu đề trên kho lưu trữ tác giả. Tùy chọn SEO cho các tác giả cụ thể có thể được đặt bằng hộp meta có sẵn trong hồ sơ người dùng."

#: includes/settings/titles/author.php:34
msgid "Change the <code>/author/</code> part in author archive URLs."
msgstr "Thay đổi phần <code>/author/</code> trong URL kho lưu trữ tác giả."

#: includes/settings/titles/author.php:22 includes/settings/titles/misc.php:26
#: includes/settings/titles/post-types.php:330
#: includes/settings/titles/post-types.php:368
msgid "Disabled"
msgstr "Đã tắt"

#. translators: Norton webmaster link
#: includes/settings/general/webmaster.php:80
msgid "Norton Ownership Verification Page"
msgstr "Trang xác minh quyền sở hữu của Norton"

#. translators: Norton webmaster link
#: includes/settings/general/webmaster.php:80
msgid "Enter your Norton Safe Web verification HTML code or ID. Learn how to get it: %s"
msgstr "Nhập mã HTML hoặc ID xác minh Norton Safe Web của bạn. Tìm hiểu cách lấy nó: %s"

#: includes/settings/general/webmaster.php:78
msgid "Norton Safe Web Verification ID"
msgstr "ID xác minh Norton Safe Web"

#. translators: Pinterest webmaster link
#: includes/settings/general/webmaster.php:68
msgid "Enter your Pinterest verification HTML code or ID. Learn how to get it: %s"
msgstr "Nhập mã HTML hoặc ID xác minh Pinterest của bạn. Tìm hiểu cách lấy nó: %s"

#. translators: Yandex webmaster link
#: includes/settings/general/webmaster.php:55
msgid "Enter your Yandex verification HTML code or ID. Learn how to get it: %s"
msgstr "Nhập mã HTML hoặc ID xác minh Yandex của bạn. Tìm hiểu cách lấy nó: %s"

#. translators: Baidu webmaster link
#: includes/settings/general/webmaster.php:43
msgid "Enter your Baidu Webmaster Tools verification HTML code or ID. Learn how to get it: %s"
msgstr "Nhập mã HTML hoặc ID xác minh Công cụ quản trị web Baidu của bạn. Tìm hiểu cách lấy nó: %s"

#. translators: Bing webmaster link
#: includes/settings/general/webmaster.php:31
msgid "Bing Webmaster Verification Page"
msgstr "Trang xác minh quản trị web của Bing"

#. translators: Bing webmaster link
#: includes/settings/general/webmaster.php:31
msgid "Enter your Bing Webmaster Tools verification HTML code or ID. Get it here: %s"
msgstr "Nhập mã HTML hoặc ID xác minh Công cụ quản trị web Bing của bạn. Lấy nó ở đây: %s"

#. translators: Google Search Console Link
#: includes/settings/general/webmaster.php:19
msgid "Enter your Google Search Console verification HTML code or ID. Learn how to get it: %s"
msgstr "Nhập mã HTML hoặc ID xác minh Google Search Console của bạn. Tìm hiểu cách lấy nó: %s"

#: includes/settings/general/rss-vars-table.php:42
msgid "Featured image of the article."
msgstr "Hình ảnh nổi bật của bài viết."

#: includes/settings/general/rss-vars-table.php:38
msgid "A link to your site, with your site's name and description as anchor text."
msgstr "Liên kết đến trang web của bạn, với tên và mô tả trang web của bạn làm văn bản neo."

#: includes/settings/general/rss-vars-table.php:34
msgid "A link to your site, with your site's name as anchor text."
msgstr "Liên kết đến trang web của bạn, với tên trang web của bạn làm văn bản neo."

#: includes/settings/general/rss-vars-table.php:19
msgid "Variable"
msgstr "Biến"

#: includes/settings/general/links.php:96
msgid "The <code>nofollow</code> attribute <strong>will not be added</strong> for the link if target domain is in this list. Add one per line."
msgstr "Thuộc tính <code>nofollow</code> <strong>sẽ không được thêm vào</strong> cho liên kết nếu tên miền đích nằm trong danh sách này. Thêm một tên miền mỗi dòng."

#: includes/settings/general/links.php:95
msgid "Nofollow Exclude Domains"
msgstr "Tên miền loại trừ Nofollow"

#: includes/settings/general/links.php:82
msgid "Only add <code>nofollow</code> attribute for the link if target domain is in this list. Add one per line. Leave empty to apply nofollow for <strong>ALL</strong> external domains."
msgstr "Chỉ thêm thuộc tính <code>nofollow</code> cho liên kết nếu tên miền đích nằm trong danh sách này. Thêm một tên miền mỗi dòng. Để trống để áp dụng nofollow cho <strong>TẤT CẢ</strong> các tên miền bên ngoài."

#. translators: Link to kb article
#: includes/settings/general/links.php:20
msgid "Why do this?"
msgstr "Tại sao phải làm điều này?"

#. translators: Link to kb article
#: includes/settings/general/links.php:20
msgid "Remove /category/ from category archive URLs. %s <br>E.g. <code>example.com/category/my-category/</code> becomes <code>example.com/my-category</code>"
msgstr "Xóa /category/ khỏi URL kho lưu trữ danh mục. %s <br>Ví dụ: <code>example.com/category/my-category/</code> trở thành <code>example.com/my-category</code>"

#: includes/modules/image-seo/options.php:50
msgid "Format used for the new <code>title</code> attribute values."
msgstr "Định dạng được sử dụng cho các giá trị thuộc tính <code>title</code> mới."

#: includes/modules/image-seo/options.php:40
msgid "Add <code>TITLE</code> attribute for all <code>images</code> without a <code>TITLE</code> attribute automatically. The attribute is dynamically applied when the content is displayed, and the stored content is not changed."
msgstr "Tự động thêm thuộc tính <code>TITLE</code> cho tất cả <code>hình ảnh</code> không có thuộc tính <code>TITLE</code>. Thuộc tính được áp dụng động khi nội dung được hiển thị và nội dung được lưu trữ không bị thay đổi."

#: includes/modules/image-seo/options.php:26
msgid "Format used for the new <code>alt</code> attribute values."
msgstr "Định dạng được sử dụng cho các giá trị thuộc tính <code>alt</code> mới."

#: includes/modules/image-seo/options.php:25
msgid "Alt attribute format"
msgstr "Định dạng thuộc tính Alt"

#: includes/modules/image-seo/options.php:16
msgid "Add <code>alt</code> attributes for <code>images</code> without <code>alt</code> attributes automatically. The attribute is dynamically applied when the content is displayed, and the stored content is not changed."
msgstr "Tự động thêm thuộc tính <code>alt</code> cho <code>hình ảnh</code> không có thuộc tính <code>alt</code>. Thuộc tính được áp dụng động khi nội dung được hiển thị và nội dung được lưu trữ không bị thay đổi."

#: includes/settings/general/htaccess.php:52
msgid "Be careful when editing the htaccess file, it is easy to make mistakes and break your site. If that happens, you can restore the file to its state <strong>before the last edit</strong> by replacing the htaccess file with the backup copy created by Rank Math in the same directory (<em>.htaccess_back_xxxxxx</em>) using an FTP client."
msgstr "Hãy cẩn thận khi chỉnh sửa tệp htaccess, rất dễ mắc lỗi và làm hỏng trang web của bạn. Nếu điều đó xảy ra, bạn có thể khôi phục tệp về trạng thái <strong>trước khi chỉnh sửa lần cuối</strong> bằng cách thay thế tệp htaccess bằng bản sao lưu do Rank Math tạo trong cùng thư mục (<em>.htaccess_back_xxxxxx</em>) bằng cách sử dụng máy khách FTP."

#: includes/settings/general/htaccess.php:40
msgid ".htaccess file is not writable."
msgstr "Tệp .htaccess không thể ghi."

#: includes/modules/woocommerce/views/options-general.php:47
msgid "default: /product-category/accessories/action-figures/ - changed: /product-category/action-figures/"
msgstr "mặc định: /product-category/accessories/action-figures/ - đã thay đổi: /product-category/action-figures/"

#: includes/modules/woocommerce/views/options-general.php:46
msgid "Remove parent slugs from category URL."
msgstr "Xóa slug cha mẹ khỏi URL danh mục."

#: includes/modules/woocommerce/views/options-general.php:45
msgid " Remove parent slugs"
msgstr " Xóa slug cha mẹ"

#: includes/modules/woocommerce/views/options-general.php:35
msgid "default: /product-category/accessories/action-figures/ - changed: /accessories/action-figures/"
msgstr "mặc định: /product-category/accessories/action-figures/ - đã thay đổi: /accessories/action-figures/"

#: includes/modules/woocommerce/views/options-general.php:17
msgid "Remove base"
msgstr "Xóa cơ sở"

#: includes/modules/woocommerce/class-wc-vars.php:68
msgid "Product's brand of the current product"
msgstr "Thương hiệu sản phẩm của sản phẩm hiện tại"

#: includes/modules/woocommerce/class-wc-vars.php:67
msgid "Product's brand."
msgstr "Thương hiệu sản phẩm."

#: includes/modules/woocommerce/class-wc-vars.php:57
msgid "Product's short description of the current product"
msgstr "Mô tả ngắn gọn về sản phẩm hiện tại"

#: includes/modules/woocommerce/class-wc-vars.php:56
msgid "Product's short description."
msgstr "Mô tả ngắn gọn về sản phẩm."

#: includes/modules/woocommerce/class-wc-vars.php:46
msgid "Product's SKU of the current product"
msgstr "SKU sản phẩm của sản phẩm hiện tại"

#: includes/modules/woocommerce/class-wc-vars.php:45
msgid "Product's SKU."
msgstr "SKU sản phẩm."

#: includes/modules/woocommerce/class-wc-vars.php:34
msgid "Product's price."
msgstr "Giá sản phẩm."

#: includes/modules/sitemap/settings/taxonomies.php:45
msgid "Include Empty Terms"
msgstr "Bao gồm các thuật ngữ trống"

#: includes/modules/sitemap/settings/taxonomies.php:22
msgid "Include archive pages for terms of this taxonomy in the XML sitemap."
msgstr "Bao gồm các trang lưu trữ cho các thuật ngữ của phân loại này trong sơ đồ trang web XML."

#: includes/modules/sitemap/settings/authors.php:27
#: includes/modules/sitemap/settings/post-types.php:34
#: includes/modules/sitemap/settings/taxonomies.php:21
msgid "Include in Sitemap"
msgstr "Bao gồm trong sơ đồ trang web"

#: includes/modules/sitemap/settings/general.php:74
msgid "Add term IDs, separated by comma. This option is applied for all taxonomies."
msgstr "Thêm ID thuật ngữ, được phân tách bằng dấu phẩy. Tùy chọn này được áp dụng cho tất cả các phân loại."

#: includes/modules/sitemap/settings/general.php:73
msgid "Exclude Terms"
msgstr "Loại trừ các thuật ngữ"

#: includes/modules/sitemap/settings/general.php:52
msgid "Include Featured Images"
msgstr "Bao gồm hình ảnh nổi bật"

#: includes/modules/sitemap/settings/general.php:30
msgid "Links Per Sitemap"
msgstr "Liên kết trên mỗi sơ đồ trang web"

#: includes/modules/sitemap/settings/authors.php:65
msgid "Add user IDs, separated by commas, to exclude them from the sitemap."
msgstr "Thêm ID người dùng, cách nhau bằng dấu phẩy, để loại trừ khỏi sơ đồ trang web."

#: includes/modules/sitemap/settings/authors.php:51
msgid "Exclude User Roles"
msgstr "Loại trừ vai trò người dùng"

#. translators: 1. separator, 2. blogname
#: includes/modules/sitemap/class-stylesheet.php:53
msgid "XML Sitemap %1$s %2$s"
msgstr "Sơ đồ trang web XML %1$s %2$s"

#. translators: Taxonomy singular label
#: includes/modules/sitemap/class-admin.php:222
msgid "%s archives"
msgstr "Lưu trữ %s"

#. translators: Taxonomy singular label
#: includes/modules/sitemap/class-admin.php:217
msgid "your product %s pages"
msgstr "các trang %s sản phẩm của bạn"

#: includes/modules/sitemap/class-admin.php:172
msgid "Please note that this will add the attachment page URLs to the sitemap, not direct image URLs."
msgstr "Xin lưu ý rằng điều này sẽ thêm URL trang tệp đính kèm vào sơ đồ trang web, không phải URL hình ảnh trực tiếp."

#. translators: Post Type label
#: includes/modules/sitemap/class-admin.php:157
msgid "single %s"
msgstr "%s đơn lẻ"

#: includes/modules/sitemap/class-admin.php:135
msgid "your product pages"
msgstr "các trang sản phẩm của bạn"

#: includes/modules/sitemap/class-admin.php:134
msgid "attachments"
msgstr "tệp đính kèm"

#. translators: Learn more link.
#: includes/modules/sitemap/class-admin.php:104
msgid "Set the sitemap options for author archive pages. %s."
msgstr "Đặt tùy chọn sơ đồ trang web cho các trang lưu trữ tác giả. %s."

#: includes/modules/sitemap/class-admin.php:85
msgid "This tab contains General settings related to the XML sitemaps."
msgstr "Tab này chứa các cài đặt chung liên quan đến sơ đồ trang web XML."

#: includes/modules/seo-analysis/views/form.php:27
msgid "Start Page Analysis"
msgstr "Bắt đầu phân tích trang"

#: includes/modules/seo-analysis/seo-analysis-tests.php:502
msgid "No sitemaps found."
msgstr "Không tìm thấy sơ đồ trang web."

#. translators: post ID count
#: includes/modules/seo-analysis/seo-analysis-tests.php:397
msgid "+%d More..."
msgstr "+%d Thêm..."

#. translators: post type links
#: includes/modules/seo-analysis/seo-analysis-tests.php:365
msgid "There are %s with no focus keyword set."
msgstr "Có %s không có từ khóa trọng tâm được đặt."

#: includes/modules/seo-analysis/seo-analysis-tests.php:233
msgid "Your Site Tagline is set to a custom value."
msgstr "Khẩu hiệu trang web của bạn được đặt thành giá trị tùy chỉnh."

#: includes/modules/seo-analysis/seo-analysis-tests.php:227
msgid "Your Site Tagline is set to the default value <em>Just another WordPress site</em>."
msgstr "Khẩu hiệu trang web của bạn được đặt thành giá trị mặc định <em>Chỉ là một trang web WordPress khác</em>."

#: includes/modules/seo-analysis/seo-analysis-tests.php:135
msgid "If you don't have an XML sitemap, the best option is to install a plugin that creates sitemaps for you. That way you'll know the sitemap will always be up-to-date. Plugins can also automatically ping the search engines when the XML file is updated. The Rank Math WordPress plugin gives you complete control over your site's XML sitemaps. You can control the settings for each page as you write or edit it, and Rank Math will ping Google as soon as you submit your edits. This results in fast crawls and indexing."
msgstr "Nếu bạn không có sơ đồ trang web XML, tùy chọn tốt nhất là cài đặt plugin tạo sơ đồ trang web cho bạn. Bằng cách đó, bạn sẽ biết sơ đồ trang web luôn được cập nhật. Plugin cũng có thể tự động ping các công cụ tìm kiếm khi tệp XML được cập nhật. Plugin Rank Math WordPress cung cấp cho bạn toàn quyền kiểm soát sơ đồ trang web XML của trang web. Bạn có thể kiểm soát cài đặt cho mỗi trang khi bạn viết hoặc chỉnh sửa nó và Rank Math sẽ ping Google ngay khi bạn gửi chỉnh sửa của mình. Điều này dẫn đến việc thu thập dữ liệu và lập chỉ mục nhanh chóng."

#: includes/modules/seo-analysis/seo-analysis-tests.php:134
msgid "XML sitemaps are a special type of text file that tells search engines about the structure of your site. They're a list of all the resources (pages and files) you would like the search engine to index. You can assign different priorities, so certain pages will be crawled first. Before XML sitemaps, search engines were limited to indexing the content they could find by following links. That's still an important feature for search engine spiders, but XML sitemaps have made it easier for content creators and search engines to collaborate."
msgstr "Sơ đồ trang web XML là một loại tệp văn bản đặc biệt cho các công cụ tìm kiếm biết về cấu trúc trang web của bạn. Chúng là danh sách tất cả các tài nguyên (trang và tệp) mà bạn muốn công cụ tìm kiếm lập chỉ mục. Bạn có thể gán các mức độ ưu tiên khác nhau, vì vậy một số trang nhất định sẽ được thu thập dữ liệu trước. Trước sơ đồ trang web XML, các công cụ tìm kiếm chỉ giới hạn trong việc lập chỉ mục nội dung mà họ có thể tìm thấy bằng cách theo dõi các liên kết. Đó vẫn là một tính năng quan trọng đối với bot công cụ tìm kiếm, nhưng sơ đồ trang web XML đã giúp người tạo nội dung và công cụ tìm kiếm dễ dàng cộng tác hơn."

#. translators: Link to Search Console KB article
#: includes/modules/seo-analysis/seo-analysis-tests.php:125
msgid "Read <a href=\"%1$s\" target=\"_blank\">this article</a> for detailed instructions on setting up your Google Webmaster account and getting Rank Math to work with the Google Search Console."
msgstr "Đọc <a href=\"%1$s\" target=\"_blank\">bài viết này</a> để biết hướng dẫn chi tiết về cách thiết lập tài khoản Google Webmaster của bạn và làm cho Rank Math hoạt động với Google Search Console."

#. translators: link to plugin search console setting screen
#: includes/modules/seo-analysis/seo-analysis-tests.php:123
msgid "You can integrate the Google Search Console with Rank math in the <a href=\"%1$s\" target=\"_blank\">Search Console tab</a>. of Rank Math's General Settings menu."
msgstr "Bạn có thể tích hợp Google Search Console với Rank Math trong <a href=\"%1$s\" target=\"_blank\">tab Search Console</a>. của menu Cài đặt chung của Rank Math."

#: includes/modules/seo-analysis/seo-analysis-tests.php:121
msgid "Google's Search Console is a vital source of information concerning your rankings and click-through rates.  Rank Math can import this data, so you don't have to log into your Google account to get the data you need."
msgstr "Google Search Console là nguồn thông tin quan trọng liên quan đến thứ hạng và tỷ lệ nhấp của bạn.  Rank Math có thể nhập dữ liệu này, vì vậy bạn không cần phải đăng nhập vào tài khoản Google của mình để lấy dữ liệu bạn cần."

#: includes/modules/seo-analysis/seo-analysis-tests.php:78
msgid "Of course, if you don't give Rank Math a focus keyword to work with, it can't give you any useful feedback."
msgstr "Tất nhiên, nếu bạn không cung cấp cho Rank Math một từ khóa trọng tâm để làm việc, nó sẽ không thể cung cấp cho bạn bất kỳ phản hồi hữu ích nào."

#: includes/modules/seo-analysis/seo-analysis-tests.php:77
msgid "Rank Math uses these focus keywords to analyze your on-page content.  It can tell if you've done a good job of optimizing your text to rank for these keywords."
msgstr "Rank Math sử dụng các từ khóa trọng tâm này để phân tích nội dung trên trang của bạn.  Nó có thể cho biết liệu bạn đã làm tốt việc tối ưu hóa văn bản của mình để xếp hạng cho các từ khóa này hay chưa."

#: includes/modules/seo-analysis/seo-analysis-tests.php:74
#: includes/replace-variables/class-advanced-variables.php:55
#: includes/settings/titles/post-types.php:288
msgid "Focus Keywords"
msgstr "Từ khóa trọng tâm"

#: includes/modules/seo-analysis/seo-analysis-tests.php:64
msgid "It's not very kind on the eyes, and it does nothing for your site's SEO.  In fact, it can hurt it - Google's bot is quite cautious about crawling pages that look auto-generated."
msgstr "Nó không đẹp mắt lắm và không làm gì cho SEO của trang web bạn.  Trên thực tế, nó có thể gây hại - bot của Google khá thận trọng trong việc thu thập dữ liệu các trang trông giống như được tạo tự động."

#: includes/modules/seo-analysis/seo-analysis-tests.php:38
msgid "The tagline is the second option.  Choose a tagline that summarizes your site in a few words.  The tagline is also a good place to use your main keyword."
msgstr "Khẩu hiệu là lựa chọn thứ hai.  Chọn một khẩu hiệu tóm tắt trang web của bạn trong một vài từ.  Khẩu hiệu cũng là nơi thích hợp để sử dụng từ khóa chính của bạn."

#. translators: link to general setting screen
#: includes/modules/seo-analysis/seo-analysis-tests.php:37
msgid "Changing your tagline is very easy.  Just head on over to <a target=\"_blank\" href=\"%1$s\">Settings - General</a> in WordPress's admin menu (on the left), or click on the link in this sentence."
msgstr "Thay đổi khẩu hiệu của bạn rất dễ dàng.  Chỉ cần truy cập vào <a target=\"_blank\" href=\"%1$s\">Cài đặt - Chung</a> trong menu quản trị của WordPress (bên trái) hoặc nhấp vào liên kết trong câu này."

#: includes/modules/seo-analysis/seo-analysis-tests.php:35
msgid "Unfortunately, the standard WordPress tagline is \"Just Another WordPress site.\"  That's pretty sloppy looking, and it does nothing for your SEO.  In fact, it's actually a security risk - it makes it easy for hackers with a WordPress exploit to locate your site with an automated search."
msgstr "Thật không may, khẩu hiệu WordPress tiêu chuẩn là \"Chỉ là một trang web WordPress khác.\"  Điều đó trông khá cẩu thả và không làm gì cho SEO của bạn.  Trên thực tế, nó thực sự là một rủi ro bảo mật - nó giúp tin tặc dễ dàng khai thác WordPress để định vị trang web của bạn bằng tìm kiếm tự động."

#: includes/modules/seo-analysis/seo-analysis-tests.php:31
msgid "Site Tagline"
msgstr "Khẩu hiệu trang web"

#: includes/modules/seo-analysis/class-seo-analyzer.php:640
msgid "Security"
msgstr "Bảo mật"

#: includes/modules/seo-analysis/class-seo-analyzer.php:639
msgid "Performance"
msgstr "Hiệu suất"

#: includes/modules/seo-analysis/class-seo-analyzer.php:637
msgid "Advanced SEO"
msgstr "SEO nâng cao"

#: includes/modules/instant-indexing/views/history.php:32
#: includes/modules/seo-analysis/class-result.php:176
#: includes/modules/seo-analysis/views/form.php:24
msgid "OK"
msgstr "OK"

#: includes/modules/seo-analysis/class-result.php:88
msgid "How to fix"
msgstr "Cách khắc phục"

#: includes/modules/seo-analysis/class-seo-analyzer.php:156
#: includes/modules/seo-analysis/views/graphs.php:58
msgid "Failed Tests"
msgstr "Bài kiểm tra thất bại"

#: includes/modules/seo-analysis/class-seo-analyzer.php:148
#: includes/modules/seo-analysis/views/graphs.php:40
msgid "Passed Tests"
msgstr "Bài kiểm tra thành công"

#: includes/modules/seo-analysis/class-seo-analysis.php:76
msgid "SEO Analysis for this page"
msgstr "Phân tích SEO cho trang này"

#: includes/modules/seo-analysis/class-seo-analysis.php:63
msgid "Site-wide analysis"
msgstr "Phân tích toàn trang web"

#: includes/modules/analytics/views/options.php:80
msgid "Fetching in Progress"
msgstr "Đang tải dữ liệu"

#: includes/modules/analytics/views/options.php:80
msgid "Update data manually"
msgstr "Cập nhật dữ liệu thủ công"

#: includes/modules/analytics/class-ajax.php:340
msgid "Google oAuth is not authorized."
msgstr "Google oAuth chưa được ủy quyền."

#: includes/modules/analytics/class-ajax.php:375
msgid "Not a valid settings founds to delete cache."
msgstr "Không tìm thấy cài đặt hợp lệ để xóa bộ nhớ cache."

#: includes/modules/analytics/google/class-request.php:288
#: includes/modules/analytics/google/class-request.php:346
msgid "Bad request. Please check the code."
msgstr "Yêu cầu không hợp lệ. Vui lòng kiểm tra mã."

#: includes/modules/schema/shortcode/service.php:20
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Service Type"
msgstr "Loại dịch vụ"

#: includes/modules/schema/shortcode/softwareapplication.php:41
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Application Category"
msgstr "Danh mục ứng dụng"

#: includes/modules/schema/shortcode/softwareapplication.php:27
msgid "Price Currency"
msgstr "Tiền tệ giá"

#: includes/modules/schema/shortcode/course.php:69 assets/admin/js/blocks.js:1
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Duration"
msgstr "Thời lượng"

#: includes/modules/schema/shortcode/restaurant.php:57
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Menu URL"
msgstr "URL thực đơn"

#: includes/modules/schema/shortcode/restaurant.php:50
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Serves Cuisine"
msgstr "Phục vụ ẩm thực"

#: includes/modules/schema/shortcode/recipe.php:109
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Recipe Ingredients"
msgstr "Nguyên liệu công thức"

#: includes/modules/schema/shortcode/recipe.php:90
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Recipe Video Thumbnail"
msgstr "Hình thu nhỏ video công thức"

#: includes/modules/schema/shortcode/recipe.php:62
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Cooking Time"
msgstr "Thời gian nấu"

#: includes/modules/schema/shortcode/product.php:34
msgid "Product Currency"
msgstr "Tiền tệ sản phẩm"

#: includes/modules/schema/shortcode/product.php:20
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Product SKU"
msgstr "Mã sản phẩm (SKU)"

#: includes/modules/schema/shortcode/jobposting.php:48
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Expiry Posted"
msgstr "Ngày hết hạn đăng"

#: includes/modules/schema/shortcode/jobposting.php:27
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Salary Currency"
msgstr "Tiền tệ lương"

#: includes/modules/schema/shortcode/event.php:144
msgid "Availability Starts"
msgstr "Bắt đầu có sẵn"

#: includes/modules/schema/shortcode/event.php:137
#: includes/opengraph/class-slack.php:195
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Availability"
msgstr "Tình trạng sẵn có"

#: includes/modules/schema/shortcode/event.php:123
msgid "Entry Price"
msgstr "Giá vé vào cửa"

#: includes/modules/schema/shortcode/course.php:62
#: includes/modules/schema/shortcode/event.php:108
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "End Date"
msgstr "Ngày kết thúc"

#: includes/modules/schema/shortcode/event.php:52
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Venue Name"
msgstr "Tên địa điểm"

#: includes/modules/schema/shortcode/course.php:34
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Course Provider URL"
msgstr "URL nhà cung cấp khóa học"

#: includes/modules/schema/shortcode/book.php:35
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Edition"
msgstr "Ấn bản"

#: includes/modules/redirections/views/options.php:42
msgid "Custom Url "
msgstr "URL tùy chỉnh "

#: includes/modules/redirections/views/options.php:31
msgid "Redirect to Homepage"
msgstr "Chuyển hướng đến trang chủ"

#: includes/modules/redirections/views/options.php:18
msgid "Display the Debug Console instead of being redirected. Administrators only."
msgstr "Hiển thị Bảng điều khiển gỡ lỗi thay vì bị chuyển hướng. Chỉ dành cho quản trị viên."

#: includes/modules/redirections/views/options.php:17
msgid "Debug Redirections"
msgstr "Gỡ lỗi chuyển hướng"

#: includes/modules/redirections/views/help-tab-overview.php:17
msgid "Using a 302 <em>temporary</em> redirection is useful when you want to test a new page for client feedback temporarily without affecting the SEO scores of the original page."
msgstr "Sử dụng chuyển hướng <em>tạm thời</em> 302 rất hữu ích khi bạn muốn kiểm tra trang mới để nhận phản hồi của khách hàng tạm thời mà không ảnh hưởng đến điểm SEO của trang gốc."

#: includes/modules/redirections/views/help-tab-overview.php:13
msgid "Here you can set up custom redirections. It is important to choose the right type of redirection."
msgstr "Tại đây, bạn có thể thiết lập chuyển hướng tùy chỉnh. Điều quan trọng là chọn đúng loại chuyển hướng."

#: includes/modules/redirections/views/help-tab-bulk.php:13
msgid "You can also activate, deactivate, or delete multiple items at once using the Bulk Actions dropdown."
msgstr "Bạn cũng có thể kích hoạt, hủy kích hoạt hoặc xóa nhiều mục cùng lúc bằng cách sử dụng menu thả xuống Hành động hàng loạt."

#: includes/modules/redirections/views/help-tab-actions.php:18
msgid "<strong>Delete</strong> permanently removes the redirection."
msgstr "<strong>Xóa</strong> vĩnh viễn chuyển hướng."

#: includes/modules/redirections/views/help-tab-actions.php:17
msgid "<strong>Activate/Deactivate</strong> redirections. Deactivated redirections do not take effect on your site."
msgstr "<strong>Kích hoạt / Hủy kích hoạt</strong> chuyển hướng. Chuyển hướng bị hủy kích hoạt không có hiệu lực trên trang web của bạn."

#: includes/modules/redirections/views/help-tab-overview.php:16
msgid "301 redirections are <em>permanent</em>. The old URL will be removed in search engines and replaced by the new one, passing on SearchRank and other SEO scores. Browsers may also store the new URL in cache and redirect to it even after the redirection is deleted from the list here."
msgstr "Chuyển hướng 301 là <em>vĩnh viễn</em>. URL cũ sẽ bị xóa trong các công cụ tìm kiếm và được thay thế bằng URL mới, chuyển Xếp hạng tìm kiếm và các điểm SEO khác. Trình duyệt cũng có thể lưu trữ URL mới trong bộ nhớ cache và chuyển hướng đến URL đó ngay cả sau khi chuyển hướng bị xóa khỏi danh sách ở đây."

#: includes/modules/redirections/views/help-tab-actions.php:16
msgid "<strong>Edit</strong> redirection details: from/to URLs and the redirection type."
msgstr "<strong>Chỉnh sửa</strong> chi tiết chuyển hướng: từ / đến URL và loại chuyển hướng."

#: includes/modules/redirections/views/debugging.php:53
msgid "<strong>Note:</strong> This interstitial page is displayed only to administrators. Site visitors are redirected without delay."
msgstr "<strong>Lưu ý:</strong> Trang xen kẽ này chỉ hiển thị cho quản trị viên. Khách truy cập trang web được chuyển hướng mà không bị trì hoãn."

#. translators: countdown seconds
#: includes/modules/redirections/views/debugging.php:36
msgid "Redirecting in %s seconds..."
msgstr "Chuyển hướng trong %s giây..."

#: includes/modules/redirections/views/debugging.php:28
msgid " To "
msgstr " Tới "

#: includes/modules/redirections/views/debugging.php:27
msgid "Redirecting from "
msgstr "Chuyển hướng từ "

#. translators: 1. url to new screen, 2. old trashed post permalink
#: includes/modules/redirections/class-watcher.php:360
msgid "<strong>SEO Notice:</strong> A previously published %1$s has been moved to trash. You may redirect <code>%2$s</code> to <a href=\"%3$s\">a new url</a>."
msgstr "<strong>Thông báo SEO:</strong> %1$s đã xuất bản trước đó đã được chuyển vào thùng rác. Bạn có thể chuyển hướng <code>%2$s</code> đến <a href=\"%3$s\">một URL mới</a>."

#. translators: %1$s: post type label, %2$s: edit redirection URL.
#. translators: %1$s: term name, %2$s: edit redirection URL.
#: includes/modules/redirections/class-watcher.php:102
#: includes/modules/redirections/class-watcher.php:154
msgid "SEO Notice: you just changed the slug of a %1$s and Rank Math has automatically created a redirection. You can edit the redirection by <a href=\"%2$s\">clicking here</a>."
msgstr "Thông báo SEO: bạn vừa thay đổi slug của %1$s và Rank Math đã tự động tạo chuyển hướng. Bạn có thể chỉnh sửa chuyển hướng bằng cách <a href=\"%2$s\">nhấp vào đây</a>."

#: includes/modules/redirections/class-table.php:399
msgid "Empty Trash"
msgstr "Xóa thùng rác"

#: includes/modules/redirections/class-table.php:316
msgid "Move to Trash"
msgstr "Chuyển vào thùng rác"

#: includes/modules/redirections/class-table.php:280
msgid "Last Accessed"
msgstr "Truy cập lần cuối"

#: includes/modules/redirections/class-table.php:277
#: includes/modules/schema/shortcode/music.php:27
#: includes/modules/schema/shortcode/recipe.php:20
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Type"
msgstr "Loại"

#: includes/modules/redirections/class-table.php:276
msgid "To"
msgstr "Tới"

#: includes/modules/redirections/class-table.php:254
#: includes/modules/redirections/class-table.php:342
msgid "Trash"
msgstr "Thùng rác"

#: includes/modules/redirections/class-table.php:233
#: includes/modules/redirections/class-table.php:310
msgid "Delete Permanently"
msgstr "Xóa vĩnh viễn"

#: includes/modules/redirections/class-table.php:170
#: includes/modules/sitemap/class-admin.php:345
#: includes/modules/schema/blocks/toc/assets/js/index.js:1
#: includes/modules/schema/blocks/toc/assets/src/list.js:81
msgid "Hide"
msgstr "Ẩn"

#: includes/modules/redirections/class-table.php:160
msgid "Show more"
msgstr "Hiển thị thêm"

#: includes/modules/redirections/class-table.php:37
msgid "No redirections added yet. <a href=\"#\" class=\"rank-math-add-new-redirection\">Add New Redirection</a>"
msgstr "Chưa có chuyển hướng nào được thêm. <a href=\"#\" class=\"rank-math-add-new-redirection\">Thêm chuyển hướng mới</a>"

#: includes/modules/redirections/class-metabox.php:114
#: includes/modules/redirections/class-metabox.php:118
msgid "New redirection created."
msgstr "Chuyển hướng mới đã được tạo."

#: includes/modules/redirections/class-table.php:253
#: includes/modules/redirections/class-table.php:314
#: includes/modules/redirections/assets/js/redirections.js:1
msgid "Activate"
msgstr "Kích hoạt"

#. translators: delete counter
#: includes/modules/redirections/class-admin.php:442
msgid "%d redirection(s) successfully deleted."
msgstr "Đã xóa thành công %d chuyển hướng."

#: includes/modules/redirections/class-admin.php:429
msgid "Redirection successfully moved to Trash."
msgstr "Đã di chuyển chuyển hướng vào Thùng rác thành công."

#: includes/modules/redirections/class-admin.php:428
msgid "Redirection successfully deactivated."
msgstr "Đã hủy kích hoạt chuyển hướng thành công."

#: includes/modules/redirections/class-admin.php:427
msgid "Redirection successfully activated."
msgstr "Đã kích hoạt chuyển hướng thành công."

#: includes/modules/local-seo/views/titles-options.php:331
msgid "Latitude and longitude values separated by comma."
msgstr "Giá trị vĩ độ và kinh độ được phân cách bằng dấu phẩy."

#. translators: %s expands to "Google Maps Embed API"
#. https://developers.google.com/maps/documentation/embed
#: includes/modules/local-seo/views/titles-options.php:320
msgid "Google Maps Embed API"
msgstr "API nhúng Google Maps"

#. translators: %s expands to "Google Maps Embed API"
#. https://developers.google.com/maps/documentation/embed
#: includes/modules/local-seo/views/titles-options.php:320
msgid "An API Key is required to display embedded Google Maps on your site. Get it here: %s"
msgstr "Bạn cần có Khóa API để hiển thị Google Maps được nhúng trên trang web của mình. Nhận tại đây: %s"

#: includes/modules/local-seo/views/titles-options.php:318
msgid "Google Maps API Key"
msgstr "Khóa API Google Maps"

#: includes/modules/local-seo/views/titles-options.php:308
msgid "Contact Page"
msgstr "Trang liên hệ"

#: includes/modules/local-seo/views/titles-options.php:291
#: includes/modules/local-seo/views/titles-options.php:309
msgid "Select a page on your site where you want to show the LocalBusiness meta data."
msgstr "Chọn một trang trên trang web của bạn mà bạn muốn hiển thị dữ liệu meta LocalBusiness."

#: includes/modules/local-seo/views/titles-options.php:240
msgid "The price range of the business, for example $$$."
msgstr "Phạm vi giá của doanh nghiệp, ví dụ: $$$."

#: includes/modules/local-seo/views/titles-options.php:231
msgid "Format: +1-401-555-1212"
msgstr "Định dạng: +1-401-555-1212"

#: includes/helpers/class-choices.php:615
msgid "Package Tracking"
msgstr "Theo dõi gói hàng"

#: includes/helpers/class-choices.php:614
msgid "Roadside Assistance"
msgstr "Hỗ trợ trên đường"

#: includes/helpers/class-choices.php:610
msgid "Reservations"
msgstr "Đặt chỗ"

#: includes/modules/local-seo/views/titles-options.php:196
msgid "e.g. 09:00-17:00"
msgstr "ví dụ: 09: 00-17: 00"

#: includes/modules/local-seo/views/titles-options.php:186
msgid "Sunday"
msgstr "Chủ nhật"

#: includes/modules/local-seo/views/titles-options.php:182
msgid "Wednesday"
msgstr "Thứ tư"

#: includes/modules/local-seo/views/titles-options.php:180
msgid "Monday"
msgstr "Thứ hai"

#: includes/modules/local-seo/views/titles-options.php:164
msgid "Select opening hours. You can add multiple sets if you have different opening or closing hours on some days or if you have a mid-day break. Times are specified using 24:00 time."
msgstr "Chọn giờ mở cửa. Bạn có thể thêm nhiều bộ nếu bạn có giờ mở cửa hoặc đóng cửa khác nhau vào một số ngày hoặc nếu bạn có thời gian nghỉ trưa. Thời gian được chỉ định bằng cách sử dụng thời gian 24:00."

#: includes/modules/local-seo/views/titles-options.php:122
msgid "Format used when the address is displayed using the <code>[rank_math_contact_info]</code> shortcode.<br><strong>Available Tags: {address}, {locality}, {region}, {postalcode}, {country}, {gps}</strong>"
msgstr "Định dạng được sử dụng khi địa chỉ được hiển thị bằng cách sử dụng mã ngắn <code>[rank_math_contact_info]</code>.<br><strong>Thẻ có sẵn: {address}, {locality}, {region}, {postalcode}, {country}, {gps}</strong>"

#: includes/modules/local-seo/views/titles-options.php:104
#: includes/modules/local-seo/views/titles-options.php:206
msgid "Search engines may prominently display your contact phone number for mobile users."
msgstr "Các công cụ tìm kiếm có thể hiển thị nổi bật số điện thoại liên lạc của bạn cho người dùng di động."

#: includes/modules/local-seo/views/titles-options.php:94
#: includes/modules/schema/shortcode/person.php:20
#: includes/modules/content-ai/assets/js/content-ai-page.js:1
#: includes/modules/content-ai/assets/js/content-ai.js:1
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Email"
msgstr "Email"

#: includes/settings/titles/local.php:69
msgid "URL of the item."
msgstr "URL của mục."

#: includes/modules/links/class-links.php:113
#: includes/modules/analytics/assets/js/stats.js:1
msgid "External Links"
msgstr "Liên kết ngoài"

#: includes/modules/links/class-links.php:107
msgid "Links: "
msgstr "Liên kết: "

#: includes/modules/404-monitor/views/options.php:86
msgid "Turn ON to ignore all query parameters (the part after a question mark in a URL) when logging 404 errors."
msgstr "Bật BẬT để bỏ qua tất cả các tham số truy vấn (phần sau dấu hỏi trong URL) khi ghi nhật ký lỗi 404."

#: includes/modules/404-monitor/views/options.php:85
msgid "Ignore Query Parameters"
msgstr "Bỏ qua tham số truy vấn"

#: includes/modules/404-monitor/views/options.php:58
#: includes/modules/local-seo/views/titles-options.php:167
#: includes/modules/local-seo/views/titles-options.php:209
#: includes/modules/local-seo/views/titles-options.php:253
#: includes/modules/redirections/assets/js/redirections.js:1
msgid "Remove"
msgstr "Gỡ bỏ"

#: includes/modules/404-monitor/views/options.php:57
#: includes/modules/redirections/assets/js/redirections.js:1
msgid "Add another"
msgstr "Thêm khác"

#: includes/modules/404-monitor/views/options.php:55
msgid "Enter URIs or keywords you wish to prevent from getting logged by the 404 monitor."
msgstr "Nhập URI hoặc từ khóa bạn muốn ngăn không cho bị ghi nhật ký bởi trình theo dõi 404."

#: includes/modules/404-monitor/views/options.php:54
msgid "Exclude Paths"
msgstr "Loại trừ đường dẫn"

#: includes/modules/404-monitor/views/options.php:44
msgid "Sets the max number of rows in a log. Set to 0 to disable the limit."
msgstr "Đặt số lượng hàng tối đa trong nhật ký. Đặt thành 0 để tắt giới hạn."

#: includes/modules/404-monitor/views/options.php:20
msgid "If you have hundreds of 404 errors, your error log might increase quickly. Only choose this option if you have a very few 404s and are unable to replicate the 404 error on a particular URL from your end."
msgstr "Nếu bạn có hàng trăm lỗi 404, nhật ký lỗi của bạn có thể tăng lên nhanh chóng. Chỉ chọn tùy chọn này nếu bạn có rất ít lỗi 404 và không thể sao chép lỗi 404 trên một URL cụ thể từ phía bạn."

#: includes/modules/404-monitor/views/help-tab-screen-content.php:19
#: includes/modules/redirections/views/help-tab-screen-content.php:19
msgid "You can reorder the list by clicking on the column headings. "
msgstr "Bạn có thể sắp xếp lại danh sách bằng cách nhấp vào tiêu đề cột. "

#: includes/modules/404-monitor/views/help-tab-screen-content.php:17
#: includes/modules/redirections/views/help-tab-screen-content.php:17
msgid "You can decide how many items to list per screen using the Screen Options tab."
msgstr "Bạn có thể quyết định số lượng mục hiển thị trên mỗi màn hình bằng cách sử dụng tab Tùy chọn màn hình."

#: includes/modules/404-monitor/views/help-tab-screen-content.php:13
#: includes/modules/redirections/views/help-tab-screen-content.php:13
msgid "You can customize the display of this screen's contents in a number of ways:"
msgstr "Bạn có thể tùy chỉnh cách hiển thị nội dung của màn hình này theo nhiều cách:"

#: includes/modules/404-monitor/views/help-tab-actions.php:18
msgid "<strong>Delete</strong> permanently removes the item from the list."
msgstr "<strong>Xóa</strong> vĩnh viễn mục khỏi danh sách."

#: includes/modules/404-monitor/views/help-tab-actions.php:17
msgid "<strong>Redirect</strong> takes you to the Redirections manager to redirect the 404 URL."
msgstr "<strong>Chuyển hướng</strong> đưa bạn đến trình quản lý Chuyển hướng để chuyển hướng URL 404."

#: includes/modules/404-monitor/views/help-tab-actions.php:16
msgid "<strong>View Details</strong> shows details about the 404 requests."
msgstr "<strong>Xem chi tiết</strong> hiển thị chi tiết về các yêu cầu 404."

#: includes/modules/404-monitor/views/help-tab-actions.php:13
#: includes/modules/redirections/views/help-tab-actions.php:13
msgid "Hovering over a row in the list will display action links that allow you to manage the item. You can perform the following actions:"
msgstr "Di chuột qua một hàng trong danh sách sẽ hiển thị các liên kết hành động cho phép bạn quản lý mục. Bạn có thể thực hiện các hành động sau:"

#: includes/modules/404-monitor/class-table.php:226
msgid "User-Agent"
msgstr "Trình duyệt người dùng"

#: includes/modules/404-monitor/class-table.php:225
msgid "Referer"
msgstr "Liên kết giới thiệu"

#: includes/modules/404-monitor/class-table.php:87
msgid "Clear Log"
msgstr "Xóa nhật ký"

#: includes/modules/404-monitor/class-monitor.php:120
msgid "Log item successfully deleted."
msgstr "Đã xóa mục nhật ký thành công."

#: includes/modules/404-monitor/class-monitor.php:116
msgid "No valid id found."
msgstr "Không tìm thấy ID hợp lệ."

#: includes/modules/404-monitor/class-monitor.php:99
msgid "Review 404 errors on your site"
msgstr "Xem xét các lỗi 404 trên trang web của bạn"

#: includes/modules/404-monitor/class-admin.php:178
msgid "Are you sure you wish to delete all 404 error logs?"
msgstr "Bạn có chắc chắn muốn xóa tất cả nhật ký lỗi 404?"

#: includes/modules/404-monitor/class-admin.php:163
#: includes/modules/redirections/class-admin.php:174
msgid "Bulk Actions"
msgstr "Hành động hàng loạt"

#: includes/modules/404-monitor/class-admin.php:159
#: includes/modules/redirections/class-admin.php:170
msgid "Available Actions"
msgstr "Các hành động có sẵn"

#: includes/modules/404-monitor/class-admin.php:151
#: includes/modules/redirections/class-admin.php:162
msgid "Overview"
msgstr "Tổng quan"

#. translators: delete counter
#: includes/modules/404-monitor/class-admin.php:128
msgid "Log cleared - %d items deleted."
msgstr "Đã xóa nhật ký - %d mục đã bị xóa."

#. translators: delete counter
#: includes/modules/404-monitor/class-admin.php:112
msgid "%d log(s) deleted."
msgstr "Đã xóa %d nhật ký."

#: includes/settings/titles/global.php:100 assets/admin/js/rank-math-app.js:1
msgid "Summary Card with Large Image"
msgstr "Thẻ tóm tắt với hình ảnh lớn"

#: includes/settings/titles/global.php:17 assets/admin/js/rank-math-app.js:1
msgid "Robots Meta"
msgstr "Siêu dữ liệu Robots"

#: includes/modules/role-manager/class-capability-manager.php:71
msgid "Top Admin Bar"
msgstr "Thanh quản trị trên cùng"

#: includes/modules/role-manager/class-capability-manager.php:69
msgid "On-Page Social Settings"
msgstr "Cài đặt mạng xã hội trên trang"

#: includes/modules/role-manager/class-capability-manager.php:64
msgid "Site-Wide Analysis"
msgstr "Phân tích toàn trang web"

#: includes/modules/role-manager/class-capability-manager.php:59
msgid "404 Monitor Log"
msgstr "Nhật ký giám sát lỗi 404"

#: includes/modules/role-manager/class-capability-manager.php:56
msgid "Titles & Meta Settings"
msgstr "Cài đặt tiêu đề & siêu dữ liệu"

#: includes/admin/class-admin-helper.php:352 assets/admin/js/components.js:1
#: includes/modules/content-ai/assets/js/content-ai-page.js:1
#: includes/modules/content-ai/assets/js/content-ai.js:1
msgid "Tweet"
msgstr "Đăng lên Twitter"

#. translators: sitename
#: includes/admin/class-admin-helper.php:331 assets/admin/js/components.js:1
msgid "I just installed Rank Math SEO WordPress Plugin. It looks promising!"
msgstr "Tôi vừa cài đặt Plugin WordPress Rank Math SEO. Trông nó có vẻ đầy hứa hẹn!"

#. translators: sitename
#: includes/admin/class-admin-helper.php:329 assets/admin/js/components.js:1
msgid "I just installed @RankMathSEO #WordPress Plugin. It looks great! %s"
msgstr "Tôi vừa cài đặt Plugin @RankMathSEO #WordPress. Trông nó thật tuyệt vời! %s"

#: includes/helpers/class-choices.php:498
#: includes/modules/redirections/assets/js/redirections.js:1
msgid "Regex"
msgstr "Biểu thức chính quy"

#: includes/helpers/class-choices.php:497
#: includes/modules/redirections/assets/js/redirections.js:1
msgid "End With"
msgstr "Kết thúc bằng"

#: includes/helpers/class-choices.php:496
#: includes/modules/redirections/assets/js/redirections.js:1
msgid "Starts With"
msgstr "Bắt đầu bằng"

#: includes/helpers/class-choices.php:495
#: includes/modules/redirections/assets/js/redirections.js:1
msgid "Contains"
msgstr "Chứa"

#: includes/helpers/class-choices.php:480 assets/admin/js/rank-math-app.js:1
#: includes/modules/redirections/assets/js/redirections.js:1
msgid "410 Content Deleted"
msgstr "410 Nội dung đã bị xóa"

#: includes/helpers/class-choices.php:478 assets/admin/js/rank-math-app.js:1
#: includes/modules/redirections/assets/js/redirections.js:1
msgid "302 Temporary Move"
msgstr "302 Di chuyển tạm thời"

#: includes/helpers/class-choices.php:446
#: includes/modules/local-seo/views/titles-options.php:23
#: includes/settings/titles/local.php:17
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Person"
msgstr "Người"

#: includes/helpers/class-choices.php:443
#: includes/modules/content-ai/assets/js/content-ai-page.js:1
#: includes/modules/content-ai/assets/js/content-ai.js:1
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Recipe"
msgstr "Công thức"

#: includes/helpers/class-choices.php:439
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Event"
msgstr "Sự kiện"

#: includes/helpers/class-choices.php:438
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Course"
msgstr "Khóa học"

#: includes/helpers/class-choices.php:143
msgid "Any"
msgstr "Bất kỳ"

#: includes/helpers/class-choices.php:78 assets/admin/js/rank-math-app.js:1
msgid "Prevents a snippet from being shown in the search results"
msgstr "Ngăn đoạn trích xuất hiển thị trong kết quả tìm kiếm"

#: includes/helpers/class-choices.php:78 assets/admin/js/rank-math-app.js:1
msgid "No Snippet"
msgstr "Không có đoạn trích xuất"

#: includes/helpers/class-choices.php:77 assets/admin/js/rank-math-app.js:1
msgid "No Image Index"
msgstr "Không lập chỉ mục hình ảnh"

#: includes/helpers/class-choices.php:76 assets/admin/js/rank-math-app.js:1
msgid "Prevents search engines from showing Cached links for pages"
msgstr "Ngăn công cụ tìm kiếm hiển thị các liên kết được lưu trong bộ nhớ cache cho các trang"

#: includes/helpers/class-choices.php:76 assets/admin/js/rank-math-app.js:1
msgid "No Archive"
msgstr "Không lưu trữ"

#: includes/helpers/class-choices.php:75 assets/admin/js/rank-math-app.js:1
msgid "Prevents search engines from following links on the pages"
msgstr "Ngăn công cụ tìm kiếm theo dõi các liên kết trên trang"

#: includes/helpers/class-choices.php:75
msgid "No Follow"
msgstr "Không theo dõi"

#: includes/helpers/class-choices.php:74 assets/admin/js/rank-math-app.js:1
msgid "Prevents pages from being indexed and displayed in search engine result pages"
msgstr "Ngăn các trang bị lập chỉ mục và hiển thị trong các trang kết quả của công cụ tìm kiếm"

#: includes/admin/class-post-columns.php:218
#: includes/helpers/class-choices.php:74 assets/admin/js/rank-math-app.js:1
msgid "No Index"
msgstr "Không lập chỉ mục"

#: includes/helpers/class-choices.php:44
msgid "Play icon"
msgstr "Biểu tượng phát"

#: includes/admin/class-option-center.php:400
msgid ".htaccess file updated successfully."
msgstr "Tệp .htaccess đã được cập nhật thành công."

#: includes/admin/class-option-center.php:394
msgid "Failed to update .htaccess file. Please check file permissions."
msgstr "Không thể cập nhật tệp .htaccess. Vui lòng kiểm tra quyền của tệp."

#: includes/admin/class-option-center.php:387
msgid "Failed to backup .htaccess file. Please check file permissions."
msgstr "Không thể sao lưu tệp .htaccess. Vui lòng kiểm tra quyền của tệp."

#: includes/modules/robots-txt/class-robots-txt.php:72
msgid "Leave the field empty to let WordPress handle the contents dynamically. If an actual robots.txt file is present in the root folder of your site, this option won't take effect and you have to edit the file directly, or delete it and then edit from here."
msgstr "Để trống trường để WordPress xử lý nội dung một cách linh hoạt. Nếu tệp robots.txt thực tế hiện diện trong thư mục gốc của trang web của bạn, tùy chọn này sẽ không có hiệu lực và bạn phải chỉnh sửa tệp trực tiếp hoặc xóa tệp đó rồi chỉnh sửa từ đây."

#: includes/admin/class-option-center.php:305
msgid "Post Formats Archive"
msgstr "Lưu trữ định dạng bài viết"

#: includes/replace-variables/class-basic-variables.php:81
msgid "Starts at 1 and increments by 1."
msgstr "Bắt đầu từ 1 và tăng dần lên 1."

#: includes/replace-variables/class-basic-variables.php:80
msgid "Counter"
msgstr "Bộ đếm"

#: includes/replace-variables/class-basic-variables.php:202
msgid "Current server time with custom formatting pattern."
msgstr "Giờ máy chủ hiện tại với mẫu định dạng tùy chỉnh."

#: includes/replace-variables/class-advanced-variables.php:138
msgid "Products"
msgstr "Sản phẩm"

#: includes/replace-variables/class-basic-variables.php:175
msgid "Current server year"
msgstr "Năm hiện tại của máy chủ"

#: includes/replace-variables/class-basic-variables.php:174
msgid "Current Year"
msgstr "Năm hiện tại"

#: includes/replace-variables/class-basic-variables.php:164
msgid "Current server month"
msgstr "Tháng hiện tại của máy chủ"

#: includes/replace-variables/class-basic-variables.php:163
msgid "Current Month"
msgstr "Tháng hiện tại"

#: includes/replace-variables/class-basic-variables.php:153
msgid "Current server day"
msgstr "Ngày hiện tại của máy chủ"

#: includes/replace-variables/class-basic-variables.php:142
msgid "Current server date"
msgstr "Ngày hiện tại của máy chủ"

#: includes/replace-variables/class-basic-variables.php:141
msgid "Current Date"
msgstr "Ngày hiện tại"

#: includes/replace-variables/class-basic-variables.php:191
msgid "Current server time"
msgstr "Giờ hiện tại của máy chủ"

#: includes/replace-variables/class-basic-variables.php:190
msgid "Current Time"
msgstr "Giờ hiện tại"

#: includes/replace-variables/class-advanced-variables.php:87
msgid "Page number with context (i.e. page 2 of 4). Only displayed on page 2 and above."
msgstr "Số trang có ngữ cảnh (ví dụ: trang 2 trong số 4). Chỉ hiển thị trên trang 2 trở lên."

#: includes/replace-variables/class-advanced-variables.php:33
msgid "ID of the current post/page"
msgstr "ID của bài viết/trang hiện tại"

#: includes/modules/sitemap/settings/html-sitemap.php:103
#: includes/replace-variables/class-advanced-variables.php:32
#: includes/replace-variables/class-advanced-variables.php:35
msgid "Post ID"
msgstr "ID bài viết"

#: includes/replace-variables/class-basic-variables.php:70
msgid "Search query (only available on search results page)"
msgstr "Truy vấn tìm kiếm (chỉ khả dụng trên trang kết quả tìm kiếm)"

#: includes/replace-variables/class-term-variables.php:51
msgid "Example Term Description"
msgstr "Mô tả thuật ngữ ví dụ"

#: includes/replace-variables/class-term-variables.php:39
msgid "Example Term"
msgstr "Thuật ngữ ví dụ"

#: includes/replace-variables/class-term-variables.php:36
msgid "Current Term"
msgstr "Thuật ngữ hiện tại"

#: includes/replace-variables/class-post-variables.php:204
#: includes/replace-variables/class-post-variables.php:215
msgid "Example Category 1, Example Category 2"
msgstr "Danh mục ví dụ 1, Danh mục ví dụ 2"

#: includes/replace-variables/class-post-variables.php:201
msgid "Post Categories"
msgstr "Danh mục bài viết"

#: includes/replace-variables/class-post-variables.php:193
msgid "Example Category"
msgstr "Danh mục ví dụ"

#: includes/replace-variables/class-post-variables.php:190
msgid "Post Category"
msgstr "Danh mục bài viết"

#: includes/replace-variables/class-post-variables.php:253
msgid "Post Tags"
msgstr "Thẻ bài viết"

#: includes/replace-variables/class-post-variables.php:244
msgid "Example Tag"
msgstr "Thẻ ví dụ"

#: includes/replace-variables/class-post-variables.php:241
msgid "Post Tag"
msgstr "Thẻ bài viết"

#: includes/replace-variables/class-post-variables.php:57
#: includes/replace-variables/class-post-variables.php:69
msgid "Post Excerpt"
msgstr "Đoạn trích bài viết"

#: includes/replace-variables/class-post-variables.php:150
msgid "Last modification date of the current post/page"
msgstr "Ngày sửa đổi lần cuối của bài viết/trang hiện tại"

#: includes/replace-variables/class-post-variables.php:138
msgid "Publication date of the current post/page <strong>OR</strong> specified date on date archives"
msgstr "Ngày xuất bản của bài viết/trang hiện tại <strong>HOẶC</strong> ngày được chỉ định trên kho lưu trữ ngày"

#: includes/replace-variables/class-basic-variables.php:59
msgid "Separator character, as set in the Title Settings"
msgstr "Ký tự phân cách, như được đặt trong Cài đặt tiêu đề"

#: includes/replace-variables/class-basic-variables.php:58
#: includes/settings/general/breadcrumbs.php:37
#: includes/settings/titles/global.php:51
msgid "Separator Character"
msgstr "Ký tự phân cách"

#: includes/replace-variables/class-post-variables.php:47
msgid "Title of the parent page of the current post/page"
msgstr "Tiêu đề của trang cha của bài viết/trang hiện tại"

#: includes/replace-variables/class-post-variables.php:46
msgid "Post Title of parent page"
msgstr "Tiêu đề bài viết của trang cha"

#: includes/replace-variables/class-post-variables.php:35
msgid "Title of the current post/page"
msgstr "Tiêu đề của bài viết/trang hiện tại"

#: includes/replace-variables/class-post-variables.php:34
#: includes/modules/content-ai/assets/js/content-ai-page.js:1
#: includes/modules/content-ai/assets/js/content-ai.js:1
msgid "Post Title"
msgstr "Tiêu đề bài viết"

#. translators: %1$d: current page number, %2$d: max pages.
#: includes/replace-variables/class-advanced-variables.php:210
msgid "Page %1$d of %2$d"
msgstr "Trang %1$d trên %2$d"

#: rank-math.php:541
msgid "Once Weekly"
msgstr "Mỗi tuần một lần"

#: includes/module/class-base.php:147
msgid "Items per page"
msgstr "Mục trên mỗi trang"

#: includes/modules/redirections/class-table.php:340
msgid "Active"
msgstr "Đang hoạt động"

#: includes/module/class-manager.php:151
msgid "Link Counter"
msgstr "Bộ đếm liên kết"

#: includes/module/class-manager.php:245
msgid "Let Rank Math analyze your website and your website's content using 28+ different tests to provide tailor-made SEO Analysis to you."
msgstr "Hãy để Rank Math phân tích trang web và nội dung trang web của bạn bằng cách sử dụng hơn 28 bài kiểm tra khác nhau để cung cấp cho bạn Phân tích SEO được điều chỉnh riêng."

#: includes/module/class-manager.php:135
msgid "Enable support for the structured data, which adds Schema code in your website, resulting in rich search results, better CTR and more traffic."
msgstr "Bật hỗ trợ cho dữ liệu có cấu trúc, thêm mã Schema vào trang web của bạn, dẫn đến kết quả tìm kiếm phong phú, CTR tốt hơn và lưu lượng truy cập nhiều hơn."

#: includes/module/class-manager.php:108
msgid "Records the URLs on which visitors & search engines run into 404 Errors. You can also turn on Redirections to redirect the error causing URLs to other URLs."
msgstr "Ghi lại các URL mà khách truy cập và công cụ tìm kiếm gặp phải Lỗi 404. Bạn cũng có thể bật Chuyển hướng để chuyển hướng các URL gây ra lỗi sang các URL khác."

#: includes/class-installer.php:346
#: includes/settings/general/breadcrumbs.php:121
msgid "404 Error: page not found"
msgstr "Lỗi 404: Không tìm thấy trang"

#: includes/class-installer.php:341
#: includes/settings/general/breadcrumbs.php:65
msgid "Home"
msgstr "Trang chủ"

#: includes/frontend/class-head.php:420
msgid "Rank Math WordPress SEO plugin"
msgstr "Plugin SEO WordPress Rank Math"

#: includes/admin/class-admin-bar-menu.php:385
msgid "Google PageSpeed Insights"
msgstr "Google PageSpeed ​​Insights"

#: includes/admin/class-admin-bar-menu.php:358
msgid "As NoFollow"
msgstr "Dưới dạng NoFollow"

#: includes/admin/class-admin-bar-menu.php:347
msgid "As NoIndex"
msgstr "Dưới dạng NoIndex"

#: includes/admin/class-admin-bar-menu.php:286
msgid "SEO Settings for Date Archives"
msgstr "Cài đặt SEO cho Lưu trữ Ngày"

#: includes/admin/class-admin-bar-menu.php:220
msgid "Homepage SEO"
msgstr "SEO Trang chủ"

#. translators: deactivation link
#: includes/admin/watcher/class-watcher.php:159
msgid "Please keep only one Sitemap plugin active, otherwise, you might lose your rankings and traffic. %s."
msgstr "Vui lòng chỉ giữ một plugin Sơ đồ trang web hoạt động, nếu không, bạn có thể mất thứ hạng và lưu lượng truy cập. %s."

#. translators: deactivation link
#: includes/admin/watcher/class-watcher.php:152
msgid "Please keep only one SEO plugin active, otherwise, you might lose your rankings and traffic. %s."
msgstr "Vui lòng chỉ giữ một plugin SEO hoạt động, nếu không, bạn có thể mất thứ hạng và lưu lượng truy cập. %s."

#: includes/admin/wizard/views/your-site.php:51
msgid "Type here to search..."
msgstr "Nhập vào đây để tìm kiếm..."

#: includes/admin/wizard/views/your-site.php:50
msgid "Search the Knowledge Base for answers to your questions:"
msgstr "Tìm kiếm trong Cơ sở Kiến thức để có câu trả lời cho câu hỏi của bạn:"

#: includes/admin/wizard/views/your-site.php:45
msgid "Click here to learn how to setup Rank Math properly"
msgstr "Nhấn vào đây để tìm hiểu cách thiết lập Rank Math đúng cách"

#: includes/admin/wizard/views/your-site.php:40
msgid "Knowledge Base"
msgstr "Cơ sở kiến ​​thức"

#. translators: sitename
#: includes/admin/wizard/views/your-site.php:19
msgid "Your Website: %s"
msgstr "Trang web của bạn: %s"

#: includes/admin/wizard/views/ready.php:61
msgid "Get 24x7 Support"
msgstr "Nhận Hỗ trợ 24/7"

#: includes/admin/wizard/views/ready.php:55
msgid "Join FREE Facebook Group"
msgstr "Tham gia Nhóm Facebook MIỄN PHÍ"

#. translators: Link to How to Optimization KB article
#: includes/admin/wizard/class-optimization.php:37
msgid "Automate some of your SEO tasks like making external links nofollow, redirecting attachment pages, etc. %s"
msgstr "Tự động hóa một số tác vụ SEO của bạn như đặt liên kết ngoài là nofollow, chuyển hướng các trang tệp đính kèm, v.v. %s"

#: includes/admin/wizard/class-import.php:51
msgid "Start Import"
msgstr "Bắt đầu Nhập"

#: includes/admin/wizard/class-import.php:49
msgid "Skip, Don't Import Now"
msgstr "Bỏ qua, Không Nhập Ngay Bây Giờ"

#: includes/admin/wizard/class-import.php:49
msgid "Deactivating Plugins..."
msgstr "Đang hủy kích hoạt Plugin..."

#: includes/admin/wizard/class-import.php:45
msgid "Completed"
msgstr "Đã hoàn thành"

#: includes/admin/wizard/class-import.php:44
msgid "Importing: "
msgstr "Đang nhập: "

#: includes/admin/wizard/class-import.php:33
msgid "Import SEO Settings"
msgstr "Nhập Cài đặt SEO"

#: includes/admin/wizard/views/compatibility.php:176
msgid "Start Wizard"
msgstr "Bắt đầu Trình hướng dẫn"

#: includes/admin/wizard/views/compatibility.php:167
msgid "No known conflicting plugins found."
msgstr "Không tìm thấy plugin xung đột nào."

#: includes/admin/wizard/views/compatibility.php:158
msgid "Deactivate Plugin"
msgstr "Hủy kích hoạt Plugin"

#: includes/admin/wizard/views/compatibility.php:150
msgid "The following active plugins on your site may cause conflict issues when used alongside this plugin: "
msgstr "Các plugin đang hoạt động sau trên trang web của bạn có thể gây ra sự cố xung đột khi được sử dụng cùng với plugin này: "

#: includes/admin/wizard/views/compatibility.php:149
msgid "The following active plugins on your site may cause conflict issues when used alongside Rank Math: "
msgstr "Các plugin đang hoạt động sau trên trang web của bạn có thể gây ra sự cố xung đột khi được sử dụng cùng với Rank Math: "

#: includes/admin/wizard/views/compatibility.php:134
msgid "Please resolve the issues above to be able to use all SEO features. If you are not sure how to do it, please contact your hosting provider."
msgstr "Vui lòng giải quyết các vấn đề trên để có thể sử dụng tất cả các tính năng SEO. Nếu bạn không chắc chắn cách thực hiện, vui lòng liên hệ với nhà cung cấp dịch vụ lưu trữ của bạn."

#: includes/admin/wizard/views/compatibility.php:133
msgid "Please resolve the issues above to be able to use all features of Rank Math plugin. If you are not sure how to do it, please contact your hosting provider."
msgstr "Vui lòng giải quyết các vấn đề trên để có thể sử dụng tất cả các tính năng của plugin Rank Math. Nếu bạn không chắc chắn cách thực hiện, vui lòng liên hệ với nhà cung cấp dịch vụ lưu trữ của bạn."

#: includes/admin/wizard/views/compatibility.php:126
msgid "Your server is correctly configured to use this plugin."
msgstr "Máy chủ của bạn đã được cấu hình chính xác để sử dụng plugin này."

#: includes/admin/wizard/views/compatibility.php:125
msgid "Your server is correctly configured to use Rank Math."
msgstr "Máy chủ của bạn đã được cấu hình chính xác để sử dụng Rank Math."

#: includes/admin/wizard/views/compatibility.php:92
msgid "PHP SimpleXML Extension missing"
msgstr "Thiếu tiện ích mở rộng PHP SimpleXML"

#: includes/admin/wizard/views/compatibility.php:92
msgid "PHP SimpleXML Extension installed"
msgstr "Đã cài đặt Tiện ích mở rộng PHP SimpleXML"

#: includes/admin/wizard/views/compatibility.php:86
msgid "PHP DOM Extension missing"
msgstr "Thiếu tiện ích mở rộng PHP DOM"

#: includes/admin/wizard/views/compatibility.php:86
msgid "PHP DOM Extension installed"
msgstr "Đã cài đặt Tiện ích mở rộng PHP DOM"

#: includes/admin/wizard/views/compatibility.php:40
msgid "Your website is compatible to run Rank Math SEO"
msgstr "Trang web của bạn tương thích để chạy Rank Math SEO"

#: includes/admin/class-registration.php:288
msgid "Connect Your Account"
msgstr "Kết nối Tài khoản của bạn"

#. translators: variables used to wrap the text in the strong tag.
#: includes/admin/views/plugin-activation.php:45
msgid "You have successfully activated Rank Math. If you find the plugin useful, %1$s feel free to recommend it to your friends or colleagues %2$s."
msgstr "Bạn đã kích hoạt Rank Math thành công. Nếu bạn thấy plugin này hữu ích, %1$s hãy giới thiệu nó cho bạn bè hoặc đồng nghiệp của bạn %2$s."

#: includes/admin/views/import-export/plugins-panel.php:33
msgid "No plugin detected with importable data."
msgstr "Không tìm thấy plugin nào có dữ liệu có thể nhập."

#. translators: Link to learn about import export panel KB article
#: includes/admin/views/import-export/import-export-panel.php:19
#: includes/admin/views/import-export/plugins-panel.php:25
msgid "Learn more about the Import/Export options."
msgstr "Tìm hiểu thêm về các tùy chọn Nhập/Xuất."

#: includes/admin/views/import-export/import-export-panel.php:40
msgid "Settings File"
msgstr "Tệp Cài đặt"

#: includes/admin/views/import-export/import-export-panel.php:68
#: includes/modules/redirections/class-import-export.php:109
msgid "Export"
msgstr "Xuất"

#: includes/admin/views/import-export/import-export-panel.php:60
msgid "Role Manager Settings"
msgstr "Cài đặt Trình quản lý Vai trò"

#: includes/admin/views/import-export/backup-panel.php:33
#: includes/admin/views/import-export/backup-panel.php:43
#: includes/modules/404-monitor/class-table.php:156
#: includes/modules/404-monitor/class-table.php:276
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Delete"
msgstr "Xóa"

#: includes/admin/views/import-export/backup-panel.php:13
msgid "Create Backup"
msgstr "Tạo bản sao lưu"

#: includes/admin/views/dashboard-help.php:95
msgid "Online Documentation"
msgstr "Tài liệu trực tuyến"

#: includes/admin/views/dashboard-help.php:61
msgid "How to Import Data from Your Previous SEO Plugin"
msgstr "Cách nhập dữ liệu từ plugin SEO trước đó của bạn"

#: includes/admin/views/dashboard-help.php:50
msgid "How to Properly Setup Rank Math"
msgstr "Cách thiết lập Rank Math đúng cách"

#: includes/admin/views/dashboard-help.php:49
msgid "Setup Rank Math"
msgstr "Thiết lập Rank Math"

#: includes/admin/importers/class-wp-schema-pro.php:600
msgid "Plugin settings and site-wide meta data."
msgstr "Cài đặt plugin và siêu dữ liệu toàn trang web."

#: includes/admin/importers/class-aioseo.php:67
msgid "Import AIO SEO plugin settings, global meta, sitemap settings, etc."
msgstr "Nhập cài đặt plugin AIO SEO, siêu dữ liệu chung, cài đặt sơ đồ trang web, v.v."

#: includes/admin/importers/class-status.php:130
msgid "There are no redirection to import."
msgstr "Không có chuyển hướng nào để nhập."

#: includes/admin/importers/class-status.php:129
msgid "User meta import failed."
msgstr "Nhập siêu dữ liệu người dùng không thành công."

#: includes/admin/importers/class-status.php:128
msgid "Term meta import failed."
msgstr "Nhập siêu dữ liệu thuật ngữ không thành công."

#. translators: start, end, total
#: includes/admin/importers/class-status.php:115
msgid "Imported user meta for users %1$s - %2$s out of %3$s "
msgstr "Đã nhập siêu dữ liệu người dùng cho người dùng %1$s - %2$s trên tổng số %3$s "

#. translators: total
#: includes/admin/importers/class-status.php:113
msgid "Imported term meta for %s terms."
msgstr "Đã nhập siêu dữ liệu thuật ngữ cho %s thuật ngữ."

#: includes/admin/importers/class-status.php:109
msgid "Plugin deactivated successfully."
msgstr "Đã tắt plugin thành công."

#: includes/admin/importers/class-status.php:106
msgid "Settings imported successfully."
msgstr "Đã nhập cài đặt thành công."

#: includes/admin/importers/abstract-importer.php:200
msgid "Unable to perform action this time."
msgstr "Không thể thực hiện hành động này vào lúc này."

#: includes/admin/importers/abstract-importer.php:151
msgid "Import meta information like titles, descriptions, focus keyword, robots meta, etc., of your author archive pages."
msgstr "Nhập thông tin meta như tiêu đề, mô tả, từ khóa chính, robots meta, v.v. của các trang lưu trữ tác giả của bạn."

#: includes/admin/importers/abstract-importer.php:148
msgid "Import plugin settings, global meta, sitemap settings, etc."
msgstr "Nhập cài đặt plugin, meta toàn cầu, cài đặt sơ đồ trang web, v.v."

#: includes/admin/importers/abstract-importer.php:148
#: includes/admin/importers/class-aioseo.php:67
#: includes/admin/importers/class-wp-schema-pro.php:600
#: includes/admin/views/import-export/import-export-panel.php:27
msgid "Import Settings"
msgstr "Nhập Cài Đặt"

#: includes/admin/wizard/views/header.php:19
msgid "Setup Wizard - Rank Math"
msgstr "Trình hướng dẫn cài đặt - Rank Math"

#: includes/admin/wizard/class-schema-markup.php:84
#: includes/settings/titles/post-types.php:216
msgid "News Article"
msgstr "Tin tức"

#: includes/admin/wizard/class-schema-markup.php:83
#: includes/settings/titles/post-types.php:215
msgid "Blog Post"
msgstr "Bài viết blog"

#: includes/admin/wizard/class-schema-markup.php:82
#: includes/helpers/class-choices.php:436 includes/helpers/class-schema.php:86
#: includes/settings/titles/post-types.php:167
#: includes/settings/titles/post-types.php:214
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Article"
msgstr "Bài viết"

#. translators: Google article snippet doc link
#: includes/admin/wizard/class-schema-markup.php:75
#: includes/settings/titles/post-types.php:207
msgid "Google does not allow Person as the Publisher for articles. Organization will be used instead. You can read more about this <a href=\"%s\" target=\"_blank\">here</a>."
msgstr "Google không cho phép Cá nhân là Nhà xuất bản cho các bài viết. Thay vào đó, Tổ chức sẽ được sử dụng. Bạn có thể đọc thêm về điều này <a href=\"%s\" target=\"_blank\">tại đây</a>."

#: includes/admin/wizard/class-schema-markup.php:168
#: includes/settings/titles/post-types.php:144
msgid "None (Click here to set one)"
msgstr "Không có (Nhấp vào đây để đặt)"

#. translators: link to title setting screen
#: includes/admin/wizard/class-schema-markup.php:159
#: includes/settings/titles/post-types.php:133
msgid "Default rich snippet selected when creating a new product."
msgstr "Đoạn trích phong phú mặc định được chọn khi tạo sản phẩm mới."

#: includes/admin/wizard/class-monitor-redirection.php:85
msgid "Set up temporary or permanent redirections. Combined with the 404 monitor, you can easily redirect faulty URLs on your site, or add custom redirections."
msgstr "Thiết lập chuyển hướng tạm thời hoặc vĩnh viễn. Kết hợp với trình giám sát 404, bạn có thể dễ dàng chuyển hướng các URL bị lỗi trên trang web của mình hoặc thêm chuyển hướng tùy chỉnh."

#. translators: Link to kb article
#: includes/admin/wizard/class-monitor-redirection.php:66
msgid "The 404 monitor will let you see if visitors or search engines bump into any <code>404 Not Found</code> error while browsing your site."
msgstr "Trình giám sát 404 sẽ cho phép bạn xem liệu khách truy cập hoặc công cụ tìm kiếm có gặp phải lỗi <code>404 Không tìm thấy</code> nào khi duyệt trang web của bạn hay không."

#: includes/admin/wizard/class-monitor-redirection.php:55
msgid "Set default values for the 404 error monitor here."
msgstr "Đặt các giá trị mặc định cho trình giám sát lỗi 404 tại đây."

#: includes/admin/wizard/class-monitor-redirection.php:36
#: includes/admin/wizard/class-optimization.php:46
#: includes/admin/wizard/class-role.php:41
#: includes/admin/wizard/class-schema-markup.php:41
#: includes/admin/wizard/class-search-console.php:51
#: includes/admin/wizard/class-sitemap.php:49
#: includes/admin/wizard/views/your-site.php:65
msgid "Save and Continue"
msgstr "Lưu và tiếp tục"

#: includes/admin/wizard/class-role.php:34
msgid "Set capabilities here."
msgstr "Đặt khả năng tại đây."

#: includes/admin/class-setup-wizard.php:140
#: includes/admin/wizard/class-role.php:33
#: includes/admin/wizard/class-role.php:58
#: includes/module/class-manager.php:225
#: includes/modules/role-manager/class-capability-manager.php:62
#: includes/modules/role-manager/class-role-manager.php:60
#: includes/modules/role-manager/assets/js/role-manager.js:1
msgid "Role Manager"
msgstr "Trình quản lý vai trò"

#: includes/settings/general/links.php:18
msgid "Strip Category Base"
msgstr "Loại bỏ cơ sở danh mục"

#: includes/admin/wizard/class-optimization.php:85
#: includes/settings/general/links.php:109
msgid "Open External Links in New Tab/Window"
msgstr "Mở liên kết bên ngoài trong tab / cửa sổ mới"

#: includes/settings/general/links.php:71
msgid "Automatically add <code>rel=\"nofollow\"</code> attribute for links pointing to external image files. The attribute is dynamically applied when the content is displayed, and the stored content is not changed."
msgstr "Tự động thêm thuộc tính <code>rel=\"nofollow\"</code> cho các liên kết trỏ đến các tệp hình ảnh bên ngoài. Thuộc tính được áp dụng động khi nội dung được hiển thị và nội dung được lưu trữ không bị thay đổi."

#: includes/admin/wizard/class-optimization.php:63
#: includes/settings/titles/global.php:40
msgid "Noindex Empty Category and Tag Archives"
msgstr "Không lập chỉ mục Danh mục trống và Lưu trữ thẻ"

#: includes/admin/wizard/class-sitemap.php:106
msgid "Select taxonomies to enable SEO options for them and include them in the sitemap."
msgstr "Chọn phân loại để bật tùy chọn SEO cho chúng và đưa chúng vào sơ đồ trang web."

#: includes/admin/wizard/class-sitemap.php:105
msgid "Public Taxonomies"
msgstr "Phân loại công khai"

#: includes/admin/class-setup-wizard.php:124
#: includes/admin/wizard/class-sitemap.php:66
#: includes/modules/seo-analysis/seo-analysis-tests.php:133
msgid "Sitemaps"
msgstr "Sơ đồ trang web"

#: includes/admin/wizard/views/search-console-ui.php:128
#: includes/modules/analytics/google/class-permissions.php:107
#: includes/modules/seo-analysis/seo-analysis-tests.php:114
msgid "Search Console"
msgstr "Search Console"

#: includes/admin/wizard/class-your-site.php:116
msgid "Default Social Share Image"
msgstr "Hình ảnh chia sẻ xã hội mặc định"

#: includes/admin/wizard/class-your-site.php:105
msgid "Logo for Google"
msgstr "Logo cho Google"

#: includes/admin/wizard/class-your-site.php:60
msgid "Select the type that best describes your business. If you can't find one that applies exactly, use the generic \"Organization\" or \"Local Business\" types."
msgstr "Chọn loại mô tả rõ nhất về doanh nghiệp của bạn. Nếu bạn không thể tìm thấy loại nào áp dụng chính xác, hãy sử dụng loại chung chung là \"Tổ chức\" hoặc \"Doanh nghiệp địa phương\"."

#. translators: start, end, total
#: includes/admin/importers/class-status.php:111
msgid "Imported post meta for posts %1$s - %2$s out of %3$s "
msgstr "Đã nhập meta bài viết cho bài viết %1$s - %2$s trong tổng số %3$s "

#: includes/admin/wizard/class-monitor-redirection.php:55
#: includes/admin/wizard/class-monitor-redirection.php:64
#: includes/module/class-manager.php:107
#: includes/modules/404-monitor/class-admin.php:143
#: includes/modules/404-monitor/class-admin.php:197
#: includes/modules/404-monitor/class-monitor.php:66
#: includes/modules/404-monitor/class-monitor.php:97
#: includes/modules/404-monitor/views/help-tab-overview.php:26
msgid "404 Monitor"
msgstr "Giám sát lỗi 404"

#: includes/settings/titles/global.php:98
msgid "Card type selected when creating a new post. This will also be applied for posts without a card type selected."
msgstr "Loại thẻ được chọn khi tạo bài viết mới. Điều này cũng sẽ được áp dụng cho các bài viết mà không có loại thẻ được chọn."

#: includes/settings/titles/global.php:18
msgid "Default values for robots meta tag. These can be changed for individual posts, taxonomies, etc."
msgstr "Giá trị mặc định cho thẻ meta robots. Những thứ này có thể được thay đổi cho các bài viết, phân loại, v.v. riêng lẻ"

#: includes/settings/general/rss-vars-table.php:30
msgid "A link to the post, with the title as anchor text."
msgstr "Liên kết đến bài viết, với tiêu đề làm văn bản neo."

#: includes/settings/general/rss-vars-table.php:26
msgid "A link to the archive for the post author, with the authors name as anchor text."
msgstr "Liên kết đến kho lưu trữ cho tác giả bài viết, với tên tác giả làm văn bản neo."

#: includes/settings/general/others.php:110
msgid "Add content after each post in your site feeds."
msgstr "Thêm nội dung sau mỗi bài viết trong nguồn cấp dữ liệu trang web của bạn."

#: includes/settings/general/others.php:101
msgid "Add content before each post in your site feeds."
msgstr "Thêm nội dung trước mỗi bài viết trong nguồn cấp dữ liệu trang web của bạn."

#: includes/settings/general/links.php:49
msgid "Redirect attachments without a parent post to this URL. Leave empty for no redirection."
msgstr "Chuyển hướng các tệp đính kèm không có bài viết cha mẹ đến URL này. Để trống để không chuyển hướng."

#. translators: Link to kb article
#: includes/settings/general/links.php:38
msgid "Redirect all attachment page URLs to the post they appear in. For more advanced redirection control, use the built-in %s."
msgstr "Chuyển hướng tất cả URL trang tệp đính kèm đến bài viết mà chúng xuất hiện. Để kiểm soát chuyển hướng nâng cao hơn, hãy sử dụng %s tích hợp."

#: includes/settings/general/links.php:29
msgid "Please enable Redirections module."
msgstr "Vui lòng bật module Chuyển hướng."

#: includes/settings/general/breadcrumbs.php:130
msgid "Hide Post Title"
msgstr "Ẩn tiêu đề bài viết"

#: includes/modules/sitemap/settings/taxonomies.php:46
msgid "Include archive pages of terms that have no posts associated."
msgstr "Bao gồm các trang lưu trữ của các thuật ngữ không có bài viết nào được liên kết."

#: includes/modules/sitemap/settings/post-types.php:62
msgid "Insert custom field (post meta) names which contain image URLs to include them in the sitemaps. Add one per line."
msgstr "Chèn tên trường tùy chỉnh (meta bài viết) chứa URL hình ảnh để bao gồm chúng trong sơ đồ trang web. Thêm một tên mỗi dòng."

#: includes/modules/sitemap/settings/general.php:63
msgid "Exclude Posts"
msgstr "Loại trừ bài viết"

#: includes/modules/sitemap/settings/general.php:53
msgid "Include the Featured Image too, even if it does not appear directly in the post content."
msgstr "Bao gồm cả Hình ảnh nổi bật, ngay cả khi nó không xuất hiện trực tiếp trong nội dung bài viết."

#: includes/modules/sitemap/settings/general.php:43
msgid "Include reference to images from the post content in sitemaps. This helps search engines index the important images on your pages."
msgstr "Bao gồm tham chiếu đến hình ảnh từ nội dung bài viết trong sơ đồ trang web. Điều này giúp các công cụ tìm kiếm lập chỉ mục các hình ảnh quan trọng trên trang của bạn tốt hơn."

#. translators: post type links
#: includes/modules/seo-analysis/seo-analysis-tests.php:403
msgid "There are %s published posts where the primary focus keyword does not appear in the post title."
msgstr "Có %s bài viết đã xuất bản trong đó từ khóa trọng tâm chính không xuất hiện trong tiêu đề bài viết."

#: includes/modules/seo-analysis/seo-analysis-tests.php:382
msgid "Focus keywords appear in the titles of published posts where it is set."
msgstr "Từ khóa trọng tâm xuất hiện trong tiêu đề của các bài viết đã xuất bản nơi nó được đặt."

#: includes/modules/seo-analysis/seo-analysis-tests.php:355
msgid "All published posts have focus keywords set."
msgstr "Tất cả các bài viết đã xuất bản đều có từ khóa trọng tâm được đặt."

#: includes/modules/seo-analysis/seo-analysis-tests.php:88
msgid "Fixing the issue is simple - just edit the post/page and add the focus keyword(s) to the title."
msgstr "Việc khắc phục sự cố rất đơn giản - chỉ cần chỉnh sửa bài viết/trang và thêm từ khóa trọng tâm vào tiêu đề."

#: includes/modules/seo-analysis/seo-analysis-tests.php:87
msgid "HTML Page Titles play a large role in Google's ranking algorithm.  When you add a Focus Keyword to a post or page, Rank Math will check to see that you used the keyword in the title.  If it finds any posts or pages that are missing the keyword in the title, it will tell you here."
msgstr "Tiêu đề trang HTML đóng một vai trò lớn trong thuật toán xếp hạng của Google.  Khi bạn thêm Từ khóa trọng tâm vào bài viết hoặc trang, Rank Math sẽ kiểm tra xem bạn đã sử dụng từ khóa đó trong tiêu đề hay chưa.  Nếu nó tìm thấy bất kỳ bài viết hoặc trang nào thiếu từ khóa trong tiêu đề, nó sẽ cho bạn biết ở đây."

#: includes/modules/seo-analysis/seo-analysis-tests.php:86
msgid "Make sure the focus keywords you set for the posts appear in their titles."
msgstr "Đảm bảo rằng các từ khóa trọng tâm bạn đặt cho các bài viết xuất hiện trong tiêu đề của chúng."

#: includes/modules/seo-analysis/seo-analysis-tests.php:85
msgid "Post Titles Missing Focus Keywords"
msgstr "Tiêu đề bài viết thiếu từ khóa trọng tâm"

#: includes/modules/seo-analysis/seo-analysis-tests.php:79
msgid "Fixing this issue is easy - just edit the post, and set a Focus Keyword.  Then follow Rank Math's analysis to improve your rankings."
msgstr "Việc khắc phục sự cố này rất dễ dàng - chỉ cần chỉnh sửa bài viết và đặt Từ khóa trọng tâm.  Sau đó, hãy làm theo phân tích của Rank Math để cải thiện thứ hạng của bạn."

#: includes/modules/seo-analysis/seo-analysis-tests.php:76
msgid "Rank Math allows you to set a focus keyword for every post and page you write - the option is in the \"Meta Box\", which appears under the text editor in the screen where you write and edit content."
msgstr "Rank Math cho phép bạn đặt một từ khóa trọng tâm cho mọi bài viết và trang bạn viết - tùy chọn nằm trong \"Meta Box\", xuất hiện bên dưới trình soạn thảo văn bản trong màn hình nơi bạn viết và chỉnh sửa nội dung."

#: includes/modules/seo-analysis/seo-analysis-tests.php:75
msgid "Setting focus keywords for your posts allows Rank Math to analyse the content."
msgstr "Việc đặt từ khóa trọng tâm cho bài viết của bạn cho phép Rank Math phân tích nội dung."

#: includes/modules/seo-analysis/seo-analysis-tests.php:67
msgid "This option will replace the \"?p=99\" part of the URL with the post's title, like this: http://www.yoursite.com/my-amazing-post-title/"
msgstr "Tùy chọn này sẽ thay thế phần \"?p=99\" của URL bằng tiêu đề bài viết, như thế này: http://www.yoursite.com/my-amazing-post-title/"

#: includes/modules/seo-analysis/seo-analysis-tests.php:63
msgid "The standard permalink structure is pretty ugly - WordPress generates offputting URLs like: http://www.yoursite.com/?p=99"
msgstr "Cấu trúc liên kết cố định tiêu chuẩn khá xấu xí - WordPress tạo ra các URL khó chịu như: http://www.yoursite.com/?p=99"

#: includes/modules/seo-analysis/seo-analysis-tests.php:60
msgid "Permalink Structure"
msgstr "Cấu trúc Liên kết cố định"

#. translators: link to general setting screen
#: includes/modules/seo-analysis/seo-analysis-tests.php:33
msgid "Your theme may display the Site Tagline, and it can also be used in SEO titles &amp; descriptions. Set it to something unique. You can change it by navigating to <a href=\"%s\">Settings &gt; General</a>."
msgstr "Giao diện của bạn có thể hiển thị Dòng giới thiệu trang và nó cũng có thể được sử dụng trong tiêu đề và mô tả SEO. Đặt nó thành một cái gì đó độc đáo. Bạn có thể thay đổi nó bằng cách điều hướng đến <a href=\"%s\">Cài đặt&gt; Chung</a> ."

#: includes/modules/redirections/views/options.php:62
msgid "Extend the functionality of WordPress by creating redirects in our plugin when you change the slug of a post, page, category or a CPT. You can modify the redirection further according to your needs."
msgstr "Mở rộng chức năng của WordPress bằng cách tạo chuyển hướng trong plugin của chúng tôi khi bạn thay đổi slug của bài viết, trang, danh mục hoặc CPT. Bạn có thể sửa đổi chuyển hướng thêm nữa theo nhu cầu của bạn."

#: includes/modules/redirections/views/options.php:61
msgid "Auto Post Redirect"
msgstr "Tự động chuyển hướng bài viết"

#. translators: Link to kb article
#: includes/modules/image-seo/class-admin.php:47
msgid "SEO options related to featured images and media appearing in your post content. %s."
msgstr "Các tùy chọn SEO liên quan đến hình ảnh nổi bật và phương tiện xuất hiện trong nội dung bài viết của bạn. %s."

#: includes/replace-variables/class-post-variables.php:266
msgid "Output list of tags associated to the current post, with customization options."
msgstr "Xuất danh sách thẻ được liên kết với bài viết hiện tại, với các tùy chọn tùy chỉnh."

#: includes/replace-variables/class-post-variables.php:213
msgid "Output list of categories associated to the current post, with customization options."
msgstr "Xuất danh sách danh mục được liên kết với bài viết hiện tại, với các tùy chọn tùy chỉnh."

#: includes/replace-variables/class-author-variables.php:47
msgid "Author's user ID of the current post, page or author archive."
msgstr "ID người dùng của tác giả của bài viết, trang hoặc kho lưu trữ tác giả hiện tại."

#: includes/replace-variables/class-author-variables.php:80
msgid "Author's biographical info of the current post, page or author archive."
msgstr "Thông tin tiểu sử của tác giả của bài viết, trang hoặc kho lưu trữ tác giả hiện tại."

#: includes/replace-variables/class-author-variables.php:58
#: includes/replace-variables/class-author-variables.php:69
msgid "Display author's nicename of the current post, page or author archive."
msgstr "Hiển thị tên người dùng của tác giả của bài viết, trang hoặc kho lưu trữ tác giả hiện tại."

#: includes/replace-variables/class-post-variables.php:202
msgid "Comma-separated list of categories associated to the current post"
msgstr "Danh sách các danh mục được liên kết với bài viết hiện tại, được phân tách bằng dấu phẩy"

#: includes/replace-variables/class-post-variables.php:191
msgid "First category (alphabetically) associated to the current post <strong>OR</strong> current category on category archives"
msgstr "Danh mục đầu tiên (theo thứ tự bảng chữ cái) được liên kết với bài viết hiện tại <strong>HOẶC</strong> danh mục hiện tại trên kho lưu trữ danh mục"

#: includes/replace-variables/class-post-variables.php:254
msgid "Comma-separated list of tags associated to the current post"
msgstr "Danh sách các thẻ được liên kết với bài viết hiện tại, được phân tách bằng dấu phẩy"

#: includes/replace-variables/class-post-variables.php:242
msgid "First tag (alphabetically) associated to the current post <strong>OR</strong> current tag on tag archives"
msgstr "Thẻ đầu tiên (theo thứ tự bảng chữ cái) được liên kết với bài viết hiện tại <strong>HOẶC</strong> thẻ hiện tại trên kho lưu trữ thẻ"

#: includes/replace-variables/class-post-variables.php:70
msgid "Excerpt of the current post (without auto-generation)"
msgstr "Đoạn trích của bài viết hiện tại (không tự động tạo)"

#: includes/replace-variables/class-post-variables.php:58
msgid "Excerpt of the current post (or auto-generated if it does not exist)"
msgstr "Đoạn trích của bài viết hiện tại (hoặc được tạo tự động nếu nó không tồn tại)"

#: includes/module/class-manager.php:338
msgid "Please activate WooCommerce plugin to use this module."
msgstr "Vui lòng kích hoạt plugin WooCommerce để sử dụng module này."

#. translators: Link to How to Setup Sitemap KB article
#: includes/admin/wizard/class-sitemap.php:38
msgid "Choose your Sitemap configuration and select which type of posts or pages you want to include in your Sitemaps. %s"
msgstr "Chọn cấu hình Sơ đồ trang web của bạn và chọn loại bài viết hoặc trang bạn muốn đưa vào Sơ đồ trang web của mình. %s"

#: includes/admin/importers/class-aioseo.php:68
msgid "Import meta information of your posts/pages like the titles, descriptions, robots meta, OpenGraph info, etc."
msgstr "Nhập siêu dữ liệu của bài viết/trang của bạn như tiêu đề, mô tả, siêu dữ liệu rô bốt, thông tin OpenGraph, v.v."

#: includes/admin/importers/class-status.php:127
msgid "Posts meta import failed."
msgstr "Nhập siêu dữ liệu bài viết không thành công."

#: includes/admin/importers/abstract-importer.php:149
msgid "Import meta information of your posts/pages like the focus keyword, titles, descriptions, robots meta, OpenGraph info, etc."
msgstr "Nhập thông tin meta của bài viết / trang của bạn như từ khóa chính, tiêu đề, mô tả, robots meta, thông tin OpenGraph, v.v."

#: includes/admin/wizard/class-schema-markup.php:184
msgid "Default rich snippet selected when creating a new post of this type. "
msgstr "Đoạn trích phong phú mặc định được chọn khi tạo một bài viết mới thuộc loại này. "

#: includes/admin/wizard/class-schema-markup.php:59
msgid "Use automatic structured data to mark up content, to help Google better understand your content's context for display in Search. You can set different defaults for your posts here."
msgstr "Sử dụng dữ liệu có cấu trúc tự động để đánh dấu nội dung, giúp Google hiểu rõ hơn ngữ cảnh nội dung của bạn để hiển thị trong Tìm kiếm. Bạn có thể đặt các mặc định khác nhau cho bài viết của mình tại đây."

#: includes/admin/wizard/class-monitor-redirection.php:76
msgid "Set default values for the redirection module from here."
msgstr "Đặt các giá trị mặc định cho module chuyển hướng từ đây."

#: includes/admin/wizard/class-optimization.php:64
#: includes/settings/titles/global.php:41
msgid "Setting empty archives to <code>noindex</code> is useful for avoiding indexation of thin content pages and dilution of page rank. As soon as a post is added, the page is updated to <code>index</code>."
msgstr "Đặt kho lưu trữ trống thành <code>noindex</code> rất hữu ích để tránh lập chỉ mục cho các trang nội dung mỏng và giảm giá trị trang. Ngay sau khi bài viết được thêm vào, trang được cập nhật thành <code>index</code>."

#: includes/admin/wizard/class-sitemap.php:77
msgid "Include reference to images from the post content in sitemaps. This helps search engines index your images better."
msgstr "Bao gồm tham chiếu đến hình ảnh từ nội dung bài viết trong sơ đồ trang web. Điều này giúp các công cụ tìm kiếm lập chỉ mục hình ảnh của bạn tốt hơn."

#. translators: permalink structure
#: includes/modules/seo-analysis/seo-analysis-tests.php:279
msgid "Post permalink structure is set to %s."
msgstr "Cấu trúc liên kết cố định bài viết được đặt thành %s."

#: includes/modules/seo-analysis/seo-analysis-tests.php:272
msgid "Permalinks are set to a custom structure but the post titles do not appear in the permalinks."
msgstr "Liên kết cố định được đặt thành cấu trúc tùy chỉnh nhưng tiêu đề bài viết không xuất hiện trong liên kết cố định."

#: includes/modules/seo-analysis/seo-analysis-tests.php:265
msgid "Permalinks are set to the default value. <em>Pretty permalinks</em> are disabled. "
msgstr "Liên kết cố định được đặt thành giá trị mặc định. <em>Liên kết cố định đẹp</em> bị vô hiệu hóa. "

#. translators: link to permalink setting screen
#: includes/modules/seo-analysis/seo-analysis-tests.php:66
msgid "Fortunately, it's very easy to fix.  Just hop on over to <a target=\"_blank\" href=\"%1$s\">Settings - Permalinks</a>.  Then chose the \"Post Name\" option."
msgstr "May mắn thay, việc sửa chữa rất dễ dàng.  Chỉ cần chuyển đến <a target=\"_blank\" href=\"%1$s\">Cài đặt - Liên kết cố định</a>.  Sau đó chọn tùy chọn \"Tên bài viết\"."

#. translators: link to permalink setting screen
#: includes/modules/seo-analysis/seo-analysis-tests.php:62
msgid "For the best SEO results, use a custom permalink structure, preferably one that includes the post title (<code>%%postname%%</code>). You can change it by navigating to <a href=\"%s\">Settings &gt; Permalinks</a>"
msgstr "Để có kết quả SEO tốt nhất, hãy sử dụng cấu trúc liên kết cố định tùy chỉnh, tốt nhất là cấu trúc bao gồm tiêu đề bài viết ( <code>%%postname%%</code> ). Bạn có thể thay đổi nó bằng cách điều hướng đến <a href=\"%s\">Cài đặt&gt; Liên kết cố định</a>"

#: includes/modules/seo-analysis/seo-analysis-tests.php:34
msgid "Most WordPress themes place your site's tagline in a prominent position (inside header tags near the top of the page).  Using the right tagline can give your site an SEO boost."
msgstr "Hầu hết các giao diện WordPress đều đặt khẩu hiệu trang web của bạn ở vị trí nổi bật (bên trong thẻ tiêu đề gần đầu trang).  Sử dụng khẩu hiệu phù hợp có thể giúp trang web của bạn tăng cường SEO."

#: includes/admin/class-option-center.php:139
msgid "Global Meta"
msgstr "Meta toàn trang"

#: includes/admin/class-option-center.php:145
#: includes/module/class-manager.php:116
msgid "Local SEO"
msgstr "SEO Local"

#: includes/admin/class-option-center.php:152
msgid "Social Meta"
msgstr "Meta mạng xã hội"

#: includes/admin/class-option-center.php:173
msgid "Misc Pages"
msgstr "Các trang khác"

#: includes/settings/general/webmaster.php:29
msgid "Bing Webmaster Tools"
msgstr "Bing Webmaster Tools"

#. translators: Baidu webmaster link
#: includes/settings/general/webmaster.php:41
#: includes/settings/general/webmaster.php:43
msgid "Baidu Webmaster Tools"
msgstr "Baidu Webmaster Tools"

#: includes/admin/wizard/class-optimization.php:74
#: includes/settings/general/links.php:59
msgid "Nofollow External Links"
msgstr "Liên kết Nofollow bên ngoài"

#: includes/admin/wizard/views/ready.php:69
msgid "Proceed to Help Page"
msgstr "Tiếp tục đến trang hỗ trợ"

#: includes/admin/views/dashboard-help.php:106
msgid "Direct help from our qualified support team"
msgstr "Hỗ trợ trực tiếp từ nhóm hỗ trợ đủ điều kiện của chúng tôi"

#: includes/admin/class-setup-wizard.php:114
msgid "Your Site"
msgstr "Website của bạn"

#: includes/admin/class-admin-header.php:52
#: includes/admin/wizard/views/ready.php:58
msgid "Rank Math Knowledge Base"
msgstr "Tài liệu hướng dẫn Rank Math"

#: includes/admin/wizard/views/ready.php:70
msgid "Setup Advanced Options"
msgstr "Thiết lập tùy chọn nâng cao"

#: includes/admin/class-setup-wizard.php:119
#: includes/admin/wizard/views/search-console-ui.php:186
#: includes/module/class-manager.php:234
#: includes/modules/analytics/class-analytics-common.php:72
#: includes/modules/analytics/class-analytics-common.php:143
#: includes/modules/analytics/class-analytics.php:75
#: includes/modules/analytics/class-analytics.php:466
#: includes/modules/analytics/class-analytics.php:499
#: includes/modules/role-manager/class-capability-manager.php:63
#: includes/modules/analytics/assets/js/stats.js:1
msgid "Analytics"
msgstr "Phân tích"

#: includes/admin/class-admin-bar-menu.php:333
msgid "As Pillar Content"
msgstr "Dưới dạng nội dung cốt lõi"

#: includes/admin/importers/abstract-importer.php:149
#: includes/admin/importers/class-aioseo.php:68
msgid "Import Post Meta"
msgstr "Nhập Meta bài viết"

#: includes/admin/importers/abstract-importer.php:151
#: includes/admin/importers/class-aioseo.php:69
msgid "Import Author Meta"
msgstr "Nhập Meta tác giả"

#: includes/settings/titles/homepage.php:67
#: includes/settings/titles/homepage.php:82
msgid "Homepage Robots Meta"
msgstr "Meta Robots trang chủ"

#: includes/admin/importers/abstract-importer.php:150
#: includes/admin/importers/class-aioseo.php:73
msgid "Import Term Meta"
msgstr "Nhập Meta thuật ngữ"

#: includes/admin/class-admin-bar-menu.php:175
msgid "Rank Math Dashboard"
msgstr "Bảng tổng quan Rank Math"

#: includes/module/class-manager.php:235
msgid "Connect Rank Math with Google Search Console to see the most important information from Google directly in your WordPress dashboard."
msgstr "Kết nối Rank Math với Google Search Console để xem thông tin quan trọng nhất từ ​​Google trực tiếp trong bảng tin WordPress của bạn."

#: includes/admin/class-post-columns.php:239
#: includes/replace-variables/class-advanced-variables.php:44
#: assets/admin/js/rank-math-app.js:1
#: includes/modules/content-ai/assets/js/content-ai-page.js:1
#: includes/modules/content-ai/assets/js/content-ai.js:1
msgid "Focus Keyword"
msgstr "Từ khóa chính"

#: includes/replace-variables/class-advanced-variables.php:45
msgid "Focus Keyword of the current post"
msgstr "Từ khoá chính của bài viết hiện tại"

#: includes/admin/class-option-center.php:227
#: includes/modules/sitemap/class-admin.php:146
msgid "Post Types:"
msgstr "Loại nội dung:"

#: includes/admin/wizard/class-sitemap.php:90
msgid "Public Post Types"
msgstr "Các loại nội dung công khai"

#: includes/admin/wizard/class-sitemap.php:91
msgid "Select post types to enable SEO options for them and include them in the sitemap."
msgstr "Chọn loại nội dung để bật tùy chọn SEO cho chúng và đưa chúng vào sơ đồ trang web."

#: includes/modules/sitemap/settings/post-types.php:35
msgid "Include this post type in the XML sitemap."
msgstr "Bao gồm loại nội dung này trong sơ đồ trang web XML."

#: includes/settings/general/links.php:110
msgid "Automatically add <code>target=\"_blank\"</code> attribute for external links appearing in your posts, pages, and other post types to make them open in a new browser tab or window. The attribute is dynamically applied when the content is displayed, and the stored content is not changed."
msgstr "Tự động thêm thuộc tính <code>target=\"_blank\"</code> cho các liên kết bên ngoài xuất hiện trong bài viết, trang và các loại nội dung khác của bạn để mở chúng trong tab hoặc cửa sổ trình duyệt mới. Thuộc tính được áp dụng động khi nội dung được hiển thị và nội dung được lưu trữ không bị thay đổi."

#: includes/modules/sitemap/settings/general.php:64
msgid "Enter post IDs of posts you want to exclude from the sitemap, separated by commas. This option **applies** to all posts types including posts, pages, and custom post types."
msgstr "Nhập ID bài viết của bài viết bạn muốn loại trừ khỏi sơ đồ trang web, được phân tách bằng dấu phẩy. Tùy chọn này **áp dụng** cho tất cả các loại nội dung bao gồm bài viết, trang và loại bài viết tùy chỉnh."

#. Translators: placeholder is the post type names separated with commas.
#: includes/modules/sitemap/class-sitemap.php:142
msgid "Rank Math has detected new post types: %1$s. You may want to check the settings of the <a href=\"%2$s\">Titles &amp; Meta page</a> and <a href=\"%3$s\">the Sitemap</a>."
msgstr "Rank Math đã phát hiện các loại nội dung mới: %1$s. Bạn có thể muốn kiểm tra cài đặt của trang <a href=\"%2$s\">Tiêu đề &amp; Meta</a> và <a href=\"%3$s\">Sơ đồ trang web</a> ."

#: includes/replace-variables/class-advanced-variables.php:136
msgid "Name of current post type (plural)"
msgstr "Tên loại nội dung hiện tại (số nhiều)"

#: includes/replace-variables/class-advanced-variables.php:135
msgid "Post Type Name Plural"
msgstr "Tên loại nội dung số nhiều"

#: includes/replace-variables/class-advanced-variables.php:125
msgid "Name of current post type (singular)"
msgstr "Tên loại nội dung hiện tại (số ít)"

#: includes/replace-variables/class-advanced-variables.php:124
msgid "Post Type Name Singular"
msgstr "Tên loại nội dung số ít"

#: includes/admin/class-admin-bar-menu.php:250
msgid "Edit default SEO settings for this post type"
msgstr "Chỉnh sửa cài đặt SEO mặc định cho loại nội dung này"

#: includes/admin/importers/class-aio-rich-snippet.php:192
#: includes/admin/importers/class-wp-schema-pro.php:601
msgid "Import all Schema data for Posts, Pages, and custom post types."
msgstr "Nhập tất cả dữ liệu Schema cho bài viết, trang và loại nội dung tùy chỉnh."

#: includes/admin/wizard/class-optimization.php:86
msgid "Automatically add a <code>target=\"_blank\"</code> attribute to external links appearing in your posts, pages, and other post types. The attributes are applied when the content is displayed, which does not change the stored content."
msgstr "Tự động thêm thuộc tính <code>target=\"_blank\"</code> vào các liên kết bên ngoài xuất hiện trong bài viết, trang và các loại nội dung khác của bạn. Các thuộc tính được áp dụng khi nội dung được hiển thị, điều này không làm thay đổi nội dung được lưu trữ."

#: includes/admin/wizard/class-optimization.php:75
#: includes/settings/general/links.php:60
msgid "Automatically add <code>rel=\"nofollow\"</code> attribute for external links appearing in your posts, pages, and other post types. The attribute is dynamically applied when the content is displayed, and the stored content is not changed."
msgstr "Tự động thêm thuộc tính <code>rel=\"nofollow\"</code> cho các liên kết bên ngoài xuất hiện trong bài viết, trang và các loại nội dung khác của bạn. Thuộc tính được áp dụng động khi nội dung được hiển thị và nội dung được lưu trữ không bị thay đổi."

#: includes/admin/class-option-center.php:66
msgid "Webmaster Tools"
msgstr "Kết nối trang web"

#: includes/settings/general/breadcrumbs.php:153
msgid "Hide Taxonomy Name from Breadcrumb."
msgstr "Ẩn tên phân loại khỏi điều hướng trang."

#: includes/settings/general/breadcrumbs.php:131
msgid "Hide Post title from Breadcrumb."
msgstr "Ẩn tiêu đề bài viết khỏi điều hướng trang."

#: includes/settings/general/breadcrumbs.php:120
msgid "Label used for 404 error item in breadcrumbs."
msgstr "Nhãn được sử dụng cho mục lỗi 404 trong điều hướng trang."

#: includes/settings/general/breadcrumbs.php:86
msgid "Prefix for the breadcrumb path."
msgstr "Tiền tố cho đường dẫn điều hướng trang."

#: includes/settings/general/breadcrumbs.php:85
msgid "Prefix Breadcrumb"
msgstr "Tiền tố điều hướng trang"

#: includes/settings/general/breadcrumbs.php:64
msgid "Label used for homepage link (first item) in breadcrumbs."
msgstr "Nhãn được sử dụng cho liên kết trang chủ (mục đầu tiên) trong điều hướng trang."

#: includes/settings/general/breadcrumbs.php:51
msgid "Display homepage breadcrumb in trail."
msgstr "Hiển thị điều hướng trang trang chủ trong đường dẫn."

#: includes/settings/general/breadcrumbs.php:38
msgid "Separator character or string that appears between breadcrumb items."
msgstr "Ký tự hoặc chuỗi phân cách xuất hiện giữa các mục điều hướng trang."

#: includes/settings/general/breadcrumbs.php:17
msgid "Turning off breadcrumbs will hide breadcrumbs inserted in template files too."
msgstr "Tắt điều hướng trang cũng sẽ ẩn điều hướng trang được chèn trong các tệp mẫu."

#: includes/settings/general/breadcrumbs.php:16
msgid "Enable breadcrumbs function"
msgstr "Bật chức năng điều hướng trang"

#: includes/admin/class-option-center.php:58
msgid "Breadcrumbs"
msgstr "Điều hướng trang"

#: includes/admin/class-option-center.php:190
msgid "Titles &amp; Meta"
msgstr "Tiêu đề & mô tả"

#: includes/admin/class-import-export.php:217
msgid "Action not allowed."
msgstr "Hành động không được phép."

#: includes/admin/class-assets.php:207
msgid "Add <code>rel=\"nofollow\"</code>"
msgstr "Thêm <code>rel=\"nofollow\"</code>"

#: includes/admin/wizard/class-your-site.php:341
msgid "Personal Blog"
msgstr "Blog cá nhân"

#: includes/modules/seo-analysis/class-seo-analyzer.php:638
#: assets/admin/js/rank-math-app.js:1
msgid "Basic SEO"
msgstr "SEO cơ bản"

#. translators: plugin url
#: includes/admin/class-assets.php:184
msgid "Thank you for using <a href=\"%s\" target=\"_blank\">Rank Math</a>"
msgstr "Cảm ơn bạn đã sử dụng <a href=\"%s\" target=\"_blank\">Rank Math</a>"

#: includes/admin/class-assets.php:201
msgid "Update"
msgstr "Cập nhật"

#: includes/modules/schema/shortcode/service.php:27
#: includes/modules/schema/shortcode/softwareapplication.php:20
#: includes/opengraph/class-slack.php:194
#: includes/opengraph/class-slack.php:209
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Price"
msgstr "Giá"

#: includes/admin/wizard/class-your-site.php:346
msgid "Other Personal Website"
msgstr "Trang web cá nhân khác"

#: includes/admin/class-admin.php:76
msgid "Twitter username (without @)"
msgstr "Tên người dùng Twitter (không có @)"

#: includes/3rdparty/divi/class-divi.php:157
#: includes/admin/class-assets.php:132
msgid "This field is required."
msgstr "Trường này là bắt buộc."

#: includes/admin/class-post-columns.php:178
#: includes/admin/class-post-columns.php:199
#: includes/admin/class-post-columns.php:250
#: includes/admin/class-post-columns.php:270
#: includes/admin/class-post-columns.php:283
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Save"
msgstr "Lưu"

#: includes/modules/404-monitor/views/options.php:33
#: assets/admin/js/rank-math-app.js:1
msgid "Advanced"
msgstr "Nâng cao"

#: includes/admin/wizard/class-your-site.php:59
#: includes/modules/local-seo/views/titles-options.php:137
msgid "Business Type"
msgstr "Loại hình kinh doanh"

#: includes/admin/wizard/class-your-site.php:344
msgid "Small Business Site"
msgstr "Trang web doanh nghiệp nhỏ"

#: includes/admin/class-import-export.php:153
msgid "Are you sure you want to delete this backup?"
msgstr "Bạn có chắc chắn muốn xóa bản sao lưu này không?"

#: includes/admin/class-import-export.php:152
msgid "Are you sure you want to restore this backup? Your current configuration will be overwritten."
msgstr "Bạn có chắc chắn muốn khôi phục bản sao lưu này không? Cấu hình hiện tại của bạn sẽ bị ghi đè."

#: includes/modules/schema/shortcode/recipe.php:48
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Calories"
msgstr "Lượng calo"

#: includes/admin/class-post-columns.php:241
msgid "Keyword"
msgstr "Từ khóa"

#: includes/admin/class-assets.php:206 assets/admin/js/gutenberg-formats.js:1
msgid "Link inserted."
msgstr "Đã chèn liên kết."

#: includes/admin/class-assets.php:205
msgid "Link selected."
msgstr "Đã chọn liên kết."

#: includes/admin/class-admin-helper.php:96
#: includes/admin/class-cmb2-fields.php:297
#: includes/admin/wizard/class-schema-markup.php:161
#: includes/helpers/class-taxonomy.php:151
#: includes/modules/schema/blocks/views/options-general.php:31
#: includes/settings/titles/post-types.php:135
#: includes/settings/titles/post-types.php:166 assets/admin/js/blocks.js:1
#: assets/admin/js/rank-math-app.js:1
#: includes/modules/analytics/assets/js/stats.js:1
#: includes/modules/schema/blocks/toc/assets/js/index.js:1
#: includes/modules/schema/blocks/toc/assets/src/toolbar.js:28
msgid "None"
msgstr "Không có"

#: includes/admin/class-options.php:156
msgid "Panel"
msgstr "Bảng điều khiển"

#: includes/admin/class-post-columns.php:114
msgid "SEO Details"
msgstr "Chi tiết SEO"

#: includes/admin/class-post-columns.php:117
#: includes/replace-variables/class-post-variables.php:81
#: includes/modules/content-ai/assets/js/content-ai-page.js:1
#: includes/modules/content-ai/assets/js/content-ai.js:1
msgid "SEO Title"
msgstr "Tiêu đề SEO"

#: includes/admin/class-import-export.php:387
#: includes/admin/class-import-export.php:392
msgid "Settings could not be imported:"
msgstr "Không thể nhập cài đặt:"

#: includes/admin/class-registration.php:315
msgid "You have successfully activated Rank Math."
msgstr "Bạn đã kích hoạt Rank Math thành công."

#. translators: sitename
#: includes/admin/wizard/class-your-site.php:49
msgid "%1$s is a&hellip;"
msgstr "%1$s là&hellip;"

#: includes/admin/class-post-filters.php:173
msgid "Articles noindexed"
msgstr "Bài viết không được lập chỉ mục"

#: includes/admin/class-assets.php:200
msgid "Insert/edit link"
msgstr "Chèn/chỉnh sửa liên kết"

#: includes/admin/class-list-table.php:34
msgid "No items found."
msgstr "Không tìm thấy mục nào."

#. Plugin Name of the plugin
#. Author of the plugin
#: rank-math.php includes/admin/class-admin-bar-menu.php:173
#: includes/admin/class-admin-menu.php:90
#: includes/admin/class-registration.php:213
#: includes/admin/metabox/class-metabox.php:325
msgid "Rank Math SEO"
msgstr "Rank Math SEO"

#: includes/admin/class-admin-menu.php:62
#: includes/admin/class-post-filters.php:168
#: includes/admin/class-registration.php:212 includes/class-cmb2.php:177
#: includes/modules/analytics/views/email-reports/header.php:38
#: includes/modules/role-manager/class-members.php:47
#: includes/modules/role-manager/class-user-role-editor.php:58
#: includes/modules/schema/class-blocks.php:70
#: includes/modules/status/class-system-status.php:129
#: assets/admin/js/blocks.js:1 assets/admin/js/gutenberg.js:1
#: assets/admin/js/rank-math-app.js:1
msgid "Rank Math"
msgstr "Rank Math"

#: includes/admin/class-admin-menu.php:116
msgid "Help &amp; Support"
msgstr "Trợ giúp &amp; hỗ trợ"

#: includes/admin/class-admin-dashboard-nav.php:121
msgid "Import &amp; Export"
msgstr "Nhập &amp; xuất"

#: includes/admin/class-registration.php:71
#: includes/admin/wizard/views/content.php:42
#: includes/admin/wizard/views/ready.php:68
msgid "Return to dashboard"
msgstr "Quay lại bảng điều khiển"

#: includes/admin/wizard/class-your-site.php:347
msgid "Other Business Website"
msgstr "Trang web doanh nghiệp khác"

#: includes/admin/wizard/class-your-site.php:345
msgid "Webshop"
msgstr "Cửa hàng trực tuyến"

#: includes/admin/wizard/class-your-site.php:343
msgid "Personal Portfolio"
msgstr "Hồ sơ cá nhân"

#: includes/admin/wizard/class-your-site.php:342
msgid "Community Blog/News Site"
msgstr "Trang web tin tức / blog cộng đồng"

#: includes/admin/wizard/class-import.php:84
msgid "Input Data From:"
msgstr "Nhập dữ liệu từ:"

#: includes/admin/class-setup-wizard.php:296
msgid "Deactivated"
msgstr "Đã tắt"

#: includes/modules/schema/shortcode/jobposting.php:90
msgid "Location"
msgstr "Địa điểm"

#: includes/opengraph/class-slack.php:227
msgid "In stock"
msgstr "Còn hàng"

#: includes/admin/class-registration.php:70
msgid "Rank Math Product Registration"
msgstr "Đăng ký Sản phẩm Rank Math"

#: includes/admin/class-registration.php:274
msgid "Next"
msgstr "Tiếp theo"

#: includes/admin/class-registration.php:274
msgid "Skip Now"
msgstr "Bỏ qua"

#: includes/admin/class-registration.php:314
msgid "Account Successfully Connected"
msgstr "Kết nối Tài khoản Thành công"

#: includes/admin/class-registration.php:299
msgid "Connect FREE Account"
msgstr "Kết nối Tài khoản MIỄN PHÍ"

#: includes/admin/class-post-columns.php:146
msgid "Alternative Text"
msgstr "Văn bản Thay thế"

#: includes/admin/class-post-columns.php:118
msgid "SEO Desc"
msgstr "Mô tả SEO"

#: includes/admin/class-post-filters.php:171
msgid "SEO Score: Bad"
msgstr "Điểm SEO: Xấu"

#: includes/admin/class-post-filters.php:169
msgid "SEO Score: Good"
msgstr "Điểm SEO: Tốt"

#: includes/admin/class-post-columns.php:179
#: includes/admin/class-post-columns.php:200
#: includes/admin/class-post-columns.php:251
#: includes/admin/class-post-columns.php:271
#: includes/admin/class-post-columns.php:284 assets/admin/js/post-list.js:1
#: includes/modules/redirections/assets/js/redirections.js:1
msgid "Cancel"
msgstr "Hủy"

#: includes/admin/class-page.php:134
msgid "$title variable required"
msgstr "Biến $title là bắt buộc"

#: includes/admin/class-page.php:130 includes/admin/class-page.php:134
msgid "Variable Required"
msgstr "Yêu cầu Biến"

#: includes/admin/class-page.php:130
msgid "$id variable required"
msgstr "Biến $id là bắt buộc"

#: includes/admin/class-admin-header.php:81
msgid "Search Options"
msgstr "Tùy chọn Tìm kiếm"

#: includes/admin/metabox/class-metabox.php:252
msgid "Click on the button to copy URL or insert link in content. You can also drag and drop links in the post content."
msgstr "Nhấp vào nút để sao chép URL hoặc chèn liên kết vào nội dung. Bạn cũng có thể kéo và thả liên kết vào nội dung bài viết."

#: includes/admin/metabox/class-metabox.php:241
#: includes/settings/titles/post-types.php:273
msgid "Link Suggestions"
msgstr "Đề xuất Liên kết"

#: includes/admin/metabox/class-post-screen.php:132
msgid "The featured image should be at least 200 by 200 pixels to be picked up by Facebook and other social media sites."
msgstr "Hình ảnh nổi bật nên có kích thước ít nhất 200 x 200 pixel để được Facebook và các trang mạng xã hội khác chọn."

#: includes/admin/class-import-export.php:366
msgid "No settings found to be imported."
msgstr "Không tìm thấy cài đặt nào để nhập."

#: includes/admin/class-import-export.php:362
msgid "Settings successfully imported. Your old configuration has been saved as a backup."
msgstr "Nhập cài đặt thành công. Cấu hình cũ của bạn đã được lưu dưới dạng bản sao lưu."

#: includes/admin/class-import-export.php:282
msgid "Backup restored successfully."
msgstr "Khôi phục bản sao lưu thành công."

#: includes/admin/class-import-export.php:279
msgid "Backup does not exist."
msgstr "Bản sao lưu không tồn tại."

#: includes/admin/class-import-export.php:275
msgid "No backup key found to restore."
msgstr "Không tìm thấy khóa sao lưu để khôi phục."

#: includes/admin/class-import-export.php:263
msgid "Backup successfully deleted."
msgstr "Xóa bản sao lưu thành công."

#: includes/admin/class-import-export.php:259
msgid "No backup key found to delete."
msgstr "Không tìm thấy khóa sao lưu để xóa."

#: includes/admin/class-import-export.php:245
msgid "Backup created successfully."
msgstr "Tạo bản sao lưu thành công."

#. translators: Backup formatted date
#. translators: Snapshot formatted date
#: includes/admin/class-import-export.php:244
#: includes/admin/views/import-export/backup-panel.php:28
msgid "Backup: %s"
msgstr "Sao lưu: %s"

#: includes/admin/class-import-export.php:237
msgid "Unable to create backup this time."
msgstr "Không thể tạo bản sao lưu vào lúc này."

#. translators: Plugin name
#: includes/admin/class-import-export.php:205
msgid "Cleanup of %s data failed."
msgstr "Dọn dẹp dữ liệu của %s không thành công."

#. translators: Plugin name
#: includes/admin/class-import-export.php:201
msgid "Cleanup of %s data successfully done."
msgstr "Dọn dẹp dữ liệu của %s thành công."

#: includes/admin/class-import-export.php:147
#: includes/admin/class-setup-wizard.php:297
msgid "Are you sure you want to import settings into Rank Math? Don't worry, your current configuration will be saved as a backup."
msgstr "Bạn có chắc chắn muốn nhập cài đặt vào Rank Math không? Đừng lo lắng, cấu hình hiện tại của bạn sẽ được lưu dưới dạng bản sao lưu."

#: includes/admin/class-cmb2-fields.php:126
msgid "On"
msgstr "Bật"

#: includes/admin/class-cmb2-fields.php:125
#: includes/modules/schema/class-admin.php:67
msgid "Off"
msgstr "Tắt"

#: includes/admin/class-admin.php:339
msgid "Insert Link in Content"
msgstr "Chèn Liên kết vào Nội dung"

#: includes/admin/class-admin.php:338
msgid "Copy Link URL to Clipboard"
msgstr "Sao chép URL Liên kết vào Bảng tạm"

#: includes/admin/class-admin-breadcrumbs.php:44
#: includes/admin/class-admin-dashboard-nav.php:97
msgid "Modules"
msgstr "Tiện ích mở rộng"

#: includes/admin/class-admin.php:77
msgid "Facebook profile URL"
msgstr "URL trang cá nhân Facebook"

#: includes/admin/class-assets.php:204
msgid "No matches found."
msgstr "Không tìm thấy kết quả phù hợp."

#: includes/admin/class-assets.php:203
msgid "(no title)"
msgstr "(không có tiêu đề)"

#: includes/admin/class-assets.php:202
msgid "Add Link"
msgstr "Thêm Liên kết"

#: includes/admin/class-admin-breadcrumbs.php:46
#: includes/admin/class-admin-dashboard-nav.php:104
#: includes/admin/class-dashboard-widget.php:179
msgid "Help"
msgstr "Hỗ trợ"

#: includes/admin/class-admin-dashboard-nav.php:111
#: includes/admin/class-setup-wizard.php:261
#: includes/admin/class-setup-wizard.php:262 rank-math.php:431
#: rank-math.php:436
msgid "Setup Wizard"
msgstr "Trình hướng dẫn cài đặt"

#: includes/admin/views/import-export/import-export-panel.php:59
#: includes/modules/role-manager/class-capability-manager.php:58
#: includes/modules/sitemap/class-admin.php:114
#: includes/modules/sitemap/class-admin.php:115
msgid "Sitemap Settings"
msgstr "Cài đặt sơ đồ trang"

#: includes/admin/class-option-center.php:106
#: includes/admin/views/import-export/import-export-panel.php:57
#: includes/modules/role-manager/class-capability-manager.php:57
msgid "General Settings"
msgstr "Cài đặt chung"

#: includes/admin/class-post-filters.php:219
msgid "Pillar Content"
msgstr "Nội dung cốt lõi"

#: includes/admin/metabox/class-metabox.php:261
msgid "We can't show any link suggestions for this post. Try selecting categories and tags for this post, and mark other posts as Pillar Content to make them show up here."
msgstr "Chúng tôi không thể hiển thị bất kỳ đề xuất liên kết nào cho bài viết này. Hãy thử chọn danh mục và thẻ cho bài viết này và đánh dấu các bài viết khác là nội dung cốt lõi để chúng hiển thị ở đây."

#: includes/admin/class-admin-bar-menu.php:183
#: includes/admin/class-admin-bar-menu.php:185
#: includes/admin/class-admin-breadcrumbs.php:32
#: includes/admin/class-admin-menu.php:106
#: includes/modules/version-control/class-version-control.php:235
#: includes/modules/analytics/assets/js/stats.js:1
msgid "Dashboard"
msgstr "Bảng tổng quan"

#: includes/admin/class-post-filters.php:172
msgid "Focus Keyword Not Set"
msgstr "Chưa đặt từ khoá chính"

#: includes/modules/sitemap/class-admin.php:83
#: assets/admin/js/rank-math-app.js:1
msgid "General"
msgstr "Tổng quan"

#: includes/admin/class-post-columns.php:145 assets/admin/js/rank-math-app.js:1
#: includes/modules/analytics/assets/js/stats.js:1
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Title"
msgstr "Tiêu đề SEO"

#: includes/admin/metabox/class-taxonomy-screen.php:140
#: includes/modules/local-seo/views/titles-options.php:64
#: includes/settings/general/rss-vars-table.php:20
#: includes/settings/titles/post-types.php:191
#: assets/admin/js/rank-math-app.js:1
#: includes/modules/schema/assets/js/schema-gutenberg.js:1
#: includes/modules/schema/assets/js/schema-template.js:1
msgid "Description"
msgstr "Thẻ mô tả"

#: includes/admin/class-admin.php:121
msgid "The canonical URL you entered does not seem to be a valid URL. Please double check it in the SEO meta box &raquo; Advanced tab."
msgstr "URL chính tắc (Canonical) mà bạn đã nhập có vẻ không phải là một URL hợp lệ. Vui lòng kiểm tra kỹ nó trong hộp meta SEO »tab Nâng cao."

#: includes/admin/class-assets.php:209
msgid "Link Title"
msgstr "Tiêu đề liên kết"