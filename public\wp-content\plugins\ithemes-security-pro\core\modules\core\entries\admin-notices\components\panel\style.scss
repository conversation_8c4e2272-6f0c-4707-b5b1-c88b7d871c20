.itsec-admin-bar-admin-notices__content.components-popover {
    .components-popover__content {
        border-color: 1px solid $border-color;
        box-shadow: $popover-shadow;
    }

    &::after {
        border-color: #E5EAEE !important;
    }
}

.itsec-admin-notice-panel {
    padding: 1em;
    background: $white;
    width: 420px;
    max-width: 100%;
    box-sizing: border-box;

    & .itsec-admin-notice-panel__header {
        border-bottom: 1px solid $light-blue;
        margin-bottom: 1em;

        h3 {
            color: $main-blue;
            text-align: center;
            margin: 1rem 0 !important;
        }

        p {
            text-align: center;
            font-style: oblique;
            margin: 1rem 0 !important;
        }
    }

    .is-mobile & {
        width: 100%;
        overflow-x: auto;
        height: 100%;

        & header {
            & h3 {
                display: none;
            }

            & p {
                margin-top: 0;
            }
        }
    }

    & .itsec-admin-notice-panel__configure-trigger.components-button {
        padding: 0;
        height: auto;
        width: auto;
        min-width: 0;
        position: absolute;
        right: 1em;
        top: 1em;
        transition: opacity 400ms;

        &:hover {
            opacity: .5;
            box-shadow: none !important;
        }

        .dashicon {
            margin: 0;
        }
    }

    &.itsec-admin-notice-panel--is-configuring .itsec-admin-notice-panel__configure-trigger.components-button {
        color: $main-blue;

        &:hover {
            color: $main-blue;
        }
    }

    & .itsec-admin-notice-panel__configure-highlighted-logs {
        border-bottom: 1px solid #7ABEED;
        margin-bottom: 1em;
        padding-bottom: 1em;

        & li {
            display: flex;
            justify-content: space-between;
            border-bottom: 1px solid #ccc;
            margin-bottom: 1em;
            padding-bottom: 1em;
            padding-top: 0;
            margin-top: 0;

            &:last-child {
                border-bottom: none;
                margin-bottom: 0;
                padding-bottom: 0;
            }

            & label {
                margin-left: calc(36px + 1em);
                font-size: 14px;
                font-weight: bold;
            }

            & .components-form-toggle.is-checked .components-form-toggle__track {
                background-color: $main-blue;
                border-color: $main-blue;
            }

            & .components-form-toggle__input:disabled + .components-form-toggle__track {
                opacity: .5;
            }
        }
    }
}
