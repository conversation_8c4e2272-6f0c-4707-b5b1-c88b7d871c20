#formatdiv, /* hide default radio button UI */
#titlewrap {
	display: none;
}
#post-body-content #postimagediv .inside p {
	text-align: center;
}

.clearfix:before, .clearfix:after { content: "\0020"; display: block; height: 0; overflow: hidden; }
.clearfix:after { clear: both; }
.clearfix { zoom: 1; }


.cf-elm-block {
	/*font-family: "Lucida Grande", Verdana, Arial, "Bitstream Vera Sans", sans-serif;*/
	margin-bottom: 18px;
}

.cf-elm-block .postbox h3 { cursor: default; }

.cf-elm-block .postbox .form-table th { width: 25%; }
.cf-elm-block .postbox .form-table label span { display: block; color: #999; margin: 5px 0 0 0; line-height: 18px; }
.cf-elm-block .postbox .form-table td input[type='text'] { width: 75%; }
.cf-elm-block .postbox .form-table td textarea { width: 100%; }

/*
.cf-elm-block label {
	color: #666;
	display: block;
	font: italic normal 12px Georgia, Serif;
	margin-bottom: 2px;
	padding-left: 8px;
	text-transform: uppercase;
	text-shadow: 0 1px 1px #fff;
}

.cf-elm-block input[type="text"],
.cf-elm-block textarea,
.cf-elm-block select,
.cf-elm-block .cf-elm-container {
	-moz-border-radius: 6px; 
	-webkit-border-radius: 6px; 
	-khtml-border-radius: 6px; 
	border-radius: 6px; 
	
	border: 1px #dfdfdf solid;
	font-family: "Lucida Grande", Verdana, Arial, "Bitstream Vera Sans", sans-serif;
}

.cf-elm-block input[type="text"],
.cf-elm-block textarea {
	font-size: 18px;
	width: 100%;
}

.cf-elm-block textarea {
	height: 161px;
	padding: 8px 11px;
}

.cf-elm-block input[type="text"] {
	padding: 5px 8px;
}

.cf-elm-block .description {
	color: #999;
	display: block;
	font-size: 11px;
	line-height: 1.6;
	padding-left: 8px;
}

.cf-elm-block .cf-elm-source {
	height: 96px;
}
*/
.cf-elm-block .cf-elm-container {
	-webkit-border-radius: 3px; 
	   -moz-border-radius: 3px; 
	        border-radius: 3px; 
	background: #f0f0f0;
	border: 1px solid #DFDFDF;
	padding: 10px;
}

.cf-elm-block .cf-elm-container p.none {
	text-align: center;
}

.cf-elm-block .cf-elm-image-gallery {
	margin: 0 0 -5px 0;
	padding: 0;
}

.cf-elm-block .cf-elm-container .gallery {
	margin: 0;
	padding-bottom: 8px;
}

.cf-elm-block .cf-elm-container .gallery li {
	display: inline-block;
	margin: 0 8px 8px 0;
	padding: 0;
}

.cf-elm-block .cf-elm-container .gallery li img {
	vertical-align: middle;
}

/* Video Field */
/*#cfpf-format-video-embed {
	height: 65px;
}*/

/* Featured Image */
#post-body-content #select-featured-image {
	text-align: center;
}
#post-body-content #select-featured-image img {
	display: block;
	margin: 0 auto 4px;
}
#post-body-content #select-featured-image a {
	float: none;
}
#post-body-content #select-featured-image a.button-secondary {
	margin-right: 10px;
}

/** tab navigation
 -------------------------------------------------- */
.cf-nav {
	/*border-bottom: 1px solid #ccc;*/
	margin: 10px 0 20px 0;
	width: 100%;
}
/*.cf-nav ul {
	list-style-type: none;
	margin: 0 10px;
	padding: 0em;
}
.cf-nav ul li,
.cf-nav ul li a {
	float: left;
	margin: 0;
}
.cf-nav ul li a {
	display: block;
	color: #999;
	background-color: #f1f1f1;
	background-image: -moz-linear-gradient(-90deg, #f9f9f9 , #ececec );
	background-image: -webkit-gradient(linear,left top,left bottom,from(#f9f9f9),to(#ececec));
	background-image: linear-gradient(top,#f9f9f9,#ececec);
	border: 1px solid #ddd;
	border-bottom: 0;
	font-size: 15px;
	font-family: Georgia,"Times New Roman","Bitstream Charter",Times,serif;
	height: 29px;
	line-height: 29px;
	margin-right: 5px;
	padding: 0 10px;
	text-decoration: none;
	text-shadow: #fff 0 1px 1px;
	-moz-border-radius-topleft: 3px;
	-webkit-border-top-left-radius: 3px;
	border-top-left-radius: 3px;
	-moz-border-radius-topright: 3px;
	-webkit-border-top-right-radius: 3px;
	border-top-right-radius: 3px;
}
.cf-nav ul li a:hover {
	color: #666;
}
.cf-nav ul li a.current {
	background: none;
	border: 1px solid #ccc;
	border-bottom: 1px solid #fff;
	color: #464646;
	margin-bottom: -1px;
}*/

